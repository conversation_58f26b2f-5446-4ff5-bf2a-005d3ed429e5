export default {
  base: {
    // ip: 'http://localhost',
    // auth_ip: 'http://localhost',
    // auth_port: ':44396',
    // identity_port: ':21001',
    // backend_port: ':21001',
    // file_ip: 'http://localhost:44327',
    // loginIP: 'http://localhost'

    // ip: 'http://qc.ciep-pimp.com',
    // auth_ip: 'http://qc.ciep-pimp.com',
    ip: 'http://qc.ciep-pimp.com',
    auth_ip: 'http://qc.ciep-pimp.com',
    auth_port: '',
    identity_port: '',
    backend_port: '',
    file_ip: 'http://qc.ciep-pimp.com',
    loginIP: 'http://qc.ciep-pimp.com'

    // ip: 'http://**************',
    // auth_ip: 'http://**************',
    // auth_port: ':44399',
    // identity_port: ':44377',
    // backend_port: ':44377',
    // file_ip: 'http://**************:44377'

  },
  client: {
    client_id: 'course-admin-client',
    client_secret: '1q2w3e*',
    grant_type: 'password'
  },
  bucketSpace: {
    // domainName: 'https://bj.bcebos.com'
    domainName: 'http://bj.bcebos.com'
  }
}
