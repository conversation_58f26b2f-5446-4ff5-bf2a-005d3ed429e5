<!--
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-07-05 08:37:19
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-08-05 15:19:59
 * @FilePath: /vue-element-cms-admin/src/views/notice/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="app-container">
    <el-card class="box-card">
      <div class="header_flex_box">
        <el-radio-group v-model="listQuery.State" size="small" @change="handleRefreshList">
          <el-radio-button :label="null">全部</el-radio-button>
          <el-radio-button :label="0">未发布</el-radio-button>
          <el-radio-button :label="1">已发布</el-radio-button>
        </el-radio-group>
        <el-input v-model="listQuery.Filter" size="small" class="small_input" clearable placeholder="输入名称搜索" />
        <el-button size="small" round type="success" icon="el-icon-search" @click="handleRefreshList">搜索</el-button>
        <el-button v-permission="['NoticeManagement.Resumes.Create']" size="small" round type="primary" icon="el-icon-plus" @click="handleResumesEdit(0, 0)">添加</el-button>
      </div>
      <el-table v-loading="listLoading" :data="list" @sort-change="sortChange">
        <el-table-column label="封面图" width="120">
          <template slot-scope="{ row }">
            <el-image v-if="row.imgUrl" :src="row.imgUrl" style="width: 80px; height: 45px" fit="cover">
              <div slot="error">
                <i class="el-icon-picture-outline" />
              </div>
            </el-image>
            <i v-else class="el-icon-picture-outline" />
          </template>
        </el-table-column>
        <el-table-column label="姓名" prop="name" sortable="name" min-width="200">
          <template slot-scope="{ row }">
            <span class="link-type" @click="handleResumesEdit(1, row)">{{ row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="职位" prop="post" sortable="post" min-width="100">
          <template slot-scope="{ row }">
            <span>{{ row.post }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="分类" prop="category" width="100">
          <template slot-scope="{ row }">
            <span>{{ getCategoryName(row.category) }}</span>
          </template>
        </el-table-column> -->
        <el-table-column label="状态" prop="state" sortable="state" width="100">
          <template slot-scope="{ row }">
            <el-tag v-if="row.state === 0" type="info" size="mini">未发布</el-tag>
            <el-tag v-if="row.state === 1" type="primary" size="mini">已发布</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="排序" prop="sort" sortable="sort" width="100">
          <template slot-scope="{ row }">
            <span>{{ row.sort }}</span>
          </template>
        </el-table-column>
        <el-table-column label="日期" prop="publishDate" sortable="publishDate" width="160">
          <template slot-scope="{ row }">
            {{ row.publishDate | formatDateTime }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="350">
          <template slot-scope="{ row }">
            <el-button
              size="mini"
              round
              type="warning"
              icon="el-icon-s-promotion"
              @click="handleResumesPublish(row)"
            >{{ row.state ? '取消发布' : '发布' }}</el-button>
            <el-button v-permission="['NoticeManagement.Resumes.Update']" size="mini" round type="primary" icon="el-icon-edit" @click="handleResumesEdit(1, row)">编辑
            </el-button>
            <el-button v-permission="['NoticeManagement.Resumes.Delete']" size="mini" round type="danger" icon="el-icon-delete" @click="handleResumesDelete(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="listQuery.totalCount > 0"
        :total="listQuery.totalCount"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.MaxResultCount"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>
<script>
import { resumesList, resumesPublish, resumesDelete } from '@/api/other'
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission'
// import ExportExcel from '@/components/ExportExcel/index.vue'
export default {
  name: 'ResumesList',
  directives: {
    permission
  },
  components: {
    Pagination
    // ExportExcel
  },
  data() {
    return {
      list: [],
      listLoading: false,
      listQuery: {
        State: null,
        // App: '',
        // Type: 1,
        Filter: '',
        Sorting: 'creationTime desc',
        SkipCount: 0,
        MaxResultCount: 10,
        page: 1,
        totalCount: 0
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    resumesList(args) {
      return resumesList(args)
    },
    handleResumesPublish(row) {
      this.$confirm('是否确定' + (!row.state ? '发布?' : '取消发布?'), '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        resumesPublish(row.id).then((res) => {
          this.$message.success((!row.state ? '发布' : '取消发布') + '成功')
          this.getList()
        }).catch(() => {
          this.$message.error((!row.state ? '发布' : '取消发布') + '失败')
        })
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },
    handleResumesEdit(t, row) {
      if (t === 0) {
        this.$router.push({
          name: 'ResumesEdit'
        })
      } else {
        const url = this.$router.resolve({
          name: 'ResumesEdit',
          query: {
            name: row.name,
            id: row.id
          }
        })
        window.open(url.href, '_blank')
      }
    },
    handleResumesDelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        resumesDelete(row.id).then((res) => {
          this.$message.success('删除成功')
          this.getList()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },
    sortChange(data) {
      const {
        prop,
        order
      } = data
      if (!prop || !order) {
        this.getList()
        return
      }
      this.listQuery.Sorting = prop + ' ' + order
      this.getList()
    },
    handleRefreshList() {
      this.listQuery.page = 1
      this.getList()
    },
    getList() {
      this.listLoading = true
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      resumesList(this.listQuery).then(res => {
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    }

  }
}
</script>
