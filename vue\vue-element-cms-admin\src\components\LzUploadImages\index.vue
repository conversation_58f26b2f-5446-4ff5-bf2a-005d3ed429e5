<template>
  <div>
    <el-upload
      :id="uploadId"
      :class="limit === 1 ? 'jgUpload' : 'imageUpload'"
      list-type="picture-card"
      action="#"
      :disabled="uploading"
      :v-loading="uploading"
      :multiple="multiple"
      :show-file-list="true"
      :http-request="baidubceUpload"
      :before-upload="handleBeforeUpload"
      :on-change="handleChange"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :limit="limit"
      :file-list="sourceList"
      :file-type="fileType"
    >
      <i slot="default" class="el-icon-plus" />
      <div slot="file" slot-scope="{ file }">
        <img
          class="el-upload-list__item-thumbnail"
          :src="handlePreviewUrl(file.url)"
          alt=""
          style="position: absolute; object-fit: cover"
        >
        <span class="el-upload-list__item-actions">
          <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
            <i class="el-icon-zoom-in" />
          </span>
          <span class="el-upload-list__item-delete" @click="handleRemove(file)">
            <i class="el-icon-delete" />
          </span>
        </span>
      </div>
    </el-upload>
    <el-image-viewer v-if="showViewer" :z-index="3000" :on-close="closeViewer" :url-list="viewerImgList" />
    <el-dialog class="preview_dialog" append-to-body :visible.sync="dialogVisible" width="60%" top="5vh">
      <preview-resource v-if="dialogVisible" ref="previewResource" :type="previewType" :url="previewUrl" />
    </el-dialog>
  </div>
</template>

<script>
import SparkMD5 from 'spark-md5'
import moment from 'moment'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import {
  butcketStsToken,
  reourceUploadPath,
  cloudResource,
  cloudResourceUrl,
  resourcePath
} from '@/api/upload'
import PreviewResource from '@/components/PreviewResource/index'
import bucketConfig from '@/config/config'
export default {
  name: 'LzUploadImages',
  components: {
    ElImageViewer,
    PreviewResource
  },
  props: {
    uploadId: {
      type: String,
      require: false,
      default: null
    },
    fileSize: {
      type: Number,
      require: true,
      default: 100
    },
    limit: {
      type: Number,
      require: false,
      default: 20
    },
    fileType: {
      type: Array,
      require: true,
      default: function() {
        return ['jpg', 'jpeg', 'png']
      }
    },
    sourceList: {
      type: Array,
      require: true,
      default: function() {
        return []
      }
    },
    multiple: {
      type: Boolean,
      require: false,
      default: true
    },
    // 图片显示大小
    width: {
      type: Number,
      default: 148
    },
    height: {
      type: Number,
      default: 148
    }
  },
  data() {
    return {
      showViewer: false,
      uploading: false,
      previewType: '',
      previewUrl: '',
      dialogVisible: false,
      stsToken: {},
      viewerImgList: []
    }
  },
  watch: {
    sourceList(newVal, oldVal) {
      this.setCssStyle()
    }
  },
  mounted() {
    this.$on('removeSuccess', (index) => {
      // this.sourceList.splice(index, 1)
    })
    this.$on('removeFail', (index) => {})
    this.setCssStyle()
  },
  created() {
    this.getstsToken()
  },

  methods: {
    getstsToken() {
      butcketStsToken()
        .then((res) => {
          this.stsToken.accessKeyId = res.accessKeyId
          this.stsToken.secretAccessKey = res.secretAccessKey
          this.stsToken.sessionToken = res.sessionToken
        })
        .catch((err) => {
          this.$message.error('获取sts失败，无法上传')
        })
    },
    // 一张图片的时候设置图片宽度
    setCssStyle() {
      if (this.limit === 1 && this.sourceList.length === 1) {
        this.$nextTick(() => {
          if (this.uploadId) {
            const str = '#' + this.uploadId + ' .el-upload--picture-card'
            document.querySelectorAll(str)[0].style.display = 'none'
          } else {
            document.querySelectorAll('.jgUpload .el-upload--picture-card')[0].style.display = 'none'
          }
        })
      } else if (this.limit === 1 && this.sourceList.length === 0) {
        this.$nextTick(() => {
          if (this.uploadId) {
            const str = '#' + this.uploadId + ' .el-upload--picture-card'
            document.querySelectorAll(str)[0].style.display = 'inline-block'
          } else {
            document.querySelectorAll('.jgUpload .el-upload--picture-card')[0].style.display = 'inline-block'
          }
        })
      }
    },
    handlePreviewUrl(url) {
      let fileExtension = ''
      if (url.lastIndexOf('.') > -1) {
        fileExtension = url.slice(url.lastIndexOf('.') + 1)
      }
      if (fileExtension === 'mp4') {
        // url = require('@/assets/image/video.png')
      }
      if (fileExtension === 'pdf') {
        // url = require('@/assets/image/pdf.png')
      }

      return url
    },
    handlePictureCardPreview(file) {
      let showDialog = false
      if (file.extend === '.mp4') {
        this.previewType = 'video'
        this.previewUrl = file.url
        showDialog = true
      }
      if (file.extend === '.pdf') {
        this.previewType = 'pdf'
        this.previewUrl = file.url
        showDialog = true
      }
      if (showDialog) {
        resourcePath({
          url: this.previewUrl
        })
          .then((res) => {
            this.previewUrl = res
            this.dialogVisible = showDialog
          })
          .catch(() => {
            this.$message.error('获取资源地址失败')
          })
        return
      }
      this.dialogVisible = showDialog
      var index = this.sourceList.indexOf(file)
      this.showViewer = true
      const tempImgList = []
      this.sourceList.forEach((item) => {
        let fileExtension = ''
        const _url = item.url
        if (_url.lastIndexOf('.') > -1) {
          fileExtension = _url.slice(_url.lastIndexOf('.') + 1)
        }
        if (fileExtension !== 'mp4' && fileExtension !== 'pdf') {
          tempImgList.push(_url)
        }
      })
      const temp = []
      for (let i = 0; i < index; i++) {
        temp.push(tempImgList.shift())
      }
      this.viewerImgList = tempImgList.concat(temp)
    },
    closeViewer() {
      this.showViewer = false
    },
    handleRemove(item, index) {
      var index = this.sourceList.indexOf(item)
      this.$emit('remove-upload', index)
    },
    // 上传大小
    handleBeforeUpload(file) {
      let fileFormat = false
      if (this.fileType.length) {
        let fileExtension = ''
        if (file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
        }
        fileFormat = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true
          if (fileExtension && fileExtension.indexOf(type) > -1) return true
          return false
        })
      } else {
        fileFormat = file.type.indexOf('image') > -1
      }
      if (!fileFormat) {
        this.$message.error(
          `文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`
        )
        return false
      }
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }
      this.long = 0
      this.uploading = true
      return true
    },
    handleChange(file, fileList) {
      fileList.splice(fileList.indexOf(file))
    },
    // 文件个数超出
    handleExceed(files, fileList) {
      this.$message.error(`最多上传${this.limit}个文件`)
    },
    // 上传失败
    handleUploadError(err) {
      this.uploading = false
      this.$message.error('上传失败, 请重试')
      this.$emit('error', err)
    },
    // 上传成功回调
    handleUploadSuccess() {
      this.uploading = false
      this.$message.success('上传成功')
    },
    // has 是否根据hash找到  url 地址  uploadForm 文件信息(has true有id )
    uploadSuccessFn(has, url, uploadForm) {
      this.uploading = false
      // this.sourceList.push(uploadForm)
      // this.value = [...this.value, url]
      // this.previewFileList = [...this.previewFileList, file]
      this.$emit('response-fn', url, uploadForm, has)
    },
    baidubceUpload(oldFile) {
      var blobSlice =
          File.prototype.slice ||
          File.prototype.mozSlice ||
          File.prototype.webkitSlice
      var newfile = oldFile.file

      var chunkSize = 2097152 // Read in chunks of 2MB
      var chunks = Math.ceil(newfile.size / chunkSize)
      var currentChunk = 0
      var spark = new SparkMD5.ArrayBuffer()
      var fileReader = new FileReader()
      const ele = this
      fileReader.onload = function(e) {
        spark.append(e.target.result) // Append array buffer
        currentChunk++
        if (currentChunk < chunks) {
          loadNext()
        } else {
          var hash = spark.end()
          var form = {
            hash: hash
          }
          // 后台请求
          cloudResourceUrl(form)
            .then((response) => {
              if (response) {
                ele.uploading = false
                var clouldForm = {
                  id: response.id,
                  hash: response.hash,
                  fileName: response.fileName,
                  fileType: response.fileType,
                  url: response.url,
                  size: response.size,
                  durationInSecond: response.durationInSecond,
                  thumbnailUrl: response.thumbnailUrl,
                  isPublic: response.isPublic,
                  recordingTime: ele.formatDateTime(new Date())
                }
                ele.uploadSuccessFn(true, response.url, clouldForm)
              } else {
                ele.uploadFile(oldFile, hash)
              }
            })
            .catch((err) => {
              this.$message.error('上传失败')
              ele.uploading = false
            })
        }
      }

      fileReader.onerror = function() {
        console.warn('oops, something went wrong.')
      }

      function loadNext() {
        var start = currentChunk * chunkSize
        var end =
            start + chunkSize >= newfile.size ? newfile.size : start + chunkSize

        fileReader.readAsArrayBuffer(blobSlice.call(newfile, start, end))
      }

      loadNext()
    },
    async uploadFile(oldFile, md5String) {
      var config = {
        credentials: {
          ak: this.stsToken.accessKeyId, // 您的AK
          sk: this.stsToken.secretAccessKey // 您的SK
        },
        sessionToken: this.stsToken.sessionToken,
        endpoint: bucketConfig.bucketSpace.domainName // 传入Bucket所在区域域名
      }
      // let bucket = 'jgcloudpublic'
      var file = oldFile.file
      let fileExtension = ''
      var name = md5String
      if (file.name.lastIndexOf('.') > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
        name = md5String
      }
      // 获取上传地址
      var pathForm = {
        extend: '.' + fileExtension,
        hash: md5String,
        isPublic: true
      }
      var bucket = ''
      var key = ''
      await reourceUploadPath(pathForm)
        .then((res) => {
          bucket = res.slice(0, res.indexOf('/'))
          key = res.slice(res.indexOf('/') + 1)
        })
        .catch((err) => {
          this.$message.error('获取上传路径失败，无法上传')
        })

      var renameFile = new File([file], name + '.' + fileExtension, {
        type: file.type
      })
      const url = config.endpoint + '/' + bucket + '/' + key
      const client = new baidubce.sdk.BosClient(config)
      var reader = new FileReader()
      reader.readAsArrayBuffer(renameFile)
      let blob = null
      const ele = this
      var options = {
        'Content-Disposition': 'attachment;filename="' + encodeURIComponent(file.name) + '"' // 指示回复的内容该以何种形式展示
      }
      reader.onload = function(e) {
        if (typeof e.target.result === 'object') {
          blob = new Blob([e.target.result])
        } else {
          blob = e.target.result
        }

        client
          .putObjectFromBlob(bucket, key, blob, options)
          .then((response) => {
            ele.uploading = false
            // var form = {
            //   url: url
            // }
            // resourcePath(form).then(res => {
            var clouldForm = {
              hash: md5String,
              fileName: file.name.substring(0, file.name.lastIndexOf('.')),
              fileType: '.' + fileExtension,
              url: url,
              size: file.size,
              durationInSecond: 0,
              thumbnailUrl: '',
              isPublic: true,
              recordingTime: ele.formatDateTime(new Date())
            }
            //

            ele.uploadSuccessFn(false, url, clouldForm)
            cloudResource(clouldForm)
              .then((res) => {})
              .catch(() => {})
              // ele.uploadSuccessFn(res, url, clouldForm)
              // }).catch(() => {
              //   ele.$message.error('获取资源地址失败')
              // })
          })
          .catch((fail) => {
            this.$message.error('上传失败')
            ele.uploading = false
          })
      }
    },
    formatDateTime(value) {
      if (value) {
        return moment(value).format('yyyy-MM-DD HH:mm:ss')
      } else {
        return moment(new Date()).format('yyyy-MM-DD HH:mm:ss')
      }
    }
  }
}

</script>
<style scoped>
  /* .jgUpload  .el-upload-list--picture-card ::v-deep .el-upload-list__item {
    transition: none !important;
  } */

  .jgUpload ::v-deep .el-upload-list__item {
    width: 300px;
    height: 200px;
    transition: none !important;
  }

</style>
