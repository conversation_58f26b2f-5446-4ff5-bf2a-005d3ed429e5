/**
 * @license
 * Video.js 7.0.3 <http://videojs.com/>
 * Copyright Brightcove, Inc. <https://www.brightcove.com/>
 * Available under Apache License Version 2.0
 * <https://github.com/videojs/video.js/blob/master/LICENSE>
 *
 * Includes vtt.js <https://github.com/mozilla/vtt.js>
 * Available under Apache License Version 2.0
 * <https://github.com/mozilla/vtt.js/blob/master/LICENSE>
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.videojs=t()}(this,function(){var h="7.0.3",e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function t(e,t){return e(t={exports:{}},t.exports),t.exports}var i,g="undefined"!=typeof window?window:"undefined"!=typeof e?e:"undefined"!=typeof self?self:{},r={},n=Object.freeze({default:r}),a=n&&r||n,s="undefined"!=typeof e?e:"undefined"!=typeof window?window:{};"undefined"!=typeof document?i=document:(i=s["__GLOBAL_DOCUMENT_CACHE@4"])||(i=s["__GLOBAL_DOCUMENT_CACHE@4"]=a);var p=i,o=void 0,u="info",c=[],l=function(e,t){var i=o.levels[u],r=new RegExp("^("+i+")$");if("log"!==e&&t.unshift(e.toUpperCase()+":"),c&&c.push([].concat(t)),t.unshift("VIDEOJS:"),g.console){var n=g.console[e];n||"debug"!==e||(n=g.console.info||g.console.log),n&&i&&r.test(e)&&n[Array.isArray(t)?"apply":"call"](g.console,t)}};(o=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];l("log",t)}).levels={all:"debug|log|warn|error",off:"",debug:"debug|log|warn|error",info:"log|warn|error",warn:"warn|error",error:"error",DEFAULT:u},o.level=function(e){if("string"==typeof e){if(!o.levels.hasOwnProperty(e))throw new Error('"'+e+'" in not a valid log level');u=e}return u},o.history=function(){return c?[].concat(c):[]},o.history.clear=function(){c&&(c.length=0)},o.history.disable=function(){null!==c&&(c.length=0,c=null)},o.history.enable=function(){null===c&&(c=[])},o.error=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return l("error",t)},o.warn=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return l("warn",t)},o.debug=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return l("debug",t)};var f=o;var m=function(e){for(var t="",i=0;i<arguments.length;i++)t+=e[i].replace(/\n\r?\s*/g,"")+(arguments[i+1]||"");return t},v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},y=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},_=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)},b=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t},d=function(e,t){return e.raw=t,e},T=Object.prototype.toString,S=function(e){return k(e)?Object.keys(e):[]};function E(t,i){S(t).forEach(function(e){return i(t[e],e)})}function w(i){for(var e=arguments.length,t=Array(1<e?e-1:0),r=1;r<e;r++)t[r-1]=arguments[r];return Object.assign?Object.assign.apply(Object,[i].concat(t)):(t.forEach(function(e){e&&E(e,function(e,t){i[t]=e})}),i)}function k(e){return!!e&&"object"===("undefined"==typeof e?"undefined":v(e))}function C(e){return k(e)&&"[object Object]"===T.call(e)&&e.constructor===Object}function A(e,t){if(!e||!t)return"";if("function"==typeof g.getComputedStyle){var i=g.getComputedStyle(e);return i?i[t]:""}return""}var L=d(["Setting attributes in the second argument of createEl()\n                has been deprecated. Use the third argument instead.\n                createEl(type, properties, attributes). Attempting to set "," to ","."],["Setting attributes in the second argument of createEl()\n                has been deprecated. Use the third argument instead.\n                createEl(type, properties, attributes). Attempting to set "," to ","."]);function O(e){return"string"==typeof e&&/\S/.test(e)}function P(e){if(/\s/.test(e))throw new Error("class has illegal whitespace characters")}function I(){return p===g.document}function R(e){return k(e)&&1===e.nodeType}function x(r){return function(e,t){if(!O(e))return p[r](null);O(t)&&(t=p.querySelector(t));var i=R(t)?t:p;return i[r]&&i[r](e)}}function D(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"div",i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},t=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},r=arguments[3],n=p.createElement(e);return Object.getOwnPropertyNames(i).forEach(function(e){var t=i[e];-1!==e.indexOf("aria-")||"role"===e||"type"===e?(f.warn(m(L,e,t)),n.setAttribute(e,t)):"textContent"===e?U(n,t):n[e]=t}),Object.getOwnPropertyNames(t).forEach(function(e){n.setAttribute(e,t[e])}),r&&K(n,r),n}function U(e,t){return"undefined"==typeof e.textContent?e.innerText=t:e.textContent=t,e}function N(e,t){t.firstChild?t.insertBefore(e,t.firstChild):t.appendChild(e)}function M(e,t){return P(t),e.classList?e.classList.contains(t):(i=t,new RegExp("(^|\\s)"+i+"($|\\s)")).test(e.className);var i}function B(e,t){return e.classList?e.classList.add(t):M(e,t)||(e.className=(e.className+" "+t).trim()),e}function j(e,t){return e.classList?e.classList.remove(t):(P(t),e.className=e.className.split(/\s+/).filter(function(e){return e!==t}).join(" ")),e}function F(e,t,i){var r=M(e,t);if("function"==typeof i&&(i=i(e,t)),"boolean"!=typeof i&&(i=!r),i!==r)return i?B(e,t):j(e,t),e}function H(i,r){Object.getOwnPropertyNames(r).forEach(function(e){var t=r[e];null===t||"undefined"==typeof t||!1===t?i.removeAttribute(e):i.setAttribute(e,!0===t?"":t)})}function q(e){var t={},i=",autoplay,controls,playsinline,loop,muted,default,defaultMuted,";if(e&&e.attributes&&0<e.attributes.length)for(var r=e.attributes,n=r.length-1;0<=n;n--){var a=r[n].name,s=r[n].value;"boolean"!=typeof e[a]&&-1===i.indexOf(","+a+",")||(s=null!==s),t[a]=s}return t}function V(e,t){return e.getAttribute(t)}function W(e,t,i){e.setAttribute(t,i)}function G(e,t){e.removeAttribute(t)}function z(e){var t=void 0;if(e.getBoundingClientRect&&e.parentNode&&(t=e.getBoundingClientRect()),!t)return{left:0,top:0};var i=p.documentElement,r=p.body,n=i.clientLeft||r.clientLeft||0,a=g.pageXOffset||r.scrollLeft,s=t.left+a-n,o=i.clientTop||r.clientTop||0,u=g.pageYOffset||r.scrollTop,c=t.top+u-o;return{left:Math.round(s),top:Math.round(c)}}function X(e){return k(e)&&3===e.nodeType}function Y(e){for(;e.firstChild;)e.removeChild(e.firstChild);return e}function $(e){return"function"==typeof e&&(e=e()),(Array.isArray(e)?e:[e]).map(function(e){return"function"==typeof e&&(e=e()),R(e)||X(e)?e:"string"==typeof e&&/\S/.test(e)?p.createTextNode(e):void 0}).filter(function(e){return e})}function K(t,e){return $(e).forEach(function(e){return t.appendChild(e)}),t}function Q(e,t){return K(Y(e),t)}function J(e){return void 0===e.button&&void 0===e.buttons||(0===e.button&&void 0===e.buttons||0===e.button&&1===e.buttons)}var Z=x("querySelector"),ee=x("querySelectorAll"),te=Object.freeze({isReal:I,isEl:R,isInFrame:function(){try{return g.parent!==g.self}catch(e){return!0}},createEl:D,textContent:U,prependTo:N,hasClass:M,addClass:B,removeClass:j,toggleClass:F,setAttributes:H,getAttributes:q,getAttribute:V,setAttribute:W,removeAttribute:G,blockTextSelection:function(){p.body.focus(),p.onselectstart=function(){return!1}},unblockTextSelection:function(){p.onselectstart=function(){return!0}},getBoundingClientRect:function(e){if(e&&e.getBoundingClientRect&&e.parentNode){var t=e.getBoundingClientRect(),i={};return["bottom","height","left","right","top","width"].forEach(function(e){void 0!==t[e]&&(i[e]=t[e])}),i.height||(i.height=parseFloat(A(e,"height"))),i.width||(i.width=parseFloat(A(e,"width"))),i}},findPosition:z,getPointerPosition:function(e,t){var i={},r=z(e),n=e.offsetWidth,a=e.offsetHeight,s=r.top,o=r.left,u=t.pageY,c=t.pageX;return t.changedTouches&&(c=t.changedTouches[0].pageX,u=t.changedTouches[0].pageY),i.y=Math.max(0,Math.min(1,(s-u+a)/a)),i.x=Math.max(0,Math.min(1,(c-o)/n)),i},isTextNode:X,emptyEl:Y,normalizeContent:$,appendContent:K,insertContent:Q,isSingleLeftClick:J,$:Z,$$:ee}),ie=1;function re(){return ie++}var ne={},ae="vdata"+(new Date).getTime();function se(e){var t=e[ae];return t||(t=e[ae]=re()),ne[t]||(ne[t]={}),ne[t]}function oe(e){var t=e[ae];return!!t&&!!Object.getOwnPropertyNames(ne[t]).length}function ue(t){var e=t[ae];if(e){delete ne[e];try{delete t[ae]}catch(e){t.removeAttribute?t.removeAttribute(ae):t[ae]=null}}}function ce(e,t){var i=se(e);0===i.handlers[t].length&&(delete i.handlers[t],e.removeEventListener?e.removeEventListener(t,i.dispatcher,!1):e.detachEvent&&e.detachEvent("on"+t,i.dispatcher)),Object.getOwnPropertyNames(i.handlers).length<=0&&(delete i.handlers,delete i.dispatcher,delete i.disabled),0===Object.getOwnPropertyNames(i).length&&ue(e)}function le(t,i,e,r){e.forEach(function(e){t(i,e,r)})}function de(e){function t(){return!0}function i(){return!1}if(!e||!e.isPropagationStopped){var r=e||g.event;for(var n in e={},r)"layerX"!==n&&"layerY"!==n&&"keyLocation"!==n&&"webkitMovementX"!==n&&"webkitMovementY"!==n&&("returnValue"===n&&r.preventDefault||(e[n]=r[n]));if(e.target||(e.target=e.srcElement||p),e.relatedTarget||(e.relatedTarget=e.fromElement===e.target?e.toElement:e.fromElement),e.preventDefault=function(){r.preventDefault&&r.preventDefault(),e.returnValue=!1,r.returnValue=!1,e.defaultPrevented=!0},e.defaultPrevented=!1,e.stopPropagation=function(){r.stopPropagation&&r.stopPropagation(),e.cancelBubble=!0,r.cancelBubble=!0,e.isPropagationStopped=t},e.isPropagationStopped=i,e.stopImmediatePropagation=function(){r.stopImmediatePropagation&&r.stopImmediatePropagation(),e.isImmediatePropagationStopped=t,e.stopPropagation()},e.isImmediatePropagationStopped=i,null!==e.clientX&&void 0!==e.clientX){var a=p.documentElement,s=p.body;e.pageX=e.clientX+(a&&a.scrollLeft||s&&s.scrollLeft||0)-(a&&a.clientLeft||s&&s.clientLeft||0),e.pageY=e.clientY+(a&&a.scrollTop||s&&s.scrollTop||0)-(a&&a.clientTop||s&&s.clientTop||0)}e.which=e.charCode||e.keyCode,null!==e.button&&void 0!==e.button&&(e.button=1&e.button?0:4&e.button?1:2&e.button?2:0)}return e}var he=!1;!function(){try{var e=Object.defineProperty({},"passive",{get:function(){he=!0}});g.addEventListener("test",null,e),g.removeEventListener("test",null,e)}catch(e){}}();var pe=["touchstart","touchmove"];function fe(s,e,t){if(Array.isArray(e))return le(fe,s,e,t);var o=se(s);if(o.handlers||(o.handlers={}),o.handlers[e]||(o.handlers[e]=[]),t.guid||(t.guid=re()),o.handlers[e].push(t),o.dispatcher||(o.disabled=!1,o.dispatcher=function(e,t){if(!o.disabled){e=de(e);var i=o.handlers[e.type];if(i)for(var r=i.slice(0),n=0,a=r.length;n<a&&!e.isImmediatePropagationStopped();n++)try{r[n].call(s,e,t)}catch(e){f.error(e)}}}),1===o.handlers[e].length)if(s.addEventListener){var i=!1;he&&-1<pe.indexOf(e)&&(i={passive:!0}),s.addEventListener(e,o.dispatcher,i)}else s.attachEvent&&s.attachEvent("on"+e,o.dispatcher)}function me(e,t,i){if(oe(e)){var r=se(e);if(r.handlers){if(Array.isArray(t))return le(me,e,t,i);var n=function(e,t){r.handlers[t]=[],ce(e,t)};if(void 0!==t){var a=r.handlers[t];if(a)if(i){if(i.guid)for(var s=0;s<a.length;s++)a[s].guid===i.guid&&a.splice(s--,1);ce(e,t)}else n(e,t)}else for(var o in r.handlers)Object.prototype.hasOwnProperty.call(r.handlers||{},o)&&n(e,o)}}}function ge(e,t,i){var r=oe(e)?se(e):{},n=e.parentNode||e.ownerDocument;if("string"==typeof t?t={type:t,target:e}:t.target||(t.target=e),t=de(t),r.dispatcher&&r.dispatcher.call(e,t,i),n&&!t.isPropagationStopped()&&!0===t.bubbles)ge.call(null,n,t,i);else if(!n&&!t.defaultPrevented){var a=se(t.target);t.target[t.type]&&(a.disabled=!0,"function"==typeof t.target[t.type]&&t.target[t.type](),a.disabled=!1)}return!t.defaultPrevented}function ye(t,i,r){if(Array.isArray(i))return le(ye,t,i,r);var e=function e(){me(t,i,e),r.apply(this,arguments)};e.guid=r.guid=r.guid||re(),fe(t,i,e)}var ve=Object.freeze({fixEvent:de,on:fe,off:me,trigger:ge,one:ye}),_e=!1,be=void 0,Te=function(){if(I()&&!1!==be.options.autoSetup){var e=Array.prototype.slice.call(p.getElementsByTagName("video")),t=Array.prototype.slice.call(p.getElementsByTagName("audio")),i=Array.prototype.slice.call(p.getElementsByTagName("video-js")),r=e.concat(t,i);if(r&&0<r.length)for(var n=0,a=r.length;n<a;n++){var s=r[n];if(!s||!s.getAttribute){Se(1);break}void 0===s.player&&null!==s.getAttribute("data-setup")&&be(s)}else _e||Se(1)}};function Se(e,t){t&&(be=t),g.setTimeout(Te,e)}I()&&"complete"===p.readyState?_e=!0:ye(g,"load",function(){_e=!0});var Ee=function(e){var t=p.createElement("style");return t.className=e,t},we=function(e,t){e.styleSheet?e.styleSheet.cssText=t:e.textContent=t},ke=function(e,t,i){t.guid||(t.guid=re());var r=function(){return t.apply(e,arguments)};return r.guid=i?i+"_"+t.guid:t.guid,r},Ce=function(){};Ce.prototype.allowedEvents_={},Ce.prototype.addEventListener=Ce.prototype.on=function(e,t){var i=this.addEventListener;this.addEventListener=function(){},fe(this,e,t),this.addEventListener=i},Ce.prototype.removeEventListener=Ce.prototype.off=function(e,t){me(this,e,t)},Ce.prototype.one=function(e,t){var i=this.addEventListener;this.addEventListener=function(){},ye(this,e,t),this.addEventListener=i},Ce.prototype.dispatchEvent=Ce.prototype.trigger=function(e){var t=e.type||e;"string"==typeof e&&(e={type:t}),e=de(e),this.allowedEvents_[t]&&this["on"+t]&&this["on"+t](e),ge(this,e)};var Ae=function(t){return t instanceof Ce||!!t.eventBusEl_&&["on","one","off","trigger"].every(function(e){return"function"==typeof t[e]})},Le=function(e){return"string"==typeof e&&/\S/.test(e)||Array.isArray(e)&&!!e.length},Oe=function(e){if(!e.nodeName&&!Ae(e))throw new Error("Invalid target; must be a DOM node or evented object.")},Pe=function(e){if(!Le(e))throw new Error("Invalid event type; must be a non-empty string or array.")},Ie=function(e){if("function"!=typeof e)throw new Error("Invalid listener; must be a function.")},Re=function(e,t){var i=t.length<3||t[0]===e||t[0]===e.eventBusEl_,r=void 0,n=void 0,a=void 0;return i?(r=e.eventBusEl_,3<=t.length&&t.shift(),n=t[0],a=t[1]):(r=t[0],n=t[1],a=t[2]),Oe(r),Pe(n),Ie(a),{isTargetingSelf:i,target:r,type:n,listener:a=ke(e,a)}},xe=function(e,t,i,r){Oe(e),e.nodeName?ve[t](e,i,r):e[t](i,r)},De={on:function(){for(var e=this,t=arguments.length,i=Array(t),r=0;r<t;r++)i[r]=arguments[r];var n=Re(this,i),a=n.isTargetingSelf,s=n.target,o=n.type,u=n.listener;if(xe(s,"on",o,u),!a){var c=function(){return e.off(s,o,u)};c.guid=u.guid;var l=function(){return e.off("dispose",c)};l.guid=u.guid,xe(this,"on","dispose",c),xe(s,"on","dispose",l)}},one:function(){for(var n=this,e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];var r=Re(this,t),a=r.isTargetingSelf,s=r.target,o=r.type,u=r.listener;if(a)xe(s,"one",o,u);else{var c=function e(){for(var t=arguments.length,i=Array(t),r=0;r<t;r++)i[r]=arguments[r];n.off(s,o,e),u.apply(null,i)};c.guid=u.guid,xe(s,"one",o,c)}},off:function(e,t,i){if(!e||Le(e))me(this.eventBusEl_,e,t);else{var r=e,n=t;Oe(r),Pe(n),Ie(i),i=ke(this,i),this.off("dispose",i),r.nodeName?(me(r,n,i),me(r,"dispose",i)):Ae(r)&&(r.off(n,i),r.off("dispose",i))}},trigger:function(e,t){return ge(this.eventBusEl_,e,t)}};function Ue(e){var t=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).eventBusKey;if(t){if(!e[t].nodeName)throw new Error('The eventBusKey "'+t+'" does not refer to an element.');e.eventBusEl_=e[t]}else e.eventBusEl_=D("span",{className:"vjs-event-bus"});return w(e,De),e.on("dispose",function(){e.off(),g.setTimeout(function(){e.eventBusEl_=null},0)}),e}var Ne={state:{},setState:function(e){var i=this;"function"==typeof e&&(e=e());var r=void 0;return E(e,function(e,t){i.state[t]!==e&&((r=r||{})[t]={from:i.state[t],to:e}),i.state[t]=e}),r&&Ae(this)&&this.trigger({changes:r,type:"statechanged"}),r}};function Me(e,t){return w(e,Ne),e.state=w({},e.state,t),"function"==typeof e.handleStateChanged&&Ae(e)&&e.on("statechanged",e.handleStateChanged),e}function Be(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}function je(){for(var i={},e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.forEach(function(e){e&&E(e,function(e,t){C(e)?(C(i[t])||(i[t]={}),i[t]=je(i[t],e)):i[t]=e})}),i}var Fe=function(){function c(e,t,i){if(y(this,c),!e&&this.play?this.player_=e=this:this.player_=e,this.options_=je({},this.options_),t=this.options_=je(this.options_,t),this.id_=t.id||t.el&&t.el.id,!this.id_){var r=e&&e.id&&e.id()||"no_player";this.id_=r+"_component_"+re()}this.name_=t.name||null,t.el?this.el_=t.el:!1!==t.createEl&&(this.el_=this.createEl()),!1!==t.evented&&Ue(this,{eventBusKey:this.el_?"el_":null}),Me(this,this.constructor.defaultState),this.children_=[],this.childIndex_={},!(this.childNameIndex_={})!==t.initChildren&&this.initChildren(),this.ready(i),!1!==t.reportTouchActivity&&this.enableTouchActivity()}return c.prototype.dispose=function(){if(this.trigger({type:"dispose",bubbles:!1}),this.children_)for(var e=this.children_.length-1;0<=e;e--)this.children_[e].dispose&&this.children_[e].dispose();this.children_=null,this.childIndex_=null,this.childNameIndex_=null,this.el_&&(this.el_.parentNode&&this.el_.parentNode.removeChild(this.el_),ue(this.el_),this.el_=null),this.player_=null},c.prototype.player=function(){return this.player_},c.prototype.options=function(e){return f.warn("this.options() has been deprecated and will be moved to the constructor in 6.0"),e&&(this.options_=je(this.options_,e)),this.options_},c.prototype.el=function(){return this.el_},c.prototype.createEl=function(e,t,i){return D(e,t,i)},c.prototype.localize=function(e,n){var t=2<arguments.length&&void 0!==arguments[2]?arguments[2]:e,i=this.player_.language&&this.player_.language(),r=this.player_.languages&&this.player_.languages(),a=r&&r[i],s=i&&i.split("-")[0],o=r&&r[s],u=t;return a&&a[e]?u=a[e]:o&&o[e]&&(u=o[e]),n&&(u=u.replace(/\{(\d+)\}/g,function(e,t){var i=n[t-1],r=i;return"undefined"==typeof i&&(r=e),r})),u},c.prototype.contentEl=function(){return this.contentEl_||this.el_},c.prototype.id=function(){return this.id_},c.prototype.name=function(){return this.name_},c.prototype.children=function(){return this.children_},c.prototype.getChildById=function(e){return this.childIndex_[e]},c.prototype.getChild=function(e){if(e)return e=Be(e),this.childNameIndex_[e]},c.prototype.addChild=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:this.children_.length,r=void 0,n=void 0;if("string"==typeof e){n=Be(e);var a=t.componentClass||n;t.name=n;var s=c.getComponent(a);if(!s)throw new Error("Component "+a+" does not exist");if("function"!=typeof s)return null;r=new s(this.player_||this,t)}else r=e;if(this.children_.splice(i,0,r),"function"==typeof r.id&&(this.childIndex_[r.id()]=r),(n=n||r.name&&Be(r.name()))&&(this.childNameIndex_[n]=r),"function"==typeof r.el&&r.el()){var o=this.contentEl().children[i]||null;this.contentEl().insertBefore(r.el(),o)}return r},c.prototype.removeChild=function(e){if("string"==typeof e&&(e=this.getChild(e)),e&&this.children_){for(var t=!1,i=this.children_.length-1;0<=i;i--)if(this.children_[i]===e){t=!0,this.children_.splice(i,1);break}if(t){this.childIndex_[e.id()]=null,this.childNameIndex_[e.name()]=null;var r=e.el();r&&r.parentNode===this.contentEl()&&this.contentEl().removeChild(e.el())}}},c.prototype.initChildren=function(){var n=this,r=this.options_.children;if(r){var a=this.options_,e=void 0,i=c.getComponent("Tech");(e=Array.isArray(r)?r:Object.keys(r)).concat(Object.keys(this.options_).filter(function(t){return!e.some(function(e){return"string"==typeof e?t===e:t===e.name})})).map(function(e){var t=void 0,i=void 0;return"string"==typeof e?i=r[t=e]||n.options_[t]||{}:(t=e.name,i=e),{name:t,opts:i}}).filter(function(e){var t=c.getComponent(e.opts.componentClass||Be(e.name));return t&&!i.isTech(t)}).forEach(function(e){var t=e.name,i=e.opts;if(void 0!==a[t]&&(i=a[t]),!1!==i){!0===i&&(i={}),i.playerOptions=n.options_.playerOptions;var r=n.addChild(t,i);r&&(n[t]=r)}})}},c.prototype.buildCSSClass=function(){return""},c.prototype.ready=function(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1];if(e)return this.isReady_?void(t?e.call(this):this.setTimeout(e,1)):(this.readyQueue_=this.readyQueue_||[],void this.readyQueue_.push(e))},c.prototype.triggerReady=function(){this.isReady_=!0,this.setTimeout(function(){var e=this.readyQueue_;this.readyQueue_=[],e&&0<e.length&&e.forEach(function(e){e.call(this)},this),this.trigger("ready")},1)},c.prototype.$=function(e,t){return Z(e,t||this.contentEl())},c.prototype.$$=function(e,t){return ee(e,t||this.contentEl())},c.prototype.hasClass=function(e){return M(this.el_,e)},c.prototype.addClass=function(e){B(this.el_,e)},c.prototype.removeClass=function(e){j(this.el_,e)},c.prototype.toggleClass=function(e,t){F(this.el_,e,t)},c.prototype.show=function(){this.removeClass("vjs-hidden")},c.prototype.hide=function(){this.addClass("vjs-hidden")},c.prototype.lockShowing=function(){this.addClass("vjs-lock-showing")},c.prototype.unlockShowing=function(){this.removeClass("vjs-lock-showing")},c.prototype.getAttribute=function(e){return V(this.el_,e)},c.prototype.setAttribute=function(e,t){W(this.el_,e,t)},c.prototype.removeAttribute=function(e){G(this.el_,e)},c.prototype.width=function(e,t){return this.dimension("width",e,t)},c.prototype.height=function(e,t){return this.dimension("height",e,t)},c.prototype.dimensions=function(e,t){this.width(e,!0),this.height(t)},c.prototype.dimension=function(e,t,i){if(void 0!==t)return null!==t&&t==t||(t=0),-1!==(""+t).indexOf("%")||-1!==(""+t).indexOf("px")?this.el_.style[e]=t:this.el_.style[e]="auto"===t?"":t+"px",void(i||this.trigger("componentresize"));if(!this.el_)return 0;var r=this.el_.style[e],n=r.indexOf("px");return-1!==n?parseInt(r.slice(0,n),10):parseInt(this.el_["offset"+Be(e)],10)},c.prototype.currentDimension=function(e){var t=0;if("width"!==e&&"height"!==e)throw new Error("currentDimension only accepts width or height value");if("function"==typeof g.getComputedStyle){var i=g.getComputedStyle(this.el_);t=i.getPropertyValue(e)||i[e]}if(0===(t=parseFloat(t))){var r="offset"+Be(e);t=this.el_[r]}return t},c.prototype.currentDimensions=function(){return{width:this.currentDimension("width"),height:this.currentDimension("height")}},c.prototype.currentWidth=function(){return this.currentDimension("width")},c.prototype.currentHeight=function(){return this.currentDimension("height")},c.prototype.focus=function(){this.el_.focus()},c.prototype.blur=function(){this.el_.blur()},c.prototype.emitTapEvents=function(){var t=0,r=null,n=void 0;this.on("touchstart",function(e){1===e.touches.length&&(r={pageX:e.touches[0].pageX,pageY:e.touches[0].pageY},t=(new Date).getTime(),n=!0)}),this.on("touchmove",function(e){if(1<e.touches.length)n=!1;else if(r){var t=e.touches[0].pageX-r.pageX,i=e.touches[0].pageY-r.pageY;10<Math.sqrt(t*t+i*i)&&(n=!1)}});var e=function(){n=!1};this.on("touchleave",e),this.on("touchcancel",e),this.on("touchend",function(e){!(r=null)===n&&((new Date).getTime()-t<200&&(e.preventDefault(),this.trigger("tap")))})},c.prototype.enableTouchActivity=function(){if(this.player()&&this.player().reportUserActivity){var t=ke(this.player(),this.player().reportUserActivity),i=void 0;this.on("touchstart",function(){t(),this.clearInterval(i),i=this.setInterval(t,250)});var e=function(e){t(),this.clearInterval(i)};this.on("touchmove",t),this.on("touchend",e),this.on("touchcancel",e)}},c.prototype.setTimeout=function(e,t){var i=this;e=ke(this,e);var r=g.setTimeout(e,t),n=function(){return i.clearTimeout(r)};return n.guid="vjs-timeout-"+r,this.on("dispose",n),r},c.prototype.clearTimeout=function(e){g.clearTimeout(e);var t=function(){};return t.guid="vjs-timeout-"+e,this.off("dispose",t),e},c.prototype.setInterval=function(e,t){var i=this;e=ke(this,e);var r=g.setInterval(e,t),n=function(){return i.clearInterval(r)};return n.guid="vjs-interval-"+r,this.on("dispose",n),r},c.prototype.clearInterval=function(e){g.clearInterval(e);var t=function(){};return t.guid="vjs-interval-"+e,this.off("dispose",t),e},c.prototype.requestAnimationFrame=function(e){var t=this;if(this.supportsRaf_){e=ke(this,e);var i=g.requestAnimationFrame(e),r=function(){return t.cancelAnimationFrame(i)};return r.guid="vjs-raf-"+i,this.on("dispose",r),i}return this.setTimeout(e,1e3/60)},c.prototype.cancelAnimationFrame=function(e){if(this.supportsRaf_){g.cancelAnimationFrame(e);var t=function(){};return t.guid="vjs-raf-"+e,this.off("dispose",t),e}return this.clearTimeout(e)},c.registerComponent=function(e,t){if("string"!=typeof e||!e)throw new Error('Illegal component name, "'+e+'"; must be a non-empty string.');var i=c.getComponent("Tech"),r=i&&i.isTech(t),n=c===t||c.prototype.isPrototypeOf(t.prototype);if(r||!n){var a=void 0;throw a=r?"techs must be registered using Tech.registerTech()":"must be a Component subclass",new Error('Illegal component, "'+e+'"; '+a+".")}e=Be(e),c.components_||(c.components_={});var s=c.getComponent("Player");if("Player"===e&&s&&s.players){var o=s.players,u=Object.keys(o);if(o&&0<u.length&&u.map(function(e){return o[e]}).every(Boolean))throw new Error("Can not register Player component after player has been created.")}return c.components_[e]=t},c.getComponent=function(e){if(e)return e=Be(e),c.components_&&c.components_[e]?c.components_[e]:void 0},c}();Fe.prototype.supportsRaf_="function"==typeof g.requestAnimationFrame&&"function"==typeof g.cancelAnimationFrame,Fe.registerComponent("Component",Fe);var He,qe,Ve,We,Ge=g.navigator&&g.navigator.userAgent||"",ze=/AppleWebKit\/([\d.]+)/i.exec(Ge),Xe=ze?parseFloat(ze.pop()):null,Ye=/iPad/i.test(Ge),$e=/iPhone/i.test(Ge)&&!Ye,Ke=/iPod/i.test(Ge),Qe=$e||Ye||Ke,Je=(He=Ge.match(/OS (\d+)_/i))&&He[1]?He[1]:null,Ze=/Android/i.test(Ge),et=function(){var e=Ge.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!e)return null;var t=e[1]&&parseFloat(e[1]),i=e[2]&&parseFloat(e[2]);return t&&i?parseFloat(e[1]+"."+e[2]):t||null}(),tt=Ze&&et<5&&Xe<537,it=/Firefox/i.test(Ge),rt=/Edge/i.test(Ge),nt=!rt&&/Chrome/i.test(Ge),at=(qe=Ge.match(/Chrome\/(\d+)/))&&qe[1]?parseFloat(qe[1]):null,st=(Ve=/MSIE\s(\d+)\.\d/.exec(Ge),!(We=Ve&&parseFloat(Ve[1]))&&/Trident\/7.0/i.test(Ge)&&/rv:11.0/.test(Ge)&&(We=11),We),ot=/Safari/i.test(Ge)&&!nt&&!Ze&&!rt,ut=ot||Qe,ct=I()&&("ontouchstart"in g||g.DocumentTouch&&g.document instanceof g.DocumentTouch),lt=Object.freeze({IS_IPAD:Ye,IS_IPHONE:$e,IS_IPOD:Ke,IS_IOS:Qe,IOS_VERSION:Je,IS_ANDROID:Ze,ANDROID_VERSION:et,IS_NATIVE_ANDROID:tt,IS_FIREFOX:it,IS_EDGE:rt,IS_CHROME:nt,CHROME_VERSION:at,IE_VERSION:st,IS_SAFARI:ot,IS_ANY_SAFARI:ut,TOUCH_ENABLED:ct});function dt(e,t,i,r){return function(e,t,i){if("number"!=typeof t||t<0||i<t)throw new Error("Failed to execute '"+e+"' on 'TimeRanges': The index provided ("+t+") is non-numeric or out of bounds (0-"+i+").")}(e,r,i.length-1),i[r][t]}function ht(e){return void 0===e||0===e.length?{length:0,start:function(){throw new Error("This TimeRanges object is empty")},end:function(){throw new Error("This TimeRanges object is empty")}}:{length:e.length,start:dt.bind(null,"start",0,e),end:dt.bind(null,"end",1,e)}}function pt(e,t){return Array.isArray(e)?ht(e):void 0===e||void 0===t?ht():ht([[e,t]])}function ft(e,t){var i=0,r=void 0,n=void 0;if(!t)return 0;e&&e.length||(e=pt(0,0));for(var a=0;a<e.length;a++)r=e.start(a),t<(n=e.end(a))&&(n=t),i+=n-r;return i/t}for(var mt={},gt=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],yt=gt[0],vt=void 0,_t=0;_t<gt.length;_t++)if(gt[_t][1]in p){vt=gt[_t];break}if(vt)for(var bt=0;bt<vt.length;bt++)mt[yt[bt]]=vt[bt];function Tt(e){if(e instanceof Tt)return e;"number"==typeof e?this.code=e:"string"==typeof e?this.message=e:k(e)&&("number"==typeof e.code&&(this.code=e.code),w(this,e)),this.message||(this.message=Tt.defaultMessages[this.code]||"")}Tt.prototype.code=0,Tt.prototype.message="",Tt.prototype.status=null,Tt.errorTypes=["MEDIA_ERR_CUSTOM","MEDIA_ERR_ABORTED","MEDIA_ERR_NETWORK","MEDIA_ERR_DECODE","MEDIA_ERR_SRC_NOT_SUPPORTED","MEDIA_ERR_ENCRYPTED"],Tt.defaultMessages={1:"You aborted the media playback",2:"A network error caused the media download to fail part-way.",3:"The media playback was aborted due to a corruption problem or because the media used features your browser did not support.",4:"The media could not be loaded, either because the server or network failed or because the format is not supported.",5:"The media is encrypted and we do not have the keys to decrypt it."};for(var St=0;St<Tt.errorTypes.length;St++)Tt[Tt.errorTypes[St]]=St,Tt.prototype[Tt.errorTypes[St]]=St;var Et=function(e,t){var i,r=null;try{i=JSON.parse(e,t)}catch(e){r=e}return[r,i]};function wt(e){var t;null!=(t=e)&&"function"==typeof t.then&&e.then(null,function(e){})}var kt=function(r){return["kind","label","language","id","inBandMetadataTrackDispatchType","mode","src"].reduce(function(e,t,i){return r[t]&&(e[t]=r[t]),e},{cues:r.cues&&Array.prototype.map.call(r.cues,function(e){return{startTime:e.startTime,endTime:e.endTime,text:e.text,id:e.id}})})},Ct=function(e){var t=e.$$("track"),i=Array.prototype.map.call(t,function(e){return e.track});return Array.prototype.map.call(t,function(e){var t=kt(e.track);return e.src&&(t.src=e.src),t}).concat(Array.prototype.filter.call(e.textTracks(),function(e){return-1===i.indexOf(e)}).map(kt))},At=function(e,i){return e.forEach(function(e){var t=i.addRemoteTextTrack(e).track;!e.src&&e.cues&&e.cues.forEach(function(e){return t.addCue(e)})}),i.textTracks()},Lt="vjs-modal-dialog",Ot=function(r){function n(e,t){y(this,n);var i=b(this,r.call(this,e,t));return i.opened_=i.hasBeenOpened_=i.hasBeenFilled_=!1,i.closeable(!i.options_.uncloseable),i.content(i.options_.content),i.contentEl_=D("div",{className:Lt+"-content"},{role:"document"}),i.descEl_=D("p",{className:Lt+"-description vjs-control-text",id:i.el().getAttribute("aria-describedby")}),U(i.descEl_,i.description()),i.el_.appendChild(i.descEl_),i.el_.appendChild(i.contentEl_),i}return _(n,r),n.prototype.createEl=function(){return r.prototype.createEl.call(this,"div",{className:this.buildCSSClass(),tabIndex:-1},{"aria-describedby":this.id()+"_description","aria-hidden":"true","aria-label":this.label(),role:"dialog"})},n.prototype.dispose=function(){this.contentEl_=null,this.descEl_=null,this.previouslyActiveEl_=null,r.prototype.dispose.call(this)},n.prototype.buildCSSClass=function(){return Lt+" vjs-hidden "+r.prototype.buildCSSClass.call(this)},n.prototype.handleKeyPress=function(e){27===e.which&&this.closeable()&&this.close()},n.prototype.label=function(){return this.localize(this.options_.label||"Modal Window")},n.prototype.description=function(){var e=this.options_.description||this.localize("This is a modal window.");return this.closeable()&&(e+=" "+this.localize("This modal can be closed by pressing the Escape key or activating the close button.")),e},n.prototype.open=function(){if(!this.opened_){var e=this.player();this.trigger("beforemodalopen"),this.opened_=!0,(this.options_.fillAlways||!this.hasBeenOpened_&&!this.hasBeenFilled_)&&this.fill(),this.wasPlaying_=!e.paused(),this.options_.pauseOnOpen&&this.wasPlaying_&&e.pause(),this.closeable()&&this.on(this.el_.ownerDocument,"keydown",ke(this,this.handleKeyPress)),this.hadControls_=e.controls(),e.controls(!1),this.show(),this.conditionalFocus_(),this.el().setAttribute("aria-hidden","false"),this.trigger("modalopen"),this.hasBeenOpened_=!0}},n.prototype.opened=function(e){return"boolean"==typeof e&&this[e?"open":"close"](),this.opened_},n.prototype.close=function(){if(this.opened_){var e=this.player();this.trigger("beforemodalclose"),this.opened_=!1,this.wasPlaying_&&this.options_.pauseOnOpen&&e.play(),this.closeable()&&this.off(this.el_.ownerDocument,"keydown",ke(this,this.handleKeyPress)),this.hadControls_&&e.controls(!0),this.hide(),this.el().setAttribute("aria-hidden","true"),this.trigger("modalclose"),this.conditionalBlur_(),this.options_.temporary&&this.dispose()}},n.prototype.closeable=function(e){if("boolean"==typeof e){var t=this.closeable_=!!e,i=this.getChild("closeButton");if(t&&!i){var r=this.contentEl_;this.contentEl_=this.el_,i=this.addChild("closeButton",{controlText:"Close Modal Dialog"}),this.contentEl_=r,this.on(i,"close",this.close)}!t&&i&&(this.off(i,"close",this.close),this.removeChild(i),i.dispose())}return this.closeable_},n.prototype.fill=function(){this.fillWith(this.content())},n.prototype.fillWith=function(e){var t=this.contentEl(),i=t.parentNode,r=t.nextSibling;this.trigger("beforemodalfill"),this.hasBeenFilled_=!0,i.removeChild(t),this.empty(),Q(t,e),this.trigger("modalfill"),r?i.insertBefore(t,r):i.appendChild(t);var n=this.getChild("closeButton");n&&i.appendChild(n.el_)},n.prototype.empty=function(){this.trigger("beforemodalempty"),Y(this.contentEl()),this.trigger("modalempty")},n.prototype.content=function(e){return"undefined"!=typeof e&&(this.content_=e),this.content_},n.prototype.conditionalFocus_=function(){var e=p.activeElement,t=this.player_.el_;this.previouslyActiveEl_=null,(t.contains(e)||t===e)&&(this.previouslyActiveEl_=e,this.focus(),this.on(p,"keydown",this.handleKeyDown))},n.prototype.conditionalBlur_=function(){this.previouslyActiveEl_&&(this.previouslyActiveEl_.focus(),this.previouslyActiveEl_=null),this.off(p,"keydown",this.handleKeyDown)},n.prototype.handleKeyDown=function(e){if(9===e.which){for(var t=this.focusableEls_(),i=this.el_.querySelector(":focus"),r=void 0,n=0;n<t.length;n++)if(i===t[n]){r=n;break}p.activeElement===this.el_&&(r=0),e.shiftKey&&0===r?(t[t.length-1].focus(),e.preventDefault()):e.shiftKey||r!==t.length-1||(t[0].focus(),e.preventDefault())}},n.prototype.focusableEls_=function(){var e=this.el_.querySelectorAll("*");return Array.prototype.filter.call(e,function(e){return(e instanceof g.HTMLAnchorElement||e instanceof g.HTMLAreaElement)&&e.hasAttribute("href")||(e instanceof g.HTMLInputElement||e instanceof g.HTMLSelectElement||e instanceof g.HTMLTextAreaElement||e instanceof g.HTMLButtonElement)&&!e.hasAttribute("disabled")||e instanceof g.HTMLIFrameElement||e instanceof g.HTMLObjectElement||e instanceof g.HTMLEmbedElement||e.hasAttribute("tabindex")&&-1!==e.getAttribute("tabindex")||e.hasAttribute("contenteditable")})},n}(Fe);Ot.prototype.options_={pauseOnOpen:!0,temporary:!0},Fe.registerComponent("ModalDialog",Ot);var Pt=function(r){function n(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:[];y(this,n);var t=b(this,r.call(this));t.tracks_=[],Object.defineProperty(t,"length",{get:function(){return this.tracks_.length}});for(var i=0;i<e.length;i++)t.addTrack(e[i]);return t}return _(n,r),n.prototype.addTrack=function(e){var t=this.tracks_.length;""+t in this||Object.defineProperty(this,t,{get:function(){return this.tracks_[t]}}),-1===this.tracks_.indexOf(e)&&(this.tracks_.push(e),this.trigger({track:e,type:"addtrack"}))},n.prototype.removeTrack=function(e){for(var t=void 0,i=0,r=this.length;i<r;i++)if(this[i]===e){(t=this[i]).off&&t.off(),this.tracks_.splice(i,1);break}t&&this.trigger({track:t,type:"removetrack"})},n.prototype.getTrackById=function(e){for(var t=null,i=0,r=this.length;i<r;i++){var n=this[i];if(n.id===e){t=n;break}}return t},n}(Ce);for(var It in Pt.prototype.allowedEvents_={change:"change",addtrack:"addtrack",removetrack:"removetrack"},Pt.prototype.allowedEvents_)Pt.prototype["on"+It]=null;var Rt=function(e,t){for(var i=0;i<e.length;i++)Object.keys(e[i]).length&&t.id!==e[i].id&&(e[i].enabled=!1)},xt=function(r){function n(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:[];y(this,n);for(var t=e.length-1;0<=t;t--)if(e[t].enabled){Rt(e,e[t]);break}var i=b(this,r.call(this,e));return i.changing_=!1,i}return _(n,r),n.prototype.addTrack=function(e){var t=this;e.enabled&&Rt(this,e),r.prototype.addTrack.call(this,e),e.addEventListener&&e.addEventListener("enabledchange",function(){t.changing_||(t.changing_=!0,Rt(t,e),t.changing_=!1,t.trigger("change"))})},n}(Pt),Dt=function(e,t){for(var i=0;i<e.length;i++)Object.keys(e[i]).length&&t.id!==e[i].id&&(e[i].selected=!1)},Ut=function(r){function n(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:[];y(this,n);for(var t=e.length-1;0<=t;t--)if(e[t].selected){Dt(e,e[t]);break}var i=b(this,r.call(this,e));return i.changing_=!1,Object.defineProperty(i,"selectedIndex",{get:function(){for(var e=0;e<this.length;e++)if(this[e].selected)return e;return-1},set:function(){}}),i}return _(n,r),n.prototype.addTrack=function(e){var t=this;e.selected&&Dt(this,e),r.prototype.addTrack.call(this,e),e.addEventListener&&e.addEventListener("selectedchange",function(){t.changing_||(t.changing_=!0,Dt(t,e),t.changing_=!1,t.trigger("change"))})},n}(Pt),Nt=function(t){function e(){return y(this,e),b(this,t.apply(this,arguments))}return _(e,t),e.prototype.addTrack=function(e){t.prototype.addTrack.call(this,e),e.addEventListener("modechange",ke(this,function(){this.trigger("change")}));-1===["metadata","chapters"].indexOf(e.kind)&&e.addEventListener("modechange",ke(this,function(){this.trigger("selectedlanguagechange")}))},e}(Pt),Mt=function(){function r(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:[];y(this,r),this.trackElements_=[],Object.defineProperty(this,"length",{get:function(){return this.trackElements_.length}});for(var t=0,i=e.length;t<i;t++)this.addTrackElement_(e[t])}return r.prototype.addTrackElement_=function(e){var t=this.trackElements_.length;""+t in this||Object.defineProperty(this,t,{get:function(){return this.trackElements_[t]}}),-1===this.trackElements_.indexOf(e)&&this.trackElements_.push(e)},r.prototype.getTrackElementByTrack_=function(e){for(var t=void 0,i=0,r=this.trackElements_.length;i<r;i++)if(e===this.trackElements_[i].track){t=this.trackElements_[i];break}return t},r.prototype.removeTrackElement_=function(e){for(var t=0,i=this.trackElements_.length;t<i;t++)if(e===this.trackElements_[t]){this.trackElements_.splice(t,1);break}},r}(),Bt=function(){function t(e){y(this,t),t.prototype.setCues_.call(this,e),Object.defineProperty(this,"length",{get:function(){return this.length_}})}return t.prototype.setCues_=function(e){var t=this.length||0,i=0,r=e.length;this.cues_=e,this.length_=e.length;var n=function(e){""+e in this||Object.defineProperty(this,""+e,{get:function(){return this.cues_[e]}})};if(t<r)for(i=t;i<r;i++)n.call(this,i)},t.prototype.getCueById=function(e){for(var t=null,i=0,r=this.length;i<r;i++){var n=this[i];if(n.id===e){t=n;break}}return t},t}(),jt={alternative:"alternative",captions:"captions",main:"main",sign:"sign",subtitles:"subtitles",commentary:"commentary"},Ft={alternative:"alternative",descriptions:"descriptions",main:"main","main-desc":"main-desc",translation:"translation",commentary:"commentary"},Ht={subtitles:"subtitles",captions:"captions",descriptions:"descriptions",chapters:"chapters",metadata:"metadata"},qt={disabled:"disabled",hidden:"hidden",showing:"showing"},Vt=function(a){function s(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};y(this,s);var t=b(this,a.call(this)),i={id:e.id||"vjs_track_"+re(),kind:e.kind||"",label:e.label||"",language:e.language||""},r=function(e){Object.defineProperty(t,e,{get:function(){return i[e]},set:function(){}})};for(var n in i)r(n);return t}return _(s,a),s}(Ce),Wt=function(e){var t=["protocol","hostname","port","pathname","search","hash","host"],i=p.createElement("a");i.href=e;var r=""===i.host&&"file:"!==i.protocol,n=void 0;r&&((n=p.createElement("div")).innerHTML='<a href="'+e+'"></a>',i=n.firstChild,n.setAttribute("style","display:none; position:absolute;"),p.body.appendChild(n));for(var a={},s=0;s<t.length;s++)a[t[s]]=i[t[s]];return"http:"===a.protocol&&(a.host=a.host.replace(/:80$/,"")),"https:"===a.protocol&&(a.host=a.host.replace(/:443$/,"")),a.protocol||(a.protocol=g.location.protocol),r&&p.body.removeChild(n),a},Gt=function(e){if(!e.match(/^https?:\/\//)){var t=p.createElement("div");t.innerHTML='<a href="'+e+'">x</a>',e=t.firstChild.href}return e},zt=function(e){if("string"==typeof e){var t=/^(\/?)([\s\S]*?)((?:\.{1,2}|[^\/]+?)(\.([^\.\/\?]+)))(?:[\/]*|[\?].*)$/i.exec(e);if(t)return t.pop().toLowerCase()}return""},Xt=function(e){var t=g.location,i=Wt(e);return(":"===i.protocol?t.protocol:i.protocol)+i.host!==t.protocol+t.host},Yt=Object.freeze({parseUrl:Wt,getAbsoluteURL:Gt,getFileExtension:zt,isCrossOrigin:Xt}),$t=function(e){var t=Kt.call(e);return"[object Function]"===t||"function"==typeof e&&"[object RegExp]"!==t||"undefined"!=typeof window&&(e===window.setTimeout||e===window.alert||e===window.confirm||e===window.prompt)},Kt=Object.prototype.toString;var Qt=Object.freeze({default:$t,__moduleExports:$t}),Jt=t(function(e,t){(t=e.exports=function(e){return e.replace(/^\s*|\s*$/g,"")}).left=function(e){return e.replace(/^\s*/,"")},t.right=function(e){return e.replace(/\s*$/,"")}}),Zt=Jt.left,ei=Jt.right,ti=Object.freeze({default:Jt,__moduleExports:Jt,left:Zt,right:ei}),ii=Qt&&$t||Qt,ri=function(e,t,i){if(!ii(t))throw new TypeError("iterator must be a function");arguments.length<3&&(i=this);"[object Array]"===ni.call(e)?function(e,t,i){for(var r=0,n=e.length;r<n;r++)ai.call(e,r)&&t.call(i,e[r],r,e)}(e,t,i):"string"==typeof e?function(e,t,i){for(var r=0,n=e.length;r<n;r++)t.call(i,e.charAt(r),r,e)}(e,t,i):function(e,t,i){for(var r in e)ai.call(e,r)&&t.call(i,e[r],r,e)}(e,t,i)},ni=Object.prototype.toString,ai=Object.prototype.hasOwnProperty;var si=Object.freeze({default:ri,__moduleExports:ri}),oi=ti&&Jt||ti,ui=si&&ri||si,ci=function(e){if(!e)return{};var a={};return ui(oi(e).split("\n"),function(e){var t,i=e.indexOf(":"),r=oi(e.slice(0,i)).toLowerCase(),n=oi(e.slice(i+1));"undefined"==typeof a[r]?a[r]=n:(t=a[r],"[object Array]"===Object.prototype.toString.call(t)?a[r].push(n):a[r]=[a[r],n])}),a},li=Object.freeze({default:ci,__moduleExports:ci}),di=function(){for(var e={},t=0;t<arguments.length;t++){var i=arguments[t];for(var r in i)hi.call(i,r)&&(e[r]=i[r])}return e},hi=Object.prototype.hasOwnProperty;var pi=Object.freeze({default:di,__moduleExports:di}),fi=li&&ci||li,mi=pi&&di||pi,gi=vi;function yi(e,t,i){var r=e;return ii(t)?(i=t,"string"==typeof e&&(r={uri:e})):r=mi(t,{uri:e}),r.callback=i,r}function vi(e,t,i){return _i(t=yi(e,t,i))}function _i(r){if("undefined"==typeof r.callback)throw new Error("callback argument missing");var n=!1,a=function(e,t,i){n||(n=!0,r.callback(e,t,i))};function t(e){return clearTimeout(u),e instanceof Error||(e=new Error(""+(e||"Unknown XMLHttpRequest Error"))),e.statusCode=0,a(e,m)}function e(){if(!s){var e;clearTimeout(u),e=r.useXDR&&void 0===o.status?200:1223===o.status?204:o.status;var t=m,i=null;return 0!==e?(t={body:function(){var e=void 0;if(e=o.response?o.response:o.responseText||function(e){if("document"===e.responseType)return e.responseXML;var t=e.responseXML&&"parsererror"===e.responseXML.documentElement.nodeName;return""!==e.responseType||t?null:e.responseXML}(o),f)try{e=JSON.parse(e)}catch(e){}return e}(),statusCode:e,method:l,headers:{},url:c,rawRequest:o},o.getAllResponseHeaders&&(t.headers=fi(o.getAllResponseHeaders()))):i=new Error("Internal XMLHttpRequest Error"),a(i,t,t.body)}}var i,s,o=r.xhr||null;o||(o=r.cors||r.useXDR?new vi.XDomainRequest:new vi.XMLHttpRequest);var u,c=o.url=r.uri||r.url,l=o.method=r.method||"GET",d=r.body||r.data,h=o.headers=r.headers||{},p=!!r.sync,f=!1,m={body:void 0,headers:{},statusCode:0,method:l,url:c,rawRequest:o};if("json"in r&&!1!==r.json&&(f=!0,h.accept||h.Accept||(h.Accept="application/json"),"GET"!==l&&"HEAD"!==l&&(h["content-type"]||h["Content-Type"]||(h["Content-Type"]="application/json"),d=JSON.stringify(!0===r.json?d:r.json))),o.onreadystatechange=function(){4===o.readyState&&setTimeout(e,0)},o.onload=e,o.onerror=t,o.onprogress=function(){},o.onabort=function(){s=!0},o.ontimeout=t,o.open(l,c,!p,r.username,r.password),p||(o.withCredentials=!!r.withCredentials),!p&&0<r.timeout&&(u=setTimeout(function(){if(!s){s=!0,o.abort("timeout");var e=new Error("XMLHttpRequest timeout");e.code="ETIMEDOUT",t(e)}},r.timeout)),o.setRequestHeader)for(i in h)h.hasOwnProperty(i)&&o.setRequestHeader(i,h[i]);else if(r.headers&&!function(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0}(r.headers))throw new Error("Headers cannot be set on an XDomainRequest object");return"responseType"in r&&(o.responseType=r.responseType),"beforeSend"in r&&"function"==typeof r.beforeSend&&r.beforeSend(o),o.send(d||null),o}vi.XMLHttpRequest=g.XMLHttpRequest||function(){},vi.XDomainRequest="withCredentials"in new vi.XMLHttpRequest?vi.XMLHttpRequest:g.XDomainRequest,function(e,t){for(var i=0;i<e.length;i++)t(e[i])}(["get","put","post","patch","head","delete"],function(r){vi["delete"===r?"del":r]=function(e,t,i){return(t=yi(e,t,i)).method=r.toUpperCase(),_i(t)}});var bi=function(e,t){var i=new g.WebVTT.Parser(g,g.vttjs,g.WebVTT.StringDecoder()),r=[];i.oncue=function(e){t.addCue(e)},i.onparsingerror=function(e){r.push(e)},i.onflush=function(){t.trigger({type:"loadeddata",target:t})},i.parse(e),0<r.length&&(g.console&&g.console.groupCollapsed&&g.console.groupCollapsed("Text Track parsing errors for "+t.src),r.forEach(function(e){return f.error(e)}),g.console&&g.console.groupEnd&&g.console.groupEnd()),i.flush()},Ti=function(e,n){var t={uri:e},i=Xt(e);i&&(t.cors=i),gi(t,ke(this,function(e,t,i){if(e)return f.error(e,t);if(n.loaded_=!0,"function"!=typeof g.WebVTT){if(n.tech_){var r=function(){return bi(i,n)};n.tech_.on("vttjsloaded",r),n.tech_.on("vttjserror",function(){f.error("vttjs failed to load, stopping trying to process "+n.src),n.tech_.off("vttjsloaded",r)})}}else bi(i,n)}))},Si=function(c){function l(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};if(y(this,l),!e.tech)throw new Error("A tech was not provided.");var t=je(e,{kind:Ht[e.kind]||"subtitles",language:e.language||e.srclang||""}),i=qt[t.mode]||"disabled",r=t.default;"metadata"!==t.kind&&"chapters"!==t.kind||(i="hidden");var n=b(this,c.call(this,t));n.tech_=t.tech,n.cues_=[],n.activeCues_=[];var a=new Bt(n.cues_),s=new Bt(n.activeCues_),o=!1,u=ke(n,function(){this.activeCues,o&&(this.trigger("cuechange"),o=!1)});return"disabled"!==i&&n.tech_.ready(function(){n.tech_.on("timeupdate",u)},!0),Object.defineProperties(n,{default:{get:function(){return r},set:function(){}},mode:{get:function(){return i},set:function(e){var t=this;qt[e]&&("showing"===(i=e)&&this.tech_.ready(function(){t.tech_.on("timeupdate",u)},!0),this.trigger("modechange"))}},cues:{get:function(){return this.loaded_?a:null},set:function(){}},activeCues:{get:function(){if(!this.loaded_)return null;if(0===this.cues.length)return s;for(var e=this.tech_.currentTime(),t=[],i=0,r=this.cues.length;i<r;i++){var n=this.cues[i];n.startTime<=e&&n.endTime>=e?t.push(n):n.startTime===n.endTime&&n.startTime<=e&&n.startTime+.5>=e&&t.push(n)}if(o=!1,t.length!==this.activeCues_.length)o=!0;else for(var a=0;a<t.length;a++)-1===this.activeCues_.indexOf(t[a])&&(o=!0);return this.activeCues_=t,s.setCues_(this.activeCues_),s},set:function(){}}}),t.src?(n.src=t.src,Ti(t.src,n)):n.loaded_=!0,n}return _(l,c),l.prototype.addCue=function(e){var t=e;if(g.vttjs&&!(e instanceof g.vttjs.VTTCue)){for(var i in t=new g.vttjs.VTTCue(e.startTime,e.endTime,e.text),e)i in t||(t[i]=e[i]);t.id=e.id,t.originalCue_=e}for(var r=this.tech_.textTracks(),n=0;n<r.length;n++)r[n]!==this&&r[n].removeCue(t);this.cues_.push(t),this.cues.setCues_(this.cues_)},l.prototype.removeCue=function(e){for(var t=this.cues_.length;t--;){var i=this.cues_[t];if(i===e||i.originalCue_&&i.originalCue_===e){this.cues_.splice(t,1),this.cues.setCues_(this.cues_);break}}},l}(Vt);Si.prototype.allowedEvents_={cuechange:"cuechange"};var Ei=function(n){function a(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};y(this,a);var t=je(e,{kind:Ft[e.kind]||""}),i=b(this,n.call(this,t)),r=!1;return Object.defineProperty(i,"enabled",{get:function(){return r},set:function(e){"boolean"==typeof e&&e!==r&&(r=e,this.trigger("enabledchange"))}}),t.enabled&&(i.enabled=t.enabled),i.loaded_=!0,i}return _(a,n),a}(Vt),wi=function(n){function a(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};y(this,a);var t=je(e,{kind:jt[e.kind]||""}),i=b(this,n.call(this,t)),r=!1;return Object.defineProperty(i,"selected",{get:function(){return r},set:function(e){"boolean"==typeof e&&e!==r&&(r=e,this.trigger("selectedchange"))}}),t.selected&&(i.selected=t.selected),i}return _(a,n),a}(Vt),ki=0,Ci=2,Ai=function(n){function a(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};y(this,a);var t=b(this,n.call(this)),i=void 0,r=new Si(e);return t.kind=r.kind,t.src=r.src,t.srclang=r.language,t.label=r.label,t.default=r.default,Object.defineProperties(t,{readyState:{get:function(){return i}},track:{get:function(){return r}}}),i=ki,r.addEventListener("loadeddata",function(){i=Ci,t.trigger({type:"load",target:t})}),t}return _(a,n),a}(Ce);Ai.prototype.allowedEvents_={load:"load"},Ai.NONE=ki,Ai.LOADING=1,Ai.LOADED=Ci,Ai.ERROR=3;var Li={audio:{ListClass:xt,TrackClass:Ei,capitalName:"Audio"},video:{ListClass:Ut,TrackClass:wi,capitalName:"Video"},text:{ListClass:Nt,TrackClass:Si,capitalName:"Text"}};Object.keys(Li).forEach(function(e){Li[e].getterName=e+"Tracks",Li[e].privateName=e+"Tracks_"});var Oi={remoteText:{ListClass:Nt,TrackClass:Si,capitalName:"RemoteText",getterName:"remoteTextTracks",privateName:"remoteTextTracks_"},remoteTextEl:{ListClass:Mt,TrackClass:Ai,capitalName:"RemoteTextTrackEls",getterName:"remoteTextTrackEls",privateName:"remoteTextTrackEls_"}},Pi=je(Li,Oi);Oi.names=Object.keys(Oi),Li.names=Object.keys(Li),Pi.names=[].concat(Oi.names).concat(Li.names);var Ii=Object.create||function(){function t(){}return function(e){if(1!==arguments.length)throw new Error("Object.create shim only accepts one parameter.");return t.prototype=e,new t}}();function Ri(e,t){this.name="ParsingError",this.code=e.code,this.message=t||e.message}function xi(e){function t(e,t,i,r){return 3600*(0|e)+60*(0|t)+(0|i)+(0|r)/1e3}var i=e.match(/^(\d+):(\d{2})(:\d{2})?\.(\d{3})/);return i?i[3]?t(i[1],i[2],i[3].replace(":",""),i[4]):59<i[1]?t(i[1],i[2],0,i[4]):t(0,i[1],i[2],i[4]):null}function Di(){this.values=Ii(null)}function Ui(e,t,i,r){var n=r?e.split(r):[e];for(var a in n)if("string"==typeof n[a]){var s=n[a].split(i);if(2===s.length)t(s[0],s[1])}}function Ni(t,e,a){var i,r,s,n=t;function o(){var e=xi(t);if(null===e)throw new Ri(Ri.Errors.BadTimeStamp,"Malformed timestamp: "+n);return t=t.replace(/^[^\sa-zA-Z-]+/,""),e}function u(){t=t.replace(/^\s+/,"")}if(u(),e.startTime=o(),u(),"--\x3e"!==t.substr(0,3))throw new Ri(Ri.Errors.BadTimeStamp,"Malformed time stamp (time stamps must be separated by '--\x3e'): "+n);t=t.substr(3),u(),e.endTime=o(),u(),i=t,r=e,s=new Di,Ui(i,function(e,t){switch(e){case"region":for(var i=a.length-1;0<=i;i--)if(a[i].id===t){s.set(e,a[i].region);break}break;case"vertical":s.alt(e,t,["rl","lr"]);break;case"line":var r=t.split(","),n=r[0];s.integer(e,n),s.percent(e,n)&&s.set("snapToLines",!1),s.alt(e,n,["auto"]),2===r.length&&s.alt("lineAlign",r[1],["start","middle","end"]);break;case"position":r=t.split(","),s.percent(e,r[0]),2===r.length&&s.alt("positionAlign",r[1],["start","middle","end"]);break;case"size":s.percent(e,t);break;case"align":s.alt(e,t,["start","middle","end","left","right"])}},/:/,/\s/),r.region=s.get("region",null),r.vertical=s.get("vertical",""),r.line=s.get("line","auto"),r.lineAlign=s.get("lineAlign","start"),r.snapToLines=s.get("snapToLines",!0),r.size=s.get("size",100),r.align=s.get("align","middle"),r.position=s.get("position",{start:0,left:0,middle:50,end:100,right:100},r.align),r.positionAlign=s.get("positionAlign",{start:"start",left:"start",middle:"middle",end:"end",right:"end"},r.align)}((Ri.prototype=Ii(Error.prototype)).constructor=Ri).Errors={BadSignature:{code:0,message:"Malformed WebVTT signature."},BadTimeStamp:{code:1,message:"Malformed time stamp."}},Di.prototype={set:function(e,t){this.get(e)||""===t||(this.values[e]=t)},get:function(e,t,i){return i?this.has(e)?this.values[e]:t[i]:this.has(e)?this.values[e]:t},has:function(e){return e in this.values},alt:function(e,t,i){for(var r=0;r<i.length;++r)if(t===i[r]){this.set(e,t);break}},integer:function(e,t){/^-?\d+$/.test(t)&&this.set(e,parseInt(t,10))},percent:function(e,t){return!!(t.match(/^([\d]{1,3})(\.[\d]*)?%$/)&&0<=(t=parseFloat(t))&&t<=100)&&(this.set(e,t),!0)}};var Mi={"&amp;":"&","&lt;":"<","&gt;":">","&lrm;":"‎","&rlm;":"‏","&nbsp;":" "},Bi={c:"span",i:"i",b:"b",u:"u",ruby:"ruby",rt:"rt",v:"span",lang:"span"},ji={v:"title",lang:"lang"},Fi={rt:"ruby"};function Hi(a,i){function e(){if(!i)return null;var e,t=i.match(/^([^<]*)(<[^>]*>?)?/);return e=t[1]?t[1]:t[2],i=i.substr(e.length),e}function t(e){return Mi[e]}function r(e){for(;f=e.match(/&(amp|lt|gt|lrm|rlm|nbsp);/);)e=e.replace(f[0],t);return e}function n(e,t){var i=Bi[e];if(!i)return null;var r=a.document.createElement(i);r.localName=i;var n=ji[e];return n&&t&&(r[n]=t.trim()),r}for(var s,o,u,c=a.document.createElement("div"),l=c,d=[];null!==(s=e());)if("<"!==s[0])l.appendChild(a.document.createTextNode(r(s)));else{if("/"===s[1]){d.length&&d[d.length-1]===s.substr(2).replace(">","")&&(d.pop(),l=l.parentNode);continue}var h,p=xi(s.substr(1,s.length-2));if(p){h=a.document.createProcessingInstruction("timestamp",p),l.appendChild(h);continue}var f=s.match(/^<([^.\s/0-9>]+)(\.[^\s\\>]+)?([^>\\]+)?(\\?)>?$/);if(!f)continue;if(!(h=n(f[1],f[3])))continue;if(o=l,Fi[(u=h).localName]&&Fi[u.localName]!==o.localName)continue;f[2]&&(h.className=f[2].substr(1).replace("."," ")),d.push(f[1]),l.appendChild(h),l=h}return c}var qi=[[1470,1470],[1472,1472],[1475,1475],[1478,1478],[1488,1514],[1520,1524],[1544,1544],[1547,1547],[1549,1549],[1563,1563],[1566,1610],[1645,1647],[1649,1749],[1765,1766],[1774,1775],[1786,1805],[1807,1808],[1810,1839],[1869,1957],[1969,1969],[1984,2026],[2036,2037],[2042,2042],[2048,2069],[2074,2074],[2084,2084],[2088,2088],[2096,2110],[2112,2136],[2142,2142],[2208,2208],[2210,2220],[8207,8207],[64285,64285],[64287,64296],[64298,64310],[64312,64316],[64318,64318],[64320,64321],[64323,64324],[64326,64449],[64467,64829],[64848,64911],[64914,64967],[65008,65020],[65136,65140],[65142,65276],[67584,67589],[67592,67592],[67594,67637],[67639,67640],[67644,67644],[67647,67669],[67671,67679],[67840,67867],[67872,67897],[67903,67903],[67968,68023],[68030,68031],[68096,68096],[68112,68115],[68117,68119],[68121,68147],[68160,68167],[68176,68184],[68192,68223],[68352,68405],[68416,68437],[68440,68466],[68472,68479],[68608,68680],[126464,126467],[126469,126495],[126497,126498],[126500,126500],[126503,126503],[126505,126514],[126516,126519],[126521,126521],[126523,126523],[126530,126530],[126535,126535],[126537,126537],[126539,126539],[126541,126543],[126545,126546],[126548,126548],[126551,126551],[126553,126553],[126555,126555],[126557,126557],[126559,126559],[126561,126562],[126564,126564],[126567,126570],[126572,126578],[126580,126583],[126585,126588],[126590,126590],[126592,126601],[126603,126619],[126625,126627],[126629,126633],[126635,126651],[1114109,1114109]];function Vi(e){for(var t=0;t<qi.length;t++){var i=qi[t];if(e>=i[0]&&e<=i[1])return!0}return!1}function Wi(){}function Gi(e,t,i){Wi.call(this),this.cue=t,this.cueDiv=Hi(e,t.text);var r={color:"rgba(255, 255, 255, 1)",backgroundColor:"rgba(0, 0, 0, 0.8)",position:"relative",left:0,right:0,top:0,bottom:0,display:"inline",writingMode:""===t.vertical?"horizontal-tb":"lr"===t.vertical?"vertical-lr":"vertical-rl",unicodeBidi:"plaintext"};this.applyStyles(r,this.cueDiv),this.div=e.document.createElement("div"),r={direction:function(e){var t=[],i="";if(!e||!e.childNodes)return"ltr";function n(e,t){for(var i=t.childNodes.length-1;0<=i;i--)e.push(t.childNodes[i])}function a(e){if(!e||!e.length)return null;var t=e.pop(),i=t.textContent||t.innerText;if(i){var r=i.match(/^.*(\n|\r)/);return r?r[e.length=0]:i}return"ruby"===t.tagName?a(e):t.childNodes?(n(e,t),a(e)):void 0}for(n(t,e);i=a(t);)for(var r=0;r<i.length;r++)if(Vi(i.charCodeAt(r)))return"rtl";return"ltr"}(this.cueDiv),writingMode:""===t.vertical?"horizontal-tb":"lr"===t.vertical?"vertical-lr":"vertical-rl",unicodeBidi:"plaintext",textAlign:"middle"===t.align?"center":t.align,font:i.font,whiteSpace:"pre-line",position:"absolute"},this.applyStyles(r),this.div.appendChild(this.cueDiv);var n=0;switch(t.positionAlign){case"start":n=t.position;break;case"middle":n=t.position-t.size/2;break;case"end":n=t.position-t.size}""===t.vertical?this.applyStyles({left:this.formatStyle(n,"%"),width:this.formatStyle(t.size,"%")}):this.applyStyles({top:this.formatStyle(n,"%"),height:this.formatStyle(t.size,"%")}),this.move=function(e){this.applyStyles({top:this.formatStyle(e.top,"px"),bottom:this.formatStyle(e.bottom,"px"),left:this.formatStyle(e.left,"px"),right:this.formatStyle(e.right,"px"),height:this.formatStyle(e.height,"px"),width:this.formatStyle(e.width,"px")})}}function zi(e){var t,i,r,n;if(e.div){i=e.div.offsetHeight,r=e.div.offsetWidth,n=e.div.offsetTop;var a=(a=e.div.childNodes)&&(a=a[0])&&a.getClientRects&&a.getClientRects();e=e.div.getBoundingClientRect(),t=a?Math.max(a[0]&&a[0].height||0,e.height/a.length):0}this.left=e.left,this.right=e.right,this.top=e.top||n,this.height=e.height||i,this.bottom=e.bottom||n+(e.height||i),this.width=e.width||r,this.lineHeight=void 0!==t?t:e.lineHeight}function Xi(e,t,o,u){var i=new zi(t),r=t.cue,n=function(e){if("number"==typeof e.line&&(e.snapToLines||0<=e.line&&e.line<=100))return e.line;if(!e.track||!e.track.textTrackList||!e.track.textTrackList.mediaElement)return-1;for(var t=e.track,i=t.textTrackList,r=0,n=0;n<i.length&&i[n]!==t;n++)"showing"===i[n].mode&&r++;return-1*++r}(r),a=[];if(r.snapToLines){var s;switch(r.vertical){case"":a=["+y","-y"],s="height";break;case"rl":a=["+x","-x"],s="width";break;case"lr":a=["-x","+x"],s="width"}var c=i.lineHeight,l=c*Math.round(n),d=o[s]+c,h=a[0];Math.abs(l)>d&&(l=l<0?-1:1,l*=Math.ceil(d/c)*c),n<0&&(l+=""===r.vertical?o.height:o.width,a=a.reverse()),i.move(h,l)}else{var p=i.lineHeight/o.height*100;switch(r.lineAlign){case"middle":n-=p/2;break;case"end":n-=p}switch(r.vertical){case"":t.applyStyles({top:t.formatStyle(n,"%")});break;case"rl":t.applyStyles({left:t.formatStyle(n,"%")});break;case"lr":t.applyStyles({right:t.formatStyle(n,"%")})}a=["+y","-x","+x","-y"],i=new zi(t)}var f=function(e,t){for(var i,r=new zi(e),n=1,a=0;a<t.length;a++){for(;e.overlapsOppositeAxis(o,t[a])||e.within(o)&&e.overlapsAny(u);)e.move(t[a]);if(e.within(o))return e;var s=e.intersectPercentage(o);s<n&&(i=new zi(e),n=s),e=new zi(r)}return i||r}(i,a);t.move(f.toCSSCompatValues(o))}function Yi(){}Wi.prototype.applyStyles=function(e,t){for(var i in t=t||this.div,e)e.hasOwnProperty(i)&&(t.style[i]=e[i])},Wi.prototype.formatStyle=function(e,t){return 0===e?0:e+t},(Gi.prototype=Ii(Wi.prototype)).constructor=Gi,zi.prototype.move=function(e,t){switch(t=void 0!==t?t:this.lineHeight,e){case"+x":this.left+=t,this.right+=t;break;case"-x":this.left-=t,this.right-=t;break;case"+y":this.top+=t,this.bottom+=t;break;case"-y":this.top-=t,this.bottom-=t}},zi.prototype.overlaps=function(e){return this.left<e.right&&this.right>e.left&&this.top<e.bottom&&this.bottom>e.top},zi.prototype.overlapsAny=function(e){for(var t=0;t<e.length;t++)if(this.overlaps(e[t]))return!0;return!1},zi.prototype.within=function(e){return this.top>=e.top&&this.bottom<=e.bottom&&this.left>=e.left&&this.right<=e.right},zi.prototype.overlapsOppositeAxis=function(e,t){switch(t){case"+x":return this.left<e.left;case"-x":return this.right>e.right;case"+y":return this.top<e.top;case"-y":return this.bottom>e.bottom}},zi.prototype.intersectPercentage=function(e){return Math.max(0,Math.min(this.right,e.right)-Math.max(this.left,e.left))*Math.max(0,Math.min(this.bottom,e.bottom)-Math.max(this.top,e.top))/(this.height*this.width)},zi.prototype.toCSSCompatValues=function(e){return{top:this.top-e.top,bottom:e.bottom-this.bottom,left:this.left-e.left,right:e.right-this.right,height:this.height,width:this.width}},zi.getSimpleBoxPosition=function(e){var t=e.div?e.div.offsetHeight:e.tagName?e.offsetHeight:0,i=e.div?e.div.offsetWidth:e.tagName?e.offsetWidth:0,r=e.div?e.div.offsetTop:e.tagName?e.offsetTop:0;return{left:(e=e.div?e.div.getBoundingClientRect():e.tagName?e.getBoundingClientRect():e).left,right:e.right,top:e.top||r,height:e.height||t,bottom:e.bottom||r+(e.height||t),width:e.width||i}},Yi.StringDecoder=function(){return{decode:function(e){if(!e)return"";if("string"!=typeof e)throw new Error("Error - expected string data.");return decodeURIComponent(encodeURIComponent(e))}}},Yi.convertCueToDOMTree=function(e,t){return e&&t?Hi(e,t):null};Yi.processCues=function(r,n,e){if(!r||!n||!e)return null;for(;e.firstChild;)e.removeChild(e.firstChild);var a=r.document.createElement("div");if(a.style.position="absolute",a.style.left="0",a.style.right="0",a.style.top="0",a.style.bottom="0",a.style.margin="1.5%",e.appendChild(a),function(e){for(var t=0;t<e.length;t++)if(e[t].hasBeenReset||!e[t].displayState)return!0;return!1}(n)){var s=[],o=zi.getSimpleBoxPosition(a),u={font:Math.round(.05*o.height*100)/100+"px sans-serif"};!function(){for(var e,t,i=0;i<n.length;i++)t=n[i],e=new Gi(r,t,u),a.appendChild(e.div),Xi(0,e,o,s),t.displayState=e.div,s.push(zi.getSimpleBoxPosition(e))}()}else for(var t=0;t<n.length;t++)a.appendChild(n[t].displayState)},(Yi.Parser=function(e,t,i){i||(i=t,t={}),t||(t={}),this.window=e,this.vttjs=t,this.state="INITIAL",this.buffer="",this.decoder=i||new TextDecoder("utf8"),this.regionList=[]}).prototype={reportOrThrowError:function(e){if(!(e instanceof Ri))throw e;this.onparsingerror&&this.onparsingerror(e)},parse:function(e){var a=this;function t(){for(var e=a.buffer,t=0;t<e.length&&"\r"!==e[t]&&"\n"!==e[t];)++t;var i=e.substr(0,t);return"\r"===e[t]&&++t,"\n"===e[t]&&++t,a.buffer=e.substr(t),i}function i(e){e.match(/X-TIMESTAMP-MAP/)?Ui(e,function(e,t){switch(e){case"X-TIMESTAMP-MAP":i=t,r=new Di,Ui(i,function(e,t){switch(e){case"MPEGT":r.integer(e+"S",t);break;case"LOCA":r.set(e+"L",xi(t))}},/[^\d]:/,/,/),a.ontimestampmap&&a.ontimestampmap({MPEGTS:r.get("MPEGTS"),LOCAL:r.get("LOCAL")})}var i,r},/=/):Ui(e,function(e,t){switch(e){case"Region":!function(e){var n=new Di;if(Ui(e,function(e,t){switch(e){case"id":n.set(e,t);break;case"width":n.percent(e,t);break;case"lines":n.integer(e,t);break;case"regionanchor":case"viewportanchor":var i=t.split(",");if(2!==i.length)break;var r=new Di;if(r.percent("x",i[0]),r.percent("y",i[1]),!r.has("x")||!r.has("y"))break;n.set(e+"X",r.get("x")),n.set(e+"Y",r.get("y"));break;case"scroll":n.alt(e,t,["up"])}},/=/,/\s/),n.has("id")){var t=new(a.vttjs.VTTRegion||a.window.VTTRegion);t.width=n.get("width",100),t.lines=n.get("lines",3),t.regionAnchorX=n.get("regionanchorX",0),t.regionAnchorY=n.get("regionanchorY",100),t.viewportAnchorX=n.get("viewportanchorX",0),t.viewportAnchorY=n.get("viewportanchorY",100),t.scroll=n.get("scroll",""),a.onregion&&a.onregion(t),a.regionList.push({id:n.get("id"),region:t})}}(t)}},/:/)}e&&(a.buffer+=a.decoder.decode(e,{stream:!0}));try{var r;if("INITIAL"===a.state){if(!/\r\n|\n/.test(a.buffer))return this;var n=(r=t()).match(/^WEBVTT([ \t].*)?$/);if(!n||!n[0])throw new Ri(Ri.Errors.BadSignature);a.state="HEADER"}for(var s=!1;a.buffer;){if(!/\r\n|\n/.test(a.buffer))return this;switch(s?s=!1:r=t(),a.state){case"HEADER":/:/.test(r)?i(r):r||(a.state="ID");continue;case"NOTE":r||(a.state="ID");continue;case"ID":if(/^NOTE($|[ \t])/.test(r)){a.state="NOTE";break}if(!r)continue;if(a.cue=new(a.vttjs.VTTCue||a.window.VTTCue)(0,0,""),a.state="CUE",-1===r.indexOf("--\x3e")){a.cue.id=r;continue}case"CUE":try{Ni(r,a.cue,a.regionList)}catch(e){a.reportOrThrowError(e),a.cue=null,a.state="BADCUE";continue}a.state="CUETEXT";continue;case"CUETEXT":var o=-1!==r.indexOf("--\x3e");if(!r||o&&(s=!0)){a.oncue&&a.oncue(a.cue),a.cue=null,a.state="ID";continue}a.cue.text&&(a.cue.text+="\n"),a.cue.text+=r;continue;case"BADCUE":r||(a.state="ID");continue}}}catch(e){a.reportOrThrowError(e),"CUETEXT"===a.state&&a.cue&&a.oncue&&a.oncue(a.cue),a.cue=null,a.state="INITIAL"===a.state?"BADWEBVTT":"BADCUE"}return this},flush:function(){var t=this;try{if(t.buffer+=t.decoder.decode(),(t.cue||"HEADER"===t.state)&&(t.buffer+="\n\n",t.parse()),"INITIAL"===t.state)throw new Ri(Ri.Errors.BadSignature)}catch(e){t.reportOrThrowError(e)}return t.onflush&&t.onflush(),this}};var $i=Yi,Ki=Object.freeze({default:$i,__moduleExports:$i}),Qi="auto",Ji={"":1,lr:1,rl:1},Zi={start:1,middle:1,end:1,left:1,right:1};function er(e){return"string"==typeof e&&(!!Zi[e.toLowerCase()]&&e.toLowerCase())}function tr(e,t,i){this.hasBeenReset=!1;var r="",n=!1,a=e,s=t,o=i,u=null,c="",l=!0,d="auto",h="start",p=50,f="middle",m=50,g="middle";Object.defineProperties(this,{id:{enumerable:!0,get:function(){return r},set:function(e){r=""+e}},pauseOnExit:{enumerable:!0,get:function(){return n},set:function(e){n=!!e}},startTime:{enumerable:!0,get:function(){return a},set:function(e){if("number"!=typeof e)throw new TypeError("Start time must be set to a number.");a=e,this.hasBeenReset=!0}},endTime:{enumerable:!0,get:function(){return s},set:function(e){if("number"!=typeof e)throw new TypeError("End time must be set to a number.");s=e,this.hasBeenReset=!0}},text:{enumerable:!0,get:function(){return o},set:function(e){o=""+e,this.hasBeenReset=!0}},region:{enumerable:!0,get:function(){return u},set:function(e){u=e,this.hasBeenReset=!0}},vertical:{enumerable:!0,get:function(){return c},set:function(e){var t,i="string"==typeof(t=e)&&!!Ji[t.toLowerCase()]&&t.toLowerCase();if(!1===i)throw new SyntaxError("An invalid or illegal string was specified.");c=i,this.hasBeenReset=!0}},snapToLines:{enumerable:!0,get:function(){return l},set:function(e){l=!!e,this.hasBeenReset=!0}},line:{enumerable:!0,get:function(){return d},set:function(e){if("number"!=typeof e&&e!==Qi)throw new SyntaxError("An invalid number or illegal string was specified.");d=e,this.hasBeenReset=!0}},lineAlign:{enumerable:!0,get:function(){return h},set:function(e){var t=er(e);if(!t)throw new SyntaxError("An invalid or illegal string was specified.");h=t,this.hasBeenReset=!0}},position:{enumerable:!0,get:function(){return p},set:function(e){if(e<0||100<e)throw new Error("Position must be between 0 and 100.");p=e,this.hasBeenReset=!0}},positionAlign:{enumerable:!0,get:function(){return f},set:function(e){var t=er(e);if(!t)throw new SyntaxError("An invalid or illegal string was specified.");f=t,this.hasBeenReset=!0}},size:{enumerable:!0,get:function(){return m},set:function(e){if(e<0||100<e)throw new Error("Size must be between 0 and 100.");m=e,this.hasBeenReset=!0}},align:{enumerable:!0,get:function(){return g},set:function(e){var t=er(e);if(!t)throw new SyntaxError("An invalid or illegal string was specified.");g=t,this.hasBeenReset=!0}}}),this.displayState=void 0}tr.prototype.getCueAsHTML=function(){return WebVTT.convertCueToDOMTree(window,this.text)};var ir=tr,rr=Object.freeze({default:ir,__moduleExports:ir}),nr={"":!0,up:!0};function ar(e){return"number"==typeof e&&0<=e&&e<=100}var sr=function(){var t=100,i=3,r=0,n=100,a=0,s=100,o="";Object.defineProperties(this,{width:{enumerable:!0,get:function(){return t},set:function(e){if(!ar(e))throw new Error("Width must be between 0 and 100.");t=e}},lines:{enumerable:!0,get:function(){return i},set:function(e){if("number"!=typeof e)throw new TypeError("Lines must be set to a number.");i=e}},regionAnchorY:{enumerable:!0,get:function(){return n},set:function(e){if(!ar(e))throw new Error("RegionAnchorX must be between 0 and 100.");n=e}},regionAnchorX:{enumerable:!0,get:function(){return r},set:function(e){if(!ar(e))throw new Error("RegionAnchorY must be between 0 and 100.");r=e}},viewportAnchorY:{enumerable:!0,get:function(){return s},set:function(e){if(!ar(e))throw new Error("ViewportAnchorY must be between 0 and 100.");s=e}},viewportAnchorX:{enumerable:!0,get:function(){return a},set:function(e){if(!ar(e))throw new Error("ViewportAnchorX must be between 0 and 100.");a=e}},scroll:{enumerable:!0,get:function(){return o},set:function(e){var t,i="string"==typeof(t=e)&&!!nr[t.toLowerCase()]&&t.toLowerCase();if(!1===i)throw new SyntaxError("An invalid or illegal string was specified.");o=i}}})},or=Object.freeze({default:sr,__moduleExports:sr}),ur=Ki&&$i||Ki,cr=rr&&ir||rr,lr=or&&sr||or,dr=t(function(e){var t=e.exports={WebVTT:ur,VTTCue:cr,VTTRegion:lr};g.vttjs=t,g.WebVTT=t.WebVTT;var i=t.VTTCue,r=t.VTTRegion,n=g.VTTCue,a=g.VTTRegion;t.shim=function(){g.VTTCue=i,g.VTTRegion=r},t.restore=function(){g.VTTCue=n,g.VTTRegion=a},g.VTTCue||t.shim()});dr.WebVTT,dr.VTTCue,dr.VTTRegion;var hr=function(t){function n(){var i=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:function(){};y(this,n),i.reportTouchActivity=!1;var r=b(this,t.call(this,null,i,e));return r.hasStarted_=!1,r.on("playing",function(){this.hasStarted_=!0}),r.on("loadstart",function(){this.hasStarted_=!1}),Pi.names.forEach(function(e){var t=Pi[e];i&&i[t.getterName]&&(r[t.privateName]=i[t.getterName])}),r.featuresProgressEvents||r.manualProgressOn(),r.featuresTimeupdateEvents||r.manualTimeUpdatesOn(),["Text","Audio","Video"].forEach(function(e){!1===i["native"+e+"Tracks"]&&(r["featuresNative"+e+"Tracks"]=!1)}),!1===i.nativeCaptions||!1===i.nativeTextTracks?r.featuresNativeTextTracks=!1:!0!==i.nativeCaptions&&!0!==i.nativeTextTracks||(r.featuresNativeTextTracks=!0),r.featuresNativeTextTracks||r.emulateTextTracks(),r.autoRemoteTextTracks_=new Pi.text.ListClass,r.initTrackListeners(),i.nativeControlsForTouch||r.emitTapEvents(),r.constructor&&(r.name_=r.constructor.name||"Unknown Tech"),r}return _(n,t),n.prototype.triggerSourceset=function(e){var t=this;this.isReady_||this.one("ready",function(){return t.setTimeout(function(){return t.triggerSourceset(e)},1)}),this.trigger({src:e,type:"sourceset"})},n.prototype.manualProgressOn=function(){this.on("durationchange",this.onDurationChange),this.manualProgress=!0,this.one("ready",this.trackProgress)},n.prototype.manualProgressOff=function(){this.manualProgress=!1,this.stopTrackingProgress(),this.off("durationchange",this.onDurationChange)},n.prototype.trackProgress=function(e){this.stopTrackingProgress(),this.progressInterval=this.setInterval(ke(this,function(){var e=this.bufferedPercent();this.bufferedPercent_!==e&&this.trigger("progress"),1===(this.bufferedPercent_=e)&&this.stopTrackingProgress()}),500)},n.prototype.onDurationChange=function(e){this.duration_=this.duration()},n.prototype.buffered=function(){return pt(0,0)},n.prototype.bufferedPercent=function(){return ft(this.buffered(),this.duration_)},n.prototype.stopTrackingProgress=function(){this.clearInterval(this.progressInterval)},n.prototype.manualTimeUpdatesOn=function(){this.manualTimeUpdates=!0,this.on("play",this.trackCurrentTime),this.on("pause",this.stopTrackingCurrentTime)},n.prototype.manualTimeUpdatesOff=function(){this.manualTimeUpdates=!1,this.stopTrackingCurrentTime(),this.off("play",this.trackCurrentTime),this.off("pause",this.stopTrackingCurrentTime)},n.prototype.trackCurrentTime=function(){this.currentTimeInterval&&this.stopTrackingCurrentTime(),this.currentTimeInterval=this.setInterval(function(){this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},250)},n.prototype.stopTrackingCurrentTime=function(){this.clearInterval(this.currentTimeInterval),this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},n.prototype.dispose=function(){this.clearTracks(Li.names),this.manualProgress&&this.manualProgressOff(),this.manualTimeUpdates&&this.manualTimeUpdatesOff(),t.prototype.dispose.call(this)},n.prototype.clearTracks=function(e){var n=this;(e=[].concat(e)).forEach(function(e){for(var t=n[e+"Tracks"]()||[],i=t.length;i--;){var r=t[i];"text"===e&&n.removeRemoteTextTrack(r),t.removeTrack(r)}})},n.prototype.cleanupAutoTextTracks=function(){for(var e=this.autoRemoteTextTracks_||[],t=e.length;t--;){var i=e[t];this.removeRemoteTextTrack(i)}},n.prototype.reset=function(){},n.prototype.error=function(e){return void 0!==e&&(this.error_=new Tt(e),this.trigger("error")),this.error_},n.prototype.played=function(){return this.hasStarted_?pt(0,0):pt()},n.prototype.setCurrentTime=function(){this.manualTimeUpdates&&this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},n.prototype.initTrackListeners=function(){var n=this;Li.names.forEach(function(e){var t=Li[e],i=function(){n.trigger(e+"trackchange")},r=n[t.getterName]();r.addEventListener("removetrack",i),r.addEventListener("addtrack",i),n.on("dispose",function(){r.removeEventListener("removetrack",i),r.removeEventListener("addtrack",i)})})},n.prototype.addWebVttScript_=function(){var e=this;if(!g.WebVTT)if(p.body.contains(this.el())){if(!this.options_["vtt.js"]&&C(dr)&&0<Object.keys(dr).length)return void this.trigger("vttjsloaded");var t=p.createElement("script");t.src=this.options_["vtt.js"]||"https://vjs.zencdn.net/vttjs/0.14.1/vtt.min.js",t.onload=function(){e.trigger("vttjsloaded")},t.onerror=function(){e.trigger("vttjserror")},this.on("dispose",function(){t.onload=null,t.onerror=null}),g.WebVTT=!0,this.el().parentNode.appendChild(t)}else this.ready(this.addWebVttScript_)},n.prototype.emulateTextTracks=function(){var e=this,i=this.textTracks(),t=this.remoteTextTracks(),r=function(e){return i.addTrack(e.track)},n=function(e){return i.removeTrack(e.track)};t.on("addtrack",r),t.on("removetrack",n),this.addWebVttScript_();var a=function(){return e.trigger("texttrackchange")},s=function(){a();for(var e=0;e<i.length;e++){var t=i[e];t.removeEventListener("cuechange",a),"showing"===t.mode&&t.addEventListener("cuechange",a)}};s(),i.addEventListener("change",s),i.addEventListener("addtrack",s),i.addEventListener("removetrack",s),this.on("dispose",function(){t.off("addtrack",r),t.off("removetrack",n),i.removeEventListener("change",s),i.removeEventListener("addtrack",s),i.removeEventListener("removetrack",s);for(var e=0;e<i.length;e++){i[e].removeEventListener("cuechange",a)}})},n.prototype.addTextTrack=function(e,t,i){if(!e)throw new Error("TextTrack kind is required but was not provided");return function(e,t,i,r){var n=4<arguments.length&&void 0!==arguments[4]?arguments[4]:{},a=e.textTracks();n.kind=t,i&&(n.label=i),r&&(n.language=r),n.tech=e;var s=new Pi.text.TrackClass(n);return a.addTrack(s),s}(this,e,t,i)},n.prototype.createRemoteTextTrack=function(e){var t=je(e,{tech:this});return new Oi.remoteTextEl.TrackClass(t)},n.prototype.addRemoteTextTrack=function(){var e=this,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},i=arguments[1],r=this.createRemoteTextTrack(t);return!0!==i&&!1!==i&&(f.warn('Calling addRemoteTextTrack without explicitly setting the "manualCleanup" parameter to `true` is deprecated and default to `false` in future version of video.js'),i=!0),this.remoteTextTrackEls().addTrackElement_(r),this.remoteTextTracks().addTrack(r.track),!0!==i&&this.ready(function(){return e.autoRemoteTextTracks_.addTrack(r.track)}),r},n.prototype.removeRemoteTextTrack=function(e){var t=this.remoteTextTrackEls().getTrackElementByTrack_(e);this.remoteTextTrackEls().removeTrackElement_(t),this.remoteTextTracks().removeTrack(e),this.autoRemoteTextTracks_.removeTrack(e)},n.prototype.getVideoPlaybackQuality=function(){return{}},n.prototype.setPoster=function(){},n.prototype.playsinline=function(){},n.prototype.setPlaysinline=function(){},n.prototype.overrideNativeAudioTracks=function(){},n.prototype.overrideNativeVideoTracks=function(){},n.prototype.canPlayType=function(){return""},n.canPlayType=function(){return""},n.canPlaySource=function(e,t){return n.canPlayType(e.type)},n.isTech=function(e){return e.prototype instanceof n||e instanceof n||e===n},n.registerTech=function(e,t){if(n.techs_||(n.techs_={}),!n.isTech(t))throw new Error("Tech "+e+" must be a Tech");if(!n.canPlayType)throw new Error("Techs must have a static canPlayType method on them");if(!n.canPlaySource)throw new Error("Techs must have a static canPlaySource method on them");return e=Be(e),n.techs_[e]=t,"Tech"!==e&&n.defaultTechOrder_.push(e),t},n.getTech=function(e){if(e)return e=Be(e),n.techs_&&n.techs_[e]?n.techs_[e]:g&&g.videojs&&g.videojs[e]?(f.warn("The "+e+" tech was added to the videojs object when it should be registered using videojs.registerTech(name, tech)"),g.videojs[e]):void 0},n}(Fe);Pi.names.forEach(function(e){var t=Pi[e];hr.prototype[t.getterName]=function(){return this[t.privateName]=this[t.privateName]||new t.ListClass,this[t.privateName]}}),hr.prototype.featuresVolumeControl=!0,hr.prototype.featuresFullscreenResize=!1,hr.prototype.featuresPlaybackRate=!1,hr.prototype.featuresProgressEvents=!1,hr.prototype.featuresSourceset=!1,hr.prototype.featuresTimeupdateEvents=!1,hr.prototype.featuresNativeTextTracks=!1,hr.withSourceHandlers=function(n){n.registerSourceHandler=function(e,t){var i=n.sourceHandlers;i||(i=n.sourceHandlers=[]),void 0===t&&(t=i.length),i.splice(t,0,e)},n.canPlayType=function(e){for(var t=n.sourceHandlers||[],i=void 0,r=0;r<t.length;r++)if(i=t[r].canPlayType(e))return i;return""},n.selectSourceHandler=function(e,t){for(var i=n.sourceHandlers||[],r=0;r<i.length;r++)if(i[r].canHandleSource(e,t))return i[r];return null},n.canPlaySource=function(e,t){var i=n.selectSourceHandler(e,t);return i?i.canHandleSource(e,t):""};["seekable","seeking","duration"].forEach(function(e){var t=this[e];"function"==typeof t&&(this[e]=function(){return this.sourceHandler_&&this.sourceHandler_[e]?this.sourceHandler_[e].apply(this.sourceHandler_,arguments):t.apply(this,arguments)})},n.prototype),n.prototype.setSource=function(e){var t=n.selectSourceHandler(e,this.options_);t||(n.nativeSourceHandler?t=n.nativeSourceHandler:f.error("No source handler found for the current source.")),this.disposeSourceHandler(),this.off("dispose",this.disposeSourceHandler),t!==n.nativeSourceHandler&&(this.currentSource_=e),this.sourceHandler_=t.handleSource(e,this,this.options_),this.on("dispose",this.disposeSourceHandler)},n.prototype.disposeSourceHandler=function(){this.currentSource_&&(this.clearTracks(["audio","video"]),this.currentSource_=null),this.cleanupAutoTextTracks(),this.sourceHandler_&&(this.sourceHandler_.dispose&&this.sourceHandler_.dispose(),this.sourceHandler_=null)}},Fe.registerComponent("Tech",hr),hr.registerTech("Tech",hr),hr.defaultTechOrder_=[];var pr={},fr={},mr={};function gr(e,t,i){e.setTimeout(function(){return function i(){var r=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:[];var n=arguments[2];var a=arguments[3];var s=4<arguments.length&&void 0!==arguments[4]?arguments[4]:[];var o=5<arguments.length&&void 0!==arguments[5]&&arguments[5];var t=e[0],u=e.slice(1);if("string"==typeof t)i(r,pr[t],n,a,s,o);else if(t){var c=function(e,t){var i=fr[e.id()],r=null;if(null==i)return r=t(e),fr[e.id()]=[[t,r]],r;for(var n=0;n<i.length;n++){var a=i[n],s=a[0],o=a[1];s===t&&(r=o)}null===r&&(r=t(e),i.push([t,r]));return r}(a,t);c.setSource(w({},r),function(e,t){if(e)return i(r,u,n,a,s,o);s.push(c),i(t,r.type===t.type?u:pr[t.type],n,a,s,o)})}else u.length?i(r,u,n,a,s,o):o?n(r,s):i(r,pr["*"],n,a,s,!0)}(t,pr[t.type],i,e)},1)}function yr(e,t,i){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null,n="call"+Be(i),a=e.reduce(Tr(n),r),s=a===mr,o=s?null:t[i](a);return function(e,t,i,r){for(var n=e.length-1;0<=n;n--){var a=e[n];a[t]&&a[t](r,i)}}(e,i,o,s),o}var vr={buffered:1,currentTime:1,duration:1,seekable:1,played:1,paused:1},_r={setCurrentTime:1},br={play:1,pause:1};function Tr(i){return function(e,t){return e===mr?mr:t[i]?t[i](e):e}}var Sr={opus:"video/ogg",ogv:"video/ogg",mp4:"video/mp4",mov:"video/mp4",m4v:"video/mp4",mkv:"video/x-matroska",mp3:"audio/mpeg",aac:"audio/aac",oga:"audio/ogg",m3u8:"application/x-mpegURL"},Er=function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"",t=zt(e);return Sr[t.toLowerCase()]||""};function wr(e){var t=Er(e.src);return!e.type&&t&&(e.type=t),e}var kr=function(c){function l(e,t,i){y(this,l);var r=je({createEl:!1},t),n=b(this,c.call(this,e,r,i));if(t.playerOptions.sources&&0!==t.playerOptions.sources.length)e.src(t.playerOptions.sources);else for(var a=0,s=t.playerOptions.techOrder;a<s.length;a++){var o=Be(s[a]),u=hr.getTech(o);if(o||(u=Fe.getComponent(o)),u&&u.isSupported()){e.loadTech_(o);break}}return n}return _(l,c),l}(Fe);Fe.registerComponent("MediaLoader",kr);var Cr=function(e){var t=e.el();if(t.hasAttribute("src"))return e.triggerSourceset(t.src),!0;var i=e.$$("source"),r=[],n="";if(!i.length)return!1;for(var a=0;a<i.length;a++){var s=i[a].src;s&&-1===r.indexOf(s)&&r.push(s)}return!!r.length&&(1===r.length&&(n=r[0]),e.triggerSourceset(n),!0)},Ar=Object.defineProperty({},"innerHTML",{get:function(){return this.cloneNode(!0).innerHTML},set:function(e){var t=p.createElement(this.nodeName.toLowerCase());t.innerHTML=e;for(var i=p.createDocumentFragment();t.childNodes.length;)i.appendChild(t.childNodes[0]);return this.innerText="",g.Element.prototype.appendChild.call(this,i),this.innerHTML}}),Lr=function(e,t){for(var i={},r=0;r<e.length&&!((i=Object.getOwnPropertyDescriptor(e[r],t))&&i.set&&i.get);r++);return i.enumerable=!0,i.configurable=!0,i},Or=function(a){var s=a.el();if(!s.resetSourceWatch_){var t={},e=Lr([a.el(),g.HTMLMediaElement.prototype,g.Element.prototype,Ar],"innerHTML"),i=function(n){return function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];var r=n.apply(s,t);return Cr(a),r}};["append","appendChild","insertAdjacentHTML"].forEach(function(e){s[e]&&(t[e]=s[e],s[e]=i(t[e]))}),Object.defineProperty(s,"innerHTML",je(e,{set:i(e.set)})),s.resetSourceWatch_=function(){s.resetSourceWatch_=null,Object.keys(t).forEach(function(e){s[e]=t[e]}),Object.defineProperty(s,"innerHTML",e)},a.one("sourceset",s.resetSourceWatch_)}},Pr=Object.defineProperty({},"src",{get:function(){return this.hasAttribute("src")?Gt(g.Element.prototype.getAttribute.call(this,"src")):""},set:function(e){return g.Element.prototype.setAttribute.call(this,"src",e),e}}),Ir=function(r){if(r.featuresSourceset){var n=r.el();if(!n.resetSourceset_){var i=Lr([r.el(),g.HTMLMediaElement.prototype,Pr],"src"),a=n.setAttribute,t=n.load;Object.defineProperty(n,"src",je(i,{set:function(e){var t=i.set.call(n,e);return r.triggerSourceset(n.src),t}})),n.setAttribute=function(e,t){var i=a.call(n,e,t);return/src/i.test(e)&&r.triggerSourceset(n.src),i},n.load=function(){var e=t.call(n);return Cr(r)||(r.triggerSourceset(""),Or(r)),e},n.currentSrc?r.triggerSourceset(n.currentSrc):Cr(r)||Or(r),n.resetSourceset_=function(){n.resetSourceset_=null,n.load=t,n.setAttribute=a,Object.defineProperty(n,"src",i),n.resetSourceWatch_&&n.resetSourceWatch_()}}}},Rr=d(["Text Tracks are being loaded from another origin but the crossorigin attribute isn't used.\n            This may prevent text tracks from loading."],["Text Tracks are being loaded from another origin but the crossorigin attribute isn't used.\n            This may prevent text tracks from loading."]),xr=function(l){function d(e,t){y(this,d);var i=b(this,l.call(this,e,t)),r=e.source,n=!1;if(r&&(i.el_.currentSrc!==r.src||e.tag&&3===e.tag.initNetworkState_)?i.setSource(r):i.handleLateInit_(i.el_),e.enableSourceset&&i.setupSourcesetHandling_(),i.el_.hasChildNodes()){for(var a=i.el_.childNodes,s=a.length,o=[];s--;){var u=a[s];"track"===u.nodeName.toLowerCase()&&(i.featuresNativeTextTracks?(i.remoteTextTrackEls().addTrackElement_(u),i.remoteTextTracks().addTrack(u.track),i.textTracks().addTrack(u.track),n||i.el_.hasAttribute("crossorigin")||!Xt(u.src)||(n=!0)):o.push(u))}for(var c=0;c<o.length;c++)i.el_.removeChild(o[c])}return i.proxyNativeTracks_(),i.featuresNativeTextTracks&&n&&f.warn(m(Rr)),i.restoreMetadataTracksInIOSNativePlayer_(),(ct||$e||tt)&&!0===e.nativeControlsForTouch&&i.setControls(!0),i.proxyWebkitFullscreen_(),i.triggerReady(),i}return _(d,l),d.prototype.dispose=function(){this.el_&&this.el_.resetSourceset_&&this.el_.resetSourceset_(),d.disposeMediaElement(this.el_),this.options_=null,l.prototype.dispose.call(this)},d.prototype.setupSourcesetHandling_=function(){Ir(this)},d.prototype.restoreMetadataTracksInIOSNativePlayer_=function(){var r=this.textTracks(),n=void 0,e=function(){n=[];for(var e=0;e<r.length;e++){var t=r[e];"metadata"===t.kind&&n.push({track:t,storedMode:t.mode})}};e(),r.addEventListener("change",e),this.on("dispose",function(){return r.removeEventListener("change",e)});var t=function e(){for(var t=0;t<n.length;t++){var i=n[t];"disabled"===i.track.mode&&i.track.mode!==i.storedMode&&(i.track.mode=i.storedMode)}r.removeEventListener("change",e)};this.on("webkitbeginfullscreen",function(){r.removeEventListener("change",e),r.removeEventListener("change",t),r.addEventListener("change",t)}),this.on("webkitendfullscreen",function(){r.removeEventListener("change",e),r.addEventListener("change",e),r.removeEventListener("change",t)})},d.prototype.overrideNative_=function(e,t){var i=this;if(t===this["featuresNative"+e+"Tracks"]){var r=e.toLowerCase();this[r+"TracksListeners_"]&&Object.keys(this[r+"TracksListeners_"]).forEach(function(e){i.el()[r+"Tracks"].removeEventListener(e,i[r+"TracksListeners_"][e])}),this["featuresNative"+e+"Tracks"]=!t,this[r+"TracksListeners_"]=null,this.proxyNativeTracksForType_(r)}},d.prototype.overrideNativeAudioTracks=function(e){this.overrideNative_("Audio",e)},d.prototype.overrideNativeVideoTracks=function(e){this.overrideNative_("Video",e)},d.prototype.proxyNativeTracksForType_=function(e){var r=this,t=Li[e],n=this.el()[t.getterName],a=this[t.getterName]();if(this["featuresNative"+t.capitalName+"Tracks"]&&n&&n.addEventListener){var s={change:function(e){a.trigger({type:"change",target:a,currentTarget:a,srcElement:a})},addtrack:function(e){a.addTrack(e.track)},removetrack:function(e){a.removeTrack(e.track)}},i=function(){for(var e=[],t=0;t<a.length;t++){for(var i=!1,r=0;r<n.length;r++)if(n[r]===a[t]){i=!0;break}i||e.push(a[t])}for(;e.length;)a.removeTrack(e.shift())};this[t.getterName+"Listeners_"]=s,Object.keys(s).forEach(function(t){var i=s[t];n.addEventListener(t,i),r.on("dispose",function(e){return n.removeEventListener(t,i)})}),this.on("loadstart",i),this.on("dispose",function(e){return r.off("loadstart",i)})}},d.prototype.proxyNativeTracks_=function(){var t=this;Li.names.forEach(function(e){t.proxyNativeTracksForType_(e)})},d.prototype.createEl=function(){var e=this.options_.tag;if(!e||!this.options_.playerElIngest&&!this.movingMediaElementInDOM){if(e){var t=e.cloneNode(!0);e.parentNode&&e.parentNode.insertBefore(t,e),d.disposeMediaElement(e),e=t}else{e=p.createElement("video");var i=je({},this.options_.tag&&q(this.options_.tag));ct&&!0===this.options_.nativeControlsForTouch||delete i.controls,H(e,w(i,{id:this.options_.techId,class:"vjs-tech"}))}e.playerId=this.options_.playerId}"undefined"!=typeof this.options_.preload&&W(e,"preload",this.options_.preload);for(var r=["loop","muted","playsinline","autoplay"],n=0;n<r.length;n++){var a=r[n],s=this.options_[a];"undefined"!=typeof s&&(s?W(e,a,a):G(e,a),e[a]=s)}return e},d.prototype.handleLateInit_=function(e){if(0!==e.networkState&&3!==e.networkState){if(0===e.readyState){var t=!1,i=function(){t=!0};this.on("loadstart",i);var r=function(){t||this.trigger("loadstart")};return this.on("loadedmetadata",r),void this.ready(function(){this.off("loadstart",i),this.off("loadedmetadata",r),t||this.trigger("loadstart")})}var n=["loadstart"];n.push("loadedmetadata"),2<=e.readyState&&n.push("loadeddata"),3<=e.readyState&&n.push("canplay"),4<=e.readyState&&n.push("canplaythrough"),this.ready(function(){n.forEach(function(e){this.trigger(e)},this)})}},d.prototype.setCurrentTime=function(e){try{this.el_.currentTime=e}catch(e){f(e,"Video is not ready. (Video.js)")}},d.prototype.duration=function(){var t=this;if(this.el_.duration===1/0&&Ze&&nt&&0===this.el_.currentTime){return this.on("timeupdate",function e(){0<t.el_.currentTime&&(t.el_.duration===1/0&&t.trigger("durationchange"),t.off("timeupdate",e))}),NaN}return this.el_.duration||NaN},d.prototype.width=function(){return this.el_.offsetWidth},d.prototype.height=function(){return this.el_.offsetHeight},d.prototype.proxyWebkitFullscreen_=function(){var e=this;if("webkitDisplayingFullscreen"in this.el_){var t=function(){this.trigger("fullscreenchange",{isFullscreen:!1})},i=function(){"webkitPresentationMode"in this.el_&&"picture-in-picture"!==this.el_.webkitPresentationMode&&(this.one("webkitendfullscreen",t),this.trigger("fullscreenchange",{isFullscreen:!0}))};this.on("webkitbeginfullscreen",i),this.on("dispose",function(){e.off("webkitbeginfullscreen",i),e.off("webkitendfullscreen",t)})}},d.prototype.supportsFullScreen=function(){if("function"==typeof this.el_.webkitEnterFullScreen){var e=g.navigator&&g.navigator.userAgent||"";if(/Android/.test(e)||!/Chrome|Mac OS X 10.5/.test(e))return!0}return!1},d.prototype.enterFullScreen=function(){var e=this.el_;e.paused&&e.networkState<=e.HAVE_METADATA?(this.el_.play(),this.setTimeout(function(){e.pause(),e.webkitEnterFullScreen()},0)):e.webkitEnterFullScreen()},d.prototype.exitFullScreen=function(){this.el_.webkitExitFullScreen()},d.prototype.src=function(e){if(void 0===e)return this.el_.src;this.setSrc(e)},d.prototype.reset=function(){d.resetMediaElement(this.el_)},d.prototype.currentSrc=function(){return this.currentSource_?this.currentSource_.src:this.el_.currentSrc},d.prototype.setControls=function(e){this.el_.controls=!!e},d.prototype.addTextTrack=function(e,t,i){return this.featuresNativeTextTracks?this.el_.addTextTrack(e,t,i):l.prototype.addTextTrack.call(this,e,t,i)},d.prototype.createRemoteTextTrack=function(e){if(!this.featuresNativeTextTracks)return l.prototype.createRemoteTextTrack.call(this,e);var t=p.createElement("track");return e.kind&&(t.kind=e.kind),e.label&&(t.label=e.label),(e.language||e.srclang)&&(t.srclang=e.language||e.srclang),e.default&&(t.default=e.default),e.id&&(t.id=e.id),e.src&&(t.src=e.src),t},d.prototype.addRemoteTextTrack=function(e,t){var i=l.prototype.addRemoteTextTrack.call(this,e,t);return this.featuresNativeTextTracks&&this.el().appendChild(i),i},d.prototype.removeRemoteTextTrack=function(e){if(l.prototype.removeRemoteTextTrack.call(this,e),this.featuresNativeTextTracks)for(var t=this.$$("track"),i=t.length;i--;)e!==t[i]&&e!==t[i].track||this.el().removeChild(t[i])},d.prototype.getVideoPlaybackQuality=function(){if("function"==typeof this.el().getVideoPlaybackQuality)return this.el().getVideoPlaybackQuality();var e={};return"undefined"!=typeof this.el().webkitDroppedFrameCount&&"undefined"!=typeof this.el().webkitDecodedFrameCount&&(e.droppedVideoFrames=this.el().webkitDroppedFrameCount,e.totalVideoFrames=this.el().webkitDecodedFrameCount),g.performance&&"function"==typeof g.performance.now?e.creationTime=g.performance.now():g.performance&&g.performance.timing&&"number"==typeof g.performance.timing.navigationStart&&(e.creationTime=g.Date.now()-g.performance.timing.navigationStart),e},d}(hr);if(I()){xr.TEST_VID=p.createElement("video");var Dr=p.createElement("track");Dr.kind="captions",Dr.srclang="en",Dr.label="English",xr.TEST_VID.appendChild(Dr)}xr.isSupported=function(){try{xr.TEST_VID.volume=.5}catch(e){return!1}return!(!xr.TEST_VID||!xr.TEST_VID.canPlayType)},xr.canPlayType=function(e){return xr.TEST_VID.canPlayType(e)},xr.canPlaySource=function(e,t){return xr.canPlayType(e.type)},xr.canControlVolume=function(){try{var e=xr.TEST_VID.volume;return xr.TEST_VID.volume=e/2+.1,e!==xr.TEST_VID.volume}catch(e){return!1}},xr.canControlPlaybackRate=function(){if(Ze&&nt&&at<58)return!1;try{var e=xr.TEST_VID.playbackRate;return xr.TEST_VID.playbackRate=e/2+.1,e!==xr.TEST_VID.playbackRate}catch(e){return!1}},xr.canOverrideAttributes=function(){try{var e=function(){};Object.defineProperty(p.createElement("video"),"src",{get:e,set:e}),Object.defineProperty(p.createElement("audio"),"src",{get:e,set:e}),Object.defineProperty(p.createElement("video"),"innerHTML",{get:e,set:e}),Object.defineProperty(p.createElement("audio"),"innerHTML",{get:e,set:e})}catch(e){return!1}return!0},xr.supportsNativeTextTracks=function(){return ut},xr.supportsNativeVideoTracks=function(){return!(!xr.TEST_VID||!xr.TEST_VID.videoTracks)},xr.supportsNativeAudioTracks=function(){return!(!xr.TEST_VID||!xr.TEST_VID.audioTracks)},xr.Events=["loadstart","suspend","abort","error","emptied","stalled","loadedmetadata","loadeddata","canplay","canplaythrough","playing","waiting","seeking","seeked","ended","durationchange","timeupdate","progress","play","pause","ratechange","resize","volumechange"],xr.prototype.featuresVolumeControl=xr.canControlVolume(),xr.prototype.featuresPlaybackRate=xr.canControlPlaybackRate(),xr.prototype.featuresSourceset=xr.canOverrideAttributes(),xr.prototype.movingMediaElementInDOM=!Qe,xr.prototype.featuresFullscreenResize=!0,xr.prototype.featuresProgressEvents=!0,xr.prototype.featuresTimeupdateEvents=!0,xr.prototype.featuresNativeTextTracks=xr.supportsNativeTextTracks(),xr.prototype.featuresNativeVideoTracks=xr.supportsNativeVideoTracks(),xr.prototype.featuresNativeAudioTracks=xr.supportsNativeAudioTracks();var Ur=xr.TEST_VID&&xr.TEST_VID.constructor.prototype.canPlayType,Nr=/^application\/(?:x-|vnd\.apple\.)mpegurl/i;xr.patchCanPlayType=function(){4<=et&&!it&&!nt&&(xr.TEST_VID.constructor.prototype.canPlayType=function(e){return e&&Nr.test(e)?"maybe":Ur.call(this,e)})},xr.unpatchCanPlayType=function(){var e=xr.TEST_VID.constructor.prototype.canPlayType;return xr.TEST_VID.constructor.prototype.canPlayType=Ur,e},xr.patchCanPlayType(),xr.disposeMediaElement=function(e){if(e){for(e.parentNode&&e.parentNode.removeChild(e);e.hasChildNodes();)e.removeChild(e.firstChild);e.removeAttribute("src"),"function"==typeof e.load&&function(){try{e.load()}catch(e){}}()}},xr.resetMediaElement=function(e){if(e){for(var t=e.querySelectorAll("source"),i=t.length;i--;)e.removeChild(t[i]);e.removeAttribute("src"),"function"==typeof e.load&&function(){try{e.load()}catch(e){}}()}},["muted","defaultMuted","autoplay","controls","loop","playsinline"].forEach(function(e){xr.prototype[e]=function(){return this.el_[e]||this.el_.hasAttribute(e)}}),["muted","defaultMuted","autoplay","loop","playsinline"].forEach(function(t){xr.prototype["set"+Be(t)]=function(e){(this.el_[t]=e)?this.el_.setAttribute(t,t):this.el_.removeAttribute(t)}}),["paused","currentTime","buffered","volume","poster","preload","error","seeking","seekable","ended","playbackRate","defaultPlaybackRate","played","networkState","readyState","videoWidth","videoHeight"].forEach(function(e){xr.prototype[e]=function(){return this.el_[e]}}),["volume","src","poster","preload","playbackRate","defaultPlaybackRate"].forEach(function(t){xr.prototype["set"+Be(t)]=function(e){this.el_[t]=e}}),["pause","load","play"].forEach(function(e){xr.prototype[e]=function(){return this.el_[e]()}}),hr.withSourceHandlers(xr),xr.nativeSourceHandler={},xr.nativeSourceHandler.canPlayType=function(e){try{return xr.TEST_VID.canPlayType(e)}catch(e){return""}},xr.nativeSourceHandler.canHandleSource=function(e,t){if(e.type)return xr.nativeSourceHandler.canPlayType(e.type);if(e.src){var i=zt(e.src);return xr.nativeSourceHandler.canPlayType("video/"+i)}return""},xr.nativeSourceHandler.handleSource=function(e,t,i){t.setSrc(e.src)},xr.nativeSourceHandler.dispose=function(){},xr.registerSourceHandler(xr.nativeSourceHandler),hr.registerTech("Html5",xr);var Mr=d(["\n        Using the tech directly can be dangerous. I hope you know what you're doing.\n        See https://github.com/videojs/video.js/issues/2617 for more info.\n      "],["\n        Using the tech directly can be dangerous. I hope you know what you're doing.\n        See https://github.com/videojs/video.js/issues/2617 for more info.\n      "]),Br=["progress","abort","suspend","emptied","stalled","loadedmetadata","loadeddata","timeupdate","resize","volumechange","texttrackchange"],jr={canplay:"CanPlay",canplaythrough:"CanPlayThrough",playing:"Playing",seeked:"Seeked"},Fr=function(l){function d(e,t,i){if(y(this,d),e.id=e.id||t.id||"vjs_video_"+re(),(t=w(d.getTagSettings(e),t)).initChildren=!1,t.createEl=!1,t.evented=!1,t.reportTouchActivity=!1,!t.language)if("function"==typeof e.closest){var r=e.closest("[lang]");r&&r.getAttribute&&(t.language=r.getAttribute("lang"))}else for(var n=e;n&&1===n.nodeType;){if(q(n).hasOwnProperty("lang")){t.language=n.getAttribute("lang");break}n=n.parentNode}var a=b(this,l.call(this,null,t,i));if(a.isPosterFromTech_=!1,a.queuedCallbacks_=[],a.isReady_=!1,a.hasStarted_=!1,a.userActive_=!1,!a.options_||!a.options_.techOrder||!a.options_.techOrder.length)throw new Error("No techOrder specified. Did you overwrite videojs.options instead of just changing the properties you want to override?");if(a.tag=e,a.tagAttributes=e&&q(e),a.language(a.options_.language),t.languages){var s={};Object.getOwnPropertyNames(t.languages).forEach(function(e){s[e.toLowerCase()]=t.languages[e]}),a.languages_=s}else a.languages_=d.prototype.options_.languages;a.cache_={},a.poster_=t.poster||"",a.controls_=!!t.controls,a.cache_.lastVolume=1,e.controls=!1,e.removeAttribute("controls"),a.scrubbing_=!1,a.el_=a.createEl(),a.cache_.lastPlaybackRate=a.defaultPlaybackRate(),Ue(a,{eventBusKey:"el_"});var o=je(a.options_);if(t.plugins){var u=t.plugins;Object.keys(u).forEach(function(e){if("function"!=typeof this[e])throw new Error('plugin "'+e+'" does not exist');this[e](u[e])},a)}a.options_.playerOptions=o,a.middleware_=[],a.initChildren(),a.isAudio("audio"===e.nodeName.toLowerCase()),a.controls()?a.addClass("vjs-controls-enabled"):a.addClass("vjs-controls-disabled"),a.el_.setAttribute("role","region"),a.isAudio()?a.el_.setAttribute("aria-label",a.localize("Audio Player")):a.el_.setAttribute("aria-label",a.localize("Video Player")),a.isAudio()&&a.addClass("vjs-audio"),a.flexNotSupported_()&&a.addClass("vjs-no-flex"),Qe||a.addClass("vjs-workinghover"),d.players[a.id_]=a;var c=h.split(".")[0];return a.addClass("vjs-v"+c),a.userActive(!0),a.reportUserActivity(),a.one("play",a.listenForUserActivity_),a.on("fullscreenchange",a.handleFullscreenChange_),a.on("stageclick",a.handleStageClick_),a.changingSrc_=!1,a.playWaitingForReady_=!1,a.playOnLoadstart_=null,a}return _(d,l),d.prototype.dispose=function(){this.trigger("dispose"),this.off("dispose"),this.styleEl_&&this.styleEl_.parentNode&&(this.styleEl_.parentNode.removeChild(this.styleEl_),this.styleEl_=null),d.players[this.id_]=null,this.tag&&this.tag.player&&(this.tag.player=null),this.el_&&this.el_.player&&(this.el_.player=null),this.tech_&&(this.tech_.dispose(),this.isPosterFromTech_=!1,this.poster_=""),this.playerElIngest_&&(this.playerElIngest_=null),this.tag&&(this.tag=null),fr[this.id()]=null,l.prototype.dispose.call(this)},d.prototype.createEl=function(){var t=this.tag,i=void 0,e=this.playerElIngest_=t.parentNode&&t.parentNode.hasAttribute&&t.parentNode.hasAttribute("data-vjs-player"),r="video-js"===this.tag.tagName.toLowerCase();e?i=this.el_=t.parentNode:r||(i=this.el_=l.prototype.createEl.call(this,"div"));var n=q(t);if(r){for(i=this.el_=t,t=this.tag=p.createElement("video");i.children.length;)t.appendChild(i.firstChild);M(i,"video-js")||B(i,"video-js"),i.appendChild(t),e=this.playerElIngest_=i,Object.keys(i).forEach(function(e){t[e]=i[e]})}if(t.setAttribute("tabindex","-1"),st&&t.setAttribute("role","application"),t.removeAttribute("width"),t.removeAttribute("height"),Object.getOwnPropertyNames(n).forEach(function(e){r&&"class"===e||i.setAttribute(e,n[e]),r&&t.setAttribute(e,n[e])}),t.playerId=t.id,t.id+="_html5_api",t.className="vjs-tech",t.player=i.player=this,this.addClass("vjs-paused"),!0!==g.VIDEOJS_NO_DYNAMIC_STYLE){this.styleEl_=Ee("vjs-styles-dimensions");var a=Z(".vjs-styles-defaults"),s=Z("head");s.insertBefore(this.styleEl_,a?a.nextSibling:s.firstChild)}this.width(this.options_.width),this.height(this.options_.height),this.fluid(this.options_.fluid),this.aspectRatio(this.options_.aspectRatio);for(var o=t.getElementsByTagName("a"),u=0;u<o.length;u++){var c=o.item(u);B(c,"vjs-hidden"),c.setAttribute("hidden","hidden")}return t.initNetworkState_=t.networkState,t.parentNode&&!e&&t.parentNode.insertBefore(i,t),N(t,i),this.children_.unshift(t),this.el_.setAttribute("lang",this.language_),this.el_=i},d.prototype.width=function(e){return this.dimension("width",e)},d.prototype.height=function(e){return this.dimension("height",e)},d.prototype.dimension=function(e,t){var i=e+"_";if(void 0===t)return this[i]||0;if(""===t)return this[i]=void 0,void this.updateStyleEl_();var r=parseFloat(t);isNaN(r)?f.error('Improper value "'+t+'" supplied for for '+e):(this[i]=r,this.updateStyleEl_())},d.prototype.fluid=function(e){if(void 0===e)return!!this.fluid_;this.fluid_=!!e,e?this.addClass("vjs-fluid"):this.removeClass("vjs-fluid"),this.updateStyleEl_()},d.prototype.aspectRatio=function(e){if(void 0===e)return this.aspectRatio_;if(!/^\d+\:\d+$/.test(e))throw new Error("Improper value supplied for aspect ratio. The format should be width:height, for example 16:9.");this.aspectRatio_=e,this.fluid(!0),this.updateStyleEl_()},d.prototype.updateStyleEl_=function(){if(!0!==g.VIDEOJS_NO_DYNAMIC_STYLE){var e=void 0,t=void 0,i=void 0,r=(void 0!==this.aspectRatio_&&"auto"!==this.aspectRatio_?this.aspectRatio_:0<this.videoWidth()?this.videoWidth()+":"+this.videoHeight():"16:9").split(":"),n=r[1]/r[0];e=void 0!==this.width_?this.width_:void 0!==this.height_?this.height_/n:this.videoWidth()||300,t=void 0!==this.height_?this.height_:e*n,i=/^[^a-zA-Z]/.test(this.id())?"dimensions-"+this.id():this.id()+"-dimensions",this.addClass(i),we(this.styleEl_,"\n      ."+i+" {\n        width: "+e+"px;\n        height: "+t+"px;\n      }\n\n      ."+i+".vjs-fluid {\n        padding-top: "+100*n+"%;\n      }\n    ")}else{var a="number"==typeof this.width_?this.width_:this.options_.width,s="number"==typeof this.height_?this.height_:this.options_.height,o=this.tech_&&this.tech_.el();o&&(0<=a&&(o.width=a),0<=s&&(o.height=s))}},d.prototype.loadTech_=function(e,t){var i=this;this.tech_&&this.unloadTech_();var r=Be(e),n=e.charAt(0).toLowerCase()+e.slice(1);"Html5"!==r&&this.tag&&(hr.getTech("Html5").disposeMediaElement(this.tag),this.tag.player=null,this.tag=null),this.techName_=r,this.isReady_=!1;var a={source:t,nativeControlsForTouch:this.options_.nativeControlsForTouch,playerId:this.id(),techId:this.id()+"_"+r+"_api",autoplay:this.options_.autoplay,playsinline:this.options_.playsinline,preload:this.options_.preload,loop:this.options_.loop,muted:this.options_.muted,poster:this.poster(),language:this.language(),playerElIngest:this.playerElIngest_||!1,"vtt.js":this.options_["vtt.js"],canOverridePoster:!!this.options_.techCanOverridePoster,enableSourceset:this.options_.enableSourceset};Pi.names.forEach(function(e){var t=Pi[e];a[t.getterName]=i[t.privateName]}),w(a,this.options_[r]),w(a,this.options_[n]),w(a,this.options_[e.toLowerCase()]),this.tag&&(a.tag=this.tag),t&&t.src===this.cache_.src&&0<this.cache_.currentTime&&(a.startTime=this.cache_.currentTime);var s=hr.getTech(e);if(!s)throw new Error("No Tech named '"+r+"' exists! '"+r+"' should be registered using videojs.registerTech()'");this.tech_=new s(a),this.tech_.ready(ke(this,this.handleTechReady_),!0),At(this.textTracksJson_||[],this.tech_),Br.forEach(function(e){i.on(i.tech_,e,i["handleTech"+Be(e)+"_"])}),Object.keys(jr).forEach(function(t){i.on(i.tech_,t,function(e){0===i.tech_.playbackRate()&&i.tech_.seeking()?i.queuedCallbacks_.push({callback:i["handleTech"+jr[t]+"_"].bind(i),event:e}):i["handleTech"+jr[t]+"_"](e)})}),this.on(this.tech_,"loadstart",this.handleTechLoadStart_),this.on(this.tech_,"sourceset",this.handleTechSourceset_),this.on(this.tech_,"waiting",this.handleTechWaiting_),this.on(this.tech_,"ended",this.handleTechEnded_),this.on(this.tech_,"seeking",this.handleTechSeeking_),this.on(this.tech_,"play",this.handleTechPlay_),this.on(this.tech_,"firstplay",this.handleTechFirstPlay_),this.on(this.tech_,"pause",this.handleTechPause_),this.on(this.tech_,"durationchange",this.handleTechDurationChange_),this.on(this.tech_,"fullscreenchange",this.handleTechFullscreenChange_),this.on(this.tech_,"error",this.handleTechError_),this.on(this.tech_,"loadedmetadata",this.updateStyleEl_),this.on(this.tech_,"posterchange",this.handleTechPosterChange_),this.on(this.tech_,"textdata",this.handleTechTextData_),this.on(this.tech_,"ratechange",this.handleTechRateChange_),this.usingNativeControls(this.techGet_("controls")),this.controls()&&!this.usingNativeControls()&&this.addTechControlsListeners_(),this.tech_.el().parentNode===this.el()||"Html5"===r&&this.tag||N(this.tech_.el(),this.el()),this.tag&&(this.tag.player=null,this.tag=null)},d.prototype.unloadTech_=function(){var i=this;Pi.names.forEach(function(e){var t=Pi[e];i[t.privateName]=i[t.getterName]()}),this.textTracksJson_=Ct(this.tech_),this.isReady_=!1,this.tech_.dispose(),this.tech_=!1,this.isPosterFromTech_&&(this.poster_="",this.trigger("posterchange")),this.isPosterFromTech_=!1},d.prototype.tech=function(e){return void 0===e&&f.warn(m(Mr)),this.tech_},d.prototype.addTechControlsListeners_=function(){this.removeTechControlsListeners_(),this.on(this.tech_,"mousedown",this.handleTechClick_),this.on(this.tech_,"touchstart",this.handleTechTouchStart_),this.on(this.tech_,"touchmove",this.handleTechTouchMove_),this.on(this.tech_,"touchend",this.handleTechTouchEnd_),this.on(this.tech_,"tap",this.handleTechTap_)},d.prototype.removeTechControlsListeners_=function(){this.off(this.tech_,"tap",this.handleTechTap_),this.off(this.tech_,"touchstart",this.handleTechTouchStart_),this.off(this.tech_,"touchmove",this.handleTechTouchMove_),this.off(this.tech_,"touchend",this.handleTechTouchEnd_),this.off(this.tech_,"mousedown",this.handleTechClick_)},d.prototype.handleTechReady_=function(){if(this.triggerReady(),this.cache_.volume&&this.techCall_("setVolume",this.cache_.volume),this.handleTechPosterChange_(),this.handleTechDurationChange_(),(this.src()||this.currentSrc())&&this.tag&&this.options_.autoplay&&this.paused())try{delete this.tag.poster}catch(e){f("deleting tag.poster throws in some browsers",e)}},d.prototype.handleTechLoadStart_=function(){this.removeClass("vjs-ended"),this.removeClass("vjs-seeking"),this.error(null),this.paused()?(this.hasStarted(!1),this.trigger("loadstart")):(this.trigger("loadstart"),this.trigger("firstplay"))},d.prototype.updateSourceCaches_=function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"",t=e,i="";"string"!=typeof t&&(t=e.src,i=e.type),this.cache_.source=this.cache_.source||{},this.cache_.sources=this.cache_.sources||[],t&&!i&&(i=function(e,t){if(!t)return"";if(e.cache_.source.src===t&&e.cache_.source.type)return e.cache_.source.type;var i=e.cache_.sources.filter(function(e){return e.src===t});if(i.length)return i[0].type;for(var r=e.$$("source"),n=0;n<r.length;n++){var a=r[n];if(a.type&&a.src&&a.src===t)return a.type}return Er(t)}(this,t)),this.cache_.source={src:t,type:i};for(var r=this.cache_.sources.filter(function(e){return e.src&&e.src===t}),n=[],a=this.$$("source"),s=[],o=0;o<a.length;o++){var u=q(a[o]);n.push(u),u.src&&u.src===t&&s.push(u.src)}s.length&&!r.length?this.cache_.sources=n:r.length||(this.cache_.sources=[this.cache_.source]),this.cache_.src=t},d.prototype.handleTechSourceset_=function(e){var i=this;if(!this.changingSrc_&&(this.updateSourceCaches_(e.src),!e.src)){this.tech_.one(["sourceset","loadstart"],function e(t){"sourceset"!==t.type&&i.updateSourceCaches_(i.techGet_("currentSrc")),i.tech_.off(["sourceset","loadstart"],e)})}this.trigger({src:e.src,type:"sourceset"})},d.prototype.hasStarted=function(e){if(void 0===e)return this.hasStarted_;e!==this.hasStarted_&&(this.hasStarted_=e,this.hasStarted_?(this.addClass("vjs-has-started"),this.trigger("firstplay")):this.removeClass("vjs-has-started"))},d.prototype.handleTechPlay_=function(){this.removeClass("vjs-ended"),this.removeClass("vjs-paused"),this.addClass("vjs-playing"),this.hasStarted(!0),this.trigger("play")},d.prototype.handleTechRateChange_=function(){0<this.tech_.playbackRate()&&0===this.cache_.lastPlaybackRate&&(this.queuedCallbacks_.forEach(function(e){return e.callback(e.event)}),this.queuedCallbacks_=[]),this.cache_.lastPlaybackRate=this.tech_.playbackRate(),this.trigger("ratechange")},d.prototype.handleTechWaiting_=function(){var e=this;this.addClass("vjs-waiting"),this.trigger("waiting"),this.one("timeupdate",function(){return e.removeClass("vjs-waiting")})},d.prototype.handleTechCanPlay_=function(){this.removeClass("vjs-waiting"),this.trigger("canplay")},d.prototype.handleTechCanPlayThrough_=function(){this.removeClass("vjs-waiting"),this.trigger("canplaythrough")},d.prototype.handleTechPlaying_=function(){this.removeClass("vjs-waiting"),this.trigger("playing")},d.prototype.handleTechSeeking_=function(){this.addClass("vjs-seeking"),this.trigger("seeking")},d.prototype.handleTechSeeked_=function(){this.removeClass("vjs-seeking"),this.trigger("seeked")},d.prototype.handleTechFirstPlay_=function(){this.options_.starttime&&(f.warn("Passing the `starttime` option to the player will be deprecated in 6.0"),this.currentTime(this.options_.starttime)),this.addClass("vjs-has-started"),this.trigger("firstplay")},d.prototype.handleTechPause_=function(){this.removeClass("vjs-playing"),this.addClass("vjs-paused"),this.trigger("pause")},d.prototype.handleTechEnded_=function(){this.addClass("vjs-ended"),this.options_.loop?(this.currentTime(0),this.play()):this.paused()||this.pause(),this.trigger("ended")},d.prototype.handleTechDurationChange_=function(){this.duration(this.techGet_("duration"))},d.prototype.handleTechClick_=function(e){J(e)&&this.controls_&&(this.paused()?this.play():this.pause())},d.prototype.handleTechTap_=function(){this.userActive(!this.userActive())},d.prototype.handleTechTouchStart_=function(){this.userWasActive=this.userActive()},d.prototype.handleTechTouchMove_=function(){this.userWasActive&&this.reportUserActivity()},d.prototype.handleTechTouchEnd_=function(e){e.preventDefault()},d.prototype.handleFullscreenChange_=function(){this.isFullscreen()?this.addClass("vjs-fullscreen"):this.removeClass("vjs-fullscreen")},d.prototype.handleStageClick_=function(){this.reportUserActivity()},d.prototype.handleTechFullscreenChange_=function(e,t){t&&this.isFullscreen(t.isFullscreen),this.trigger("fullscreenchange")},d.prototype.handleTechError_=function(){var e=this.tech_.error();this.error(e)},d.prototype.handleTechTextData_=function(){var e=null;1<arguments.length&&(e=arguments[1]),this.trigger("textdata",e)},d.prototype.getCache=function(){return this.cache_},d.prototype.techCall_=function(n,a){this.ready(function(){if(n in _r)return e=this.middleware_,t=this.tech_,r=a,t[i=n](e.reduce(Tr(i),r));if(n in br)return yr(this.middleware_,this.tech_,n,a);var e,t,i,r;try{this.tech_&&this.tech_[n](a)}catch(e){throw f(e),e}},!0)},d.prototype.techGet_=function(t){if(this.tech_&&this.tech_.isReady_){if(t in vr)return e=this.middleware_,i=this.tech_,r=t,e.reduceRight(Tr(r),i[r]());if(t in br)return yr(this.middleware_,this.tech_,t);var e,i,r;try{return this.tech_[t]()}catch(e){if(void 0===this.tech_[t])throw f("Video.js: "+t+" method not defined for "+this.techName_+" playback technology.",e),e;if("TypeError"===e.name)throw f("Video.js: "+t+" unavailable on "+this.techName_+" playback technology element.",e),this.tech_.isReady_=!1,e;throw f(e),e}}},d.prototype.play=function(){var e=this;if(this.playOnLoadstart_&&this.off("loadstart",this.playOnLoadstart_),this.isReady_){if(!this.changingSrc_&&(this.src()||this.currentSrc()))return this.techGet_("play");this.playOnLoadstart_=function(){e.playOnLoadstart_=null,wt(e.play())},this.one("loadstart",this.playOnLoadstart_)}else{if(this.playWaitingForReady_)return;this.playWaitingForReady_=!0,this.ready(function(){e.playWaitingForReady_=!1,wt(e.play())})}},d.prototype.pause=function(){this.techCall_("pause")},d.prototype.paused=function(){return!1!==this.techGet_("paused")},d.prototype.played=function(){return this.techGet_("played")||pt(0,0)},d.prototype.scrubbing=function(e){if("undefined"==typeof e)return this.scrubbing_;this.scrubbing_=!!e,e?this.addClass("vjs-scrubbing"):this.removeClass("vjs-scrubbing")},d.prototype.currentTime=function(e){return"undefined"!=typeof e?(e<0&&(e=0),void this.techCall_("setCurrentTime",e)):(this.cache_.currentTime=this.techGet_("currentTime")||0,this.cache_.currentTime)},d.prototype.duration=function(e){if(void 0===e)return void 0!==this.cache_.duration?this.cache_.duration:NaN;(e=parseFloat(e))<0&&(e=1/0),e!==this.cache_.duration&&((this.cache_.duration=e)===1/0?this.addClass("vjs-live"):this.removeClass("vjs-live"),this.trigger("durationchange"))},d.prototype.remainingTime=function(){return this.duration()-this.currentTime()},d.prototype.remainingTimeDisplay=function(){return Math.floor(this.duration())-Math.floor(this.currentTime())},d.prototype.buffered=function(){var e=this.techGet_("buffered");return e&&e.length||(e=pt(0,0)),e},d.prototype.bufferedPercent=function(){return ft(this.buffered(),this.duration())},d.prototype.bufferedEnd=function(){var e=this.buffered(),t=this.duration(),i=e.end(e.length-1);return t<i&&(i=t),i},d.prototype.volume=function(e){var t=void 0;return void 0!==e?(t=Math.max(0,Math.min(1,parseFloat(e))),this.cache_.volume=t,this.techCall_("setVolume",t),void(0<t&&this.lastVolume_(t))):(t=parseFloat(this.techGet_("volume")),isNaN(t)?1:t)},d.prototype.muted=function(e){if(void 0===e)return this.techGet_("muted")||!1;this.techCall_("setMuted",e)},d.prototype.defaultMuted=function(e){return void 0!==e?this.techCall_("setDefaultMuted",e):this.techGet_("defaultMuted")||!1},d.prototype.lastVolume_=function(e){if(void 0===e||0===e)return this.cache_.lastVolume;this.cache_.lastVolume=e},d.prototype.supportsFullScreen=function(){return this.techGet_("supportsFullScreen")||!1},d.prototype.isFullscreen=function(e){if(void 0===e)return!!this.isFullscreen_;this.isFullscreen_=!!e},d.prototype.requestFullscreen=function(){var i=mt;this.isFullscreen(!0),i.requestFullscreen?(fe(p,i.fullscreenchange,ke(this,function e(t){this.isFullscreen(p[i.fullscreenElement]),!1===this.isFullscreen()&&me(p,i.fullscreenchange,e),this.trigger("fullscreenchange")})),this.el_[i.requestFullscreen]()):this.tech_.supportsFullScreen()?this.techCall_("enterFullScreen"):(this.enterFullWindow(),this.trigger("fullscreenchange"))},d.prototype.exitFullscreen=function(){var e=mt;this.isFullscreen(!1),e.requestFullscreen?p[e.exitFullscreen]():this.tech_.supportsFullScreen()?this.techCall_("exitFullScreen"):(this.exitFullWindow(),this.trigger("fullscreenchange"))},d.prototype.enterFullWindow=function(){this.isFullWindow=!0,this.docOrigOverflow=p.documentElement.style.overflow,fe(p,"keydown",ke(this,this.fullWindowOnEscKey)),p.documentElement.style.overflow="hidden",B(p.body,"vjs-full-window"),this.trigger("enterFullWindow")},d.prototype.fullWindowOnEscKey=function(e){27===e.keyCode&&(!0===this.isFullscreen()?this.exitFullscreen():this.exitFullWindow())},d.prototype.exitFullWindow=function(){this.isFullWindow=!1,me(p,"keydown",this.fullWindowOnEscKey),p.documentElement.style.overflow=this.docOrigOverflow,j(p.body,"vjs-full-window"),this.trigger("exitFullWindow")},d.prototype.canPlayType=function(e){for(var t=void 0,i=0,r=this.options_.techOrder;i<r.length;i++){var n=r[i],a=hr.getTech(n);if(a||(a=Fe.getComponent(n)),a){if(a.isSupported()&&(t=a.canPlayType(e)))return t}else f.error('The "'+n+'" tech is undefined. Skipped browser support check for that tech.')}return""},d.prototype.selectSource=function(e){var i,r=this,t=this.options_.techOrder.map(function(e){return[e,hr.getTech(e)]}).filter(function(e){var t=e[0],i=e[1];return i?i.isSupported():(f.error('The "'+t+'" tech is undefined. Skipped browser support check for that tech.'),!1)}),n=function(e,i,r){var n=void 0;return e.some(function(t){return i.some(function(e){if(n=r(t,e))return!0})}),n},a=function(e,t){var i=e[0];if(e[1].canPlaySource(t,r.options_[i.toLowerCase()]))return{source:t,tech:i}};return(this.options_.sourceOrder?n(e,t,(i=a,function(e,t){return i(t,e)})):n(t,e,a))||!1},d.prototype.src=function(e){var n=this;if("undefined"==typeof e)return this.cache_.src||"";var a=function t(e){if(Array.isArray(e)){var i=[];e.forEach(function(e){e=t(e),Array.isArray(e)?i=i.concat(e):k(e)&&i.push(e)}),e=i}else e="string"==typeof e&&e.trim()?[wr({src:e})]:k(e)&&"string"==typeof e.src&&e.src&&e.src.trim()?[wr(e)]:[];return e}(e);a.length?(this.changingSrc_=!0,this.cache_.sources=a,this.updateSourceCaches_(a[0]),gr(this,a[0],function(e,t){var i,r;if(n.middleware_=t,n.cache_.sources=a,n.updateSourceCaches_(e),n.src_(e))return 1<a.length?n.src(a.slice(1)):(n.changingSrc_=!1,n.setTimeout(function(){this.error({code:4,message:this.localize(this.options_.notSupportedMessage)})},0),void n.triggerReady());i=t,r=n.tech_,i.forEach(function(e){return e.setTech&&e.setTech(r)})})):this.setTimeout(function(){this.error({code:4,message:this.localize(this.options_.notSupportedMessage)})},0)},d.prototype.src_=function(e){var t,i,r=this,n=this.selectSource([e]);return!n||(t=n.tech,i=this.techName_,Be(t)!==Be(i)?(this.changingSrc_=!0,this.loadTech_(n.tech,n.source),this.tech_.ready(function(){r.changingSrc_=!1})):this.ready(function(){this.tech_.constructor.prototype.hasOwnProperty("setSource")?this.techCall_("setSource",e):this.techCall_("src",e.src),this.changingSrc_=!1},!0),!1)},d.prototype.load=function(){this.techCall_("load")},d.prototype.reset=function(){this.loadTech_(this.options_.techOrder[0],null),this.techCall_("reset")},d.prototype.currentSources=function(){var e=this.currentSource(),t=[];return 0!==Object.keys(e).length&&t.push(e),this.cache_.sources||t},d.prototype.currentSource=function(){return this.cache_.source||{}},d.prototype.currentSrc=function(){return this.currentSource()&&this.currentSource().src||""},d.prototype.currentType=function(){return this.currentSource()&&this.currentSource().type||""},d.prototype.preload=function(e){return void 0!==e?(this.techCall_("setPreload",e),void(this.options_.preload=e)):this.techGet_("preload")},d.prototype.autoplay=function(e){return void 0!==e?(this.techCall_("setAutoplay",e),void(this.options_.autoplay=e)):this.techGet_("autoplay",e)},d.prototype.playsinline=function(e){return void 0!==e?(this.techCall_("setPlaysinline",e),this.options_.playsinline=e,this):this.techGet_("playsinline")},d.prototype.loop=function(e){return void 0!==e?(this.techCall_("setLoop",e),void(this.options_.loop=e)):this.techGet_("loop")},d.prototype.poster=function(e){if(void 0===e)return this.poster_;e||(e=""),e!==this.poster_&&(this.poster_=e,this.techCall_("setPoster",e),this.isPosterFromTech_=!1,this.trigger("posterchange"))},d.prototype.handleTechPosterChange_=function(){if((!this.poster_||this.options_.techCanOverridePoster)&&this.tech_&&this.tech_.poster){var e=this.tech_.poster()||"";e!==this.poster_&&(this.poster_=e,this.isPosterFromTech_=!0,this.trigger("posterchange"))}},d.prototype.controls=function(e){if(void 0===e)return!!this.controls_;e=!!e,this.controls_!==e&&(this.controls_=e,this.usingNativeControls()&&this.techCall_("setControls",e),this.controls_?(this.removeClass("vjs-controls-disabled"),this.addClass("vjs-controls-enabled"),this.trigger("controlsenabled"),this.usingNativeControls()||this.addTechControlsListeners_()):(this.removeClass("vjs-controls-enabled"),this.addClass("vjs-controls-disabled"),this.trigger("controlsdisabled"),this.usingNativeControls()||this.removeTechControlsListeners_()))},d.prototype.usingNativeControls=function(e){if(void 0===e)return!!this.usingNativeControls_;e=!!e,this.usingNativeControls_!==e&&(this.usingNativeControls_=e,this.usingNativeControls_?(this.addClass("vjs-using-native-controls"),this.trigger("usingnativecontrols")):(this.removeClass("vjs-using-native-controls"),this.trigger("usingcustomcontrols")))},d.prototype.error=function(e){return void 0===e?this.error_||null:null===e?(this.error_=e,this.removeClass("vjs-error"),void(this.errorDisplay&&this.errorDisplay.close())):(this.error_=new Tt(e),this.addClass("vjs-error"),f.error("(CODE:"+this.error_.code+" "+Tt.errorTypes[this.error_.code]+")",this.error_.message,this.error_),void this.trigger("error"))},d.prototype.reportUserActivity=function(e){this.userActivity_=!0},d.prototype.userActive=function(e){if(void 0===e)return this.userActive_;if((e=!!e)!==this.userActive_){if(this.userActive_=e,this.userActive_)return this.userActivity_=!0,this.removeClass("vjs-user-inactive"),this.addClass("vjs-user-active"),void this.trigger("useractive");this.tech_&&this.tech_.one("mousemove",function(e){e.stopPropagation(),e.preventDefault()}),this.userActivity_=!1,this.removeClass("vjs-user-active"),this.addClass("vjs-user-inactive"),this.trigger("userinactive")}},d.prototype.listenForUserActivity_=function(){var t=void 0,i=void 0,r=void 0,n=ke(this,this.reportUserActivity);this.on("mousedown",function(){n(),this.clearInterval(t),t=this.setInterval(n,250)}),this.on("mousemove",function(e){e.screenX===i&&e.screenY===r||(i=e.screenX,r=e.screenY,n())}),this.on("mouseup",function(e){n(),this.clearInterval(t)}),this.on("keydown",n),this.on("keyup",n);var a=void 0;this.setInterval(function(){if(this.userActivity_){this.userActivity_=!1,this.userActive(!0),this.clearTimeout(a);var e=this.options_.inactivityTimeout;e<=0||(a=this.setTimeout(function(){this.userActivity_||this.userActive(!1)},e))}},250)},d.prototype.playbackRate=function(e){if(void 0===e)return this.tech_&&this.tech_.featuresPlaybackRate?this.cache_.lastPlaybackRate||this.techGet_("playbackRate"):1;this.techCall_("setPlaybackRate",e)},d.prototype.defaultPlaybackRate=function(e){return void 0!==e?this.techCall_("setDefaultPlaybackRate",e):this.tech_&&this.tech_.featuresPlaybackRate?this.techGet_("defaultPlaybackRate"):1},d.prototype.isAudio=function(e){if(void 0===e)return!!this.isAudio_;this.isAudio_=!!e},d.prototype.addTextTrack=function(e,t,i){if(this.tech_)return this.tech_.addTextTrack(e,t,i)},d.prototype.addRemoteTextTrack=function(e,t){if(this.tech_)return this.tech_.addRemoteTextTrack(e,t)},d.prototype.removeRemoteTextTrack=function(){var e=(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}).track,t=void 0===e?arguments[0]:e;if(this.tech_)return this.tech_.removeRemoteTextTrack(t)},d.prototype.getVideoPlaybackQuality=function(){return this.techGet_("getVideoPlaybackQuality")},d.prototype.videoWidth=function(){return this.tech_&&this.tech_.videoWidth&&this.tech_.videoWidth()||0},d.prototype.videoHeight=function(){return this.tech_&&this.tech_.videoHeight&&this.tech_.videoHeight()||0},d.prototype.language=function(e){if(void 0===e)return this.language_;this.language_=String(e).toLowerCase()},d.prototype.languages=function(){return je(d.prototype.options_.languages,this.languages_)},d.prototype.toJSON=function(){var e=je(this.options_),t=e.tracks;e.tracks=[];for(var i=0;i<t.length;i++){var r=t[i];(r=je(r)).player=void 0,e.tracks[i]=r}return e},d.prototype.createModal=function(e,t){var i=this;(t=t||{}).content=e||"";var r=new Ot(this,t);return this.addChild(r),r.on("dispose",function(){i.removeChild(r)}),r.open(),r},d.getTagSettings=function(e){var t={sources:[],tracks:[]},i=q(e),r=i["data-setup"];if(M(e,"vjs-fluid")&&(i.fluid=!0),null!==r){var n=Et(r||"{}"),a=n[0],s=n[1];a&&f.error(a),w(i,s)}if(w(t,i),e.hasChildNodes())for(var o=e.childNodes,u=0,c=o.length;u<c;u++){var l=o[u],d=l.nodeName.toLowerCase();"source"===d?t.sources.push(q(l)):"track"===d&&t.tracks.push(q(l))}return t},d.prototype.flexNotSupported_=function(){var e=p.createElement("i");return!("flexBasis"in e.style||"webkitFlexBasis"in e.style||"mozFlexBasis"in e.style||"msFlexBasis"in e.style||"msFlexOrder"in e.style)},d}(Fe);Pi.names.forEach(function(e){var t=Pi[e];Fr.prototype[t.getterName]=function(){return this.tech_?this.tech_[t.getterName]():(this[t.privateName]=this[t.privateName]||new t.ListClass,this[t.privateName])}}),Fr.players={};var Hr=g.navigator;Fr.prototype.options_={techOrder:hr.defaultTechOrder_,html5:{},flash:{},inactivityTimeout:2e3,playbackRates:[],children:["mediaLoader","posterImage","textTrackDisplay","loadingSpinner","bigPlayButton","controlBar","errorDisplay","textTrackSettings","resizeManager"],language:Hr&&(Hr.languages&&Hr.languages[0]||Hr.userLanguage||Hr.language)||"en",languages:{},notSupportedMessage:"No compatible source was found for this media."},["ended","seeking","seekable","networkState","readyState"].forEach(function(e){Fr.prototype[e]=function(){return this.techGet_(e)}}),Br.forEach(function(e){Fr.prototype["handleTech"+Be(e)+"_"]=function(){return this.trigger(e)}}),Fe.registerComponent("Player",Fr);var qr="plugin",Vr="activePlugins_",Wr={},Gr=function(e){return Wr.hasOwnProperty(e)},zr=function(e){return Gr(e)?Wr[e]:void 0},Xr=function(e,t){e[Vr]=e[Vr]||{},e[Vr][t]=!0},Yr=function(e,t,i){var r=(i?"before":"")+"pluginsetup";e.trigger(r,t),e.trigger(r+":"+t.name,t)},$r=function(n,a){return a.prototype.name=n,function(){Yr(this,{name:n,plugin:a,instance:null},!0);for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];var r=new(Function.prototype.bind.apply(a,[null].concat([this].concat(t))));return this[n]=function(){return r},Yr(this,r.getEventHash()),r}},Kr=function(){function a(e){if(y(this,a),this.constructor===a)throw new Error("Plugin must be sub-classed; not directly instantiated.");this.player=e,Ue(this),delete this.trigger,Me(this,this.constructor.defaultState),Xr(e,this.name),this.dispose=ke(this,this.dispose),e.on("dispose",this.dispose)}return a.prototype.version=function(){return this.constructor.VERSION},a.prototype.getEventHash=function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return e.name=this.name,e.plugin=this.constructor,e.instance=this,e},a.prototype.trigger=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};return ge(this.eventBusEl_,e,this.getEventHash(t))},a.prototype.handleStateChanged=function(e){},a.prototype.dispose=function(){var e=this.name,t=this.player;this.trigger("dispose"),this.off(),t.off("dispose",this.dispose),t[Vr][e]=!1,this.player=this.state=null,t[e]=$r(e,Wr[e])},a.isBasic=function(e){var t="string"==typeof e?zr(e):e;return"function"==typeof t&&!a.prototype.isPrototypeOf(t.prototype)},a.registerPlugin=function(e,t){if("string"!=typeof e)throw new Error('Illegal plugin name, "'+e+'", must be a string, was '+("undefined"==typeof e?"undefined":v(e))+".");if(Gr(e))f.warn('A plugin named "'+e+'" already exists. You may want to avoid re-registering plugins!');else if(Fr.prototype.hasOwnProperty(e))throw new Error('Illegal plugin name, "'+e+'", cannot share a name with an existing player method!');if("function"!=typeof t)throw new Error('Illegal plugin for "'+e+'", must be a function, was '+("undefined"==typeof t?"undefined":v(t))+".");var i,r,n;return Wr[e]=t,e!==qr&&(a.isBasic(t)?Fr.prototype[e]=(i=e,r=t,n=function(){Yr(this,{name:i,plugin:r,instance:null},!0);var e=r.apply(this,arguments);return Xr(this,i),Yr(this,{name:i,plugin:r,instance:e}),e},Object.keys(r).forEach(function(e){n[e]=r[e]}),n):Fr.prototype[e]=$r(e,t)),t},a.deregisterPlugin=function(e){if(e===qr)throw new Error("Cannot de-register base plugin.");Gr(e)&&(delete Wr[e],delete Fr.prototype[e])},a.getPlugins=function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:Object.keys(Wr),i=void 0;return e.forEach(function(e){var t=zr(e);t&&((i=i||{})[e]=t)}),i},a.getPluginVersion=function(e){var t=zr(e);return t&&t.VERSION||""},a}();Kr.getPlugin=zr,Kr.BASE_PLUGIN_NAME=qr,Kr.registerPlugin(qr,Kr),Fr.prototype.usingPlugin=function(e){return!!this[Vr]&&!0===this[Vr][e]},Fr.prototype.hasPlugin=function(e){return!!Gr(e)};var Qr=function(e,t){e=e<0?0:e;var i=Math.floor(e%60),r=Math.floor(e/60%60),n=Math.floor(e/3600),a=Math.floor(t/60%60),s=Math.floor(t/3600);return(isNaN(e)||e===1/0)&&(n=r=i="-"),(n=0<n||0<s?n+":":"")+(r=((n||10<=a)&&r<10?"0"+r:r)+":")+(i=i<10?"0"+i:i)},Jr=Qr;var Zr=function(e){return 0===e.indexOf("#")?e.slice(1):e};function en(e,i,t){var r=en.getPlayer(e);if(r)return i&&f.warn('Player "'+e+'" is already initialised. Options will not be applied.'),t&&r.ready(t),r;var n="string"==typeof e?Z("#"+Zr(e)):e;if(!R(n))throw new TypeError("The element or ID supplied is not valid. (videojs)");p.body.contains(n)||f.warn("The element supplied is not included in the DOM"),i=i||{},en.hooks("beforesetup").forEach(function(e){var t=e(n,je(i));k(t)&&!Array.isArray(t)?i=je(i,t):f.error("please return an object in beforesetup hooks")});var a=Fe.getComponent("Player");return r=new a(n,i,t),en.hooks("setup").forEach(function(e){return e(r)}),r}if(en.hooks_={},en.hooks=function(e,t){return en.hooks_[e]=en.hooks_[e]||[],t&&(en.hooks_[e]=en.hooks_[e].concat(t)),en.hooks_[e]},en.hook=function(e,t){en.hooks(e,t)},en.hookOnce=function(i,e){en.hooks(i,[].concat(e).map(function(t){return function e(){return en.removeHook(i,e),t.apply(void 0,arguments)}}))},en.removeHook=function(e,t){var i=en.hooks(e).indexOf(t);return!(i<=-1)&&(en.hooks_[e]=en.hooks_[e].slice(),en.hooks_[e].splice(i,1),!0)},!0!==g.VIDEOJS_NO_DYNAMIC_STYLE&&I()){var tn=Z(".vjs-styles-defaults");if(!tn){tn=Ee("vjs-styles-defaults");var rn=Z("head");rn&&rn.insertBefore(tn,rn.firstChild),we(tn,"\n      .video-js {\n        width: 300px;\n        height: 150px;\n      }\n\n      .vjs-fluid {\n        padding-top: 56.25%\n      }\n    ")}}Se(1,en),en.VERSION=h,en.options=Fr.prototype.options_,en.getPlayers=function(){return Fr.players},en.getPlayer=function(e){var t=Fr.players,i=void 0;if("string"==typeof e){var r=Zr(e),n=t[r];if(n)return n;i=Z("#"+r)}else i=e;if(R(i)){var a=i,s=a.player,o=a.playerId;if(s||t[o])return s||t[o]}},en.getAllPlayers=function(){return Object.keys(Fr.players).map(function(e){return Fr.players[e]}).filter(Boolean)},en.players=Fr.players,en.getComponent=Fe.getComponent,en.registerComponent=function(e,t){hr.isTech(t)&&f.warn("The "+e+" tech was registered as a component. It should instead be registered using videojs.registerTech(name, tech)"),Fe.registerComponent.call(Fe,e,t)},en.getTech=hr.getTech,en.registerTech=hr.registerTech,en.use=function(e,t){pr[e]=pr[e]||[],pr[e].push(t)},Object.defineProperty(en,"middleware",{value:{},writeable:!1,enumerable:!0}),Object.defineProperty(en.middleware,"TERMINATOR",{value:mr,writeable:!1,enumerable:!0}),en.browser=lt,en.TOUCH_ENABLED=ct,en.extend=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},i=function(){e.apply(this,arguments)},r={};for(var n in"object"===("undefined"==typeof t?"undefined":v(t))?(t.constructor!==Object.prototype.constructor&&(i=t.constructor),r=t):"function"==typeof t&&(i=t),function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+("undefined"==typeof t?"undefined":v(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(e.super_=t)}(i,e),r)r.hasOwnProperty(n)&&(i.prototype[n]=r[n]);return i},en.mergeOptions=je,en.bind=ke,en.registerPlugin=Kr.registerPlugin,en.plugin=function(e,t){return f.warn("videojs.plugin() is deprecated; use videojs.registerPlugin() instead"),Kr.registerPlugin(e,t)},en.getPlugins=Kr.getPlugins,en.getPlugin=Kr.getPlugin,en.getPluginVersion=Kr.getPluginVersion,en.addLanguage=function(e,t){var i;return e=(""+e).toLowerCase(),en.options.languages=je(en.options.languages,((i={})[e]=t,i)),en.options.languages[e]},en.log=f,en.createTimeRange=en.createTimeRanges=pt,en.formatTime=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:e;return Jr(e,t)},en.setFormatTime=function(e){Jr=e},en.resetFormatTime=function(){Jr=Qr},en.parseUrl=Wt,en.isCrossOrigin=Xt,en.EventTarget=Ce,en.on=fe,en.one=ye,en.off=me,en.trigger=ge,en.xhr=gi,en.TextTrack=Si,en.AudioTrack=Ei,en.VideoTrack=wi,["isEl","isTextNode","createEl","hasClass","addClass","removeClass","toggleClass","setAttributes","getAttributes","emptyEl","appendContent","insertContent"].forEach(function(e){en[e]=function(){return f.warn("videojs."+e+"() is deprecated; use videojs.dom."+e+"() instead"),te[e].apply(null,arguments)}}),en.computedStyle=A,en.dom=te,en.url=Yt;var nn=t(function(e,t){var i,l,r,n,d;i=/^((?:[a-zA-Z0-9+\-.]+:)?)(\/\/[^\/\;?#]*)?(.*?)??(;.*?)?(\?.*?)?(#.*?)?$/,l=/^([^\/;?#]*)(.*)$/,r=/(?:\/|^)\.(?=\/)/g,n=/(?:\/|^)\.\.\/(?!\.\.\/).*?(?=\/)/g,d={buildAbsoluteURL:function(e,t,i){if(i=i||{},e=e.trim(),!(t=t.trim())){if(!i.alwaysNormalize)return e;var r=d.parseURL(e);if(!r)throw new Error("Error trying to parse base URL.");return r.path=d.normalizePath(r.path),d.buildURLFromParts(r)}var n=d.parseURL(t);if(!n)throw new Error("Error trying to parse relative URL.");if(n.scheme)return i.alwaysNormalize?(n.path=d.normalizePath(n.path),d.buildURLFromParts(n)):t;var a=d.parseURL(e);if(!a)throw new Error("Error trying to parse base URL.");if(!a.netLoc&&a.path&&"/"!==a.path[0]){var s=l.exec(a.path);a.netLoc=s[1],a.path=s[2]}a.netLoc&&!a.path&&(a.path="/");var o={scheme:a.scheme,netLoc:n.netLoc,path:null,params:n.params,query:n.query,fragment:n.fragment};if(!n.netLoc&&(o.netLoc=a.netLoc,"/"!==n.path[0]))if(n.path){var u=a.path,c=u.substring(0,u.lastIndexOf("/")+1)+n.path;o.path=d.normalizePath(c)}else o.path=a.path,n.params||(o.params=a.params,n.query||(o.query=a.query));return null===o.path&&(o.path=i.alwaysNormalize?d.normalizePath(n.path):n.path),d.buildURLFromParts(o)},parseURL:function(e){var t=i.exec(e);return t?{scheme:t[1]||"",netLoc:t[2]||"",path:t[3]||"",params:t[4]||"",query:t[5]||"",fragment:t[6]||""}:null},normalizePath:function(e){for(e=e.split("").reverse().join("").replace(r,"");e.length!==(e=e.replace(n,"")).length;);return e.split("").reverse().join("")},buildURLFromParts:function(e){return e.scheme+e.netLoc+e.path+e.params+e.query+e.fragment}},e.exports=d}),an=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},sn=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(e[r]=i[r])}return e},on=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+("undefined"==typeof t?"undefined":v(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)},un=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==("undefined"==typeof t?"undefined":v(t))&&"function"!=typeof t?e:t},cn=function(){function e(){an(this,e),this.listeners={}}return e.prototype.on=function(e,t){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push(t)},e.prototype.off=function(e,t){if(!this.listeners[e])return!1;var i=this.listeners[e].indexOf(t);return this.listeners[e].splice(i,1),-1<i},e.prototype.trigger=function(e){var t=this.listeners[e],i=void 0,r=void 0,n=void 0;if(t)if(2===arguments.length)for(r=t.length,i=0;i<r;++i)t[i].call(this,arguments[1]);else for(n=Array.prototype.slice.call(arguments,1),r=t.length,i=0;i<r;++i)t[i].apply(this,n)},e.prototype.dispose=function(){this.listeners={}},e.prototype.pipe=function(t){this.on("data",function(e){t.push(e)})},e}(),ln=function(t){function i(){an(this,i);var e=un(this,t.call(this));return e.buffer="",e}return on(i,t),i.prototype.push=function(e){var t=void 0;for(this.buffer+=e,t=this.buffer.indexOf("\n");-1<t;t=this.buffer.indexOf("\n"))this.trigger("data",this.buffer.substring(0,t)),this.buffer=this.buffer.substring(t+1)},i}(cn),dn=function(e){for(var t=e.split(new RegExp('(?:^|,)((?:[^=]*)=(?:"[^"]*"|[^,]*))')),i={},r=t.length,n=void 0;r--;)""!==t[r]&&((n=/([^=]*)=(.*)/.exec(t[r]).slice(1))[0]=n[0].replace(/^\s+|\s+$/g,""),n[1]=n[1].replace(/^\s+|\s+$/g,""),n[1]=n[1].replace(/^['"](.*)['"]$/g,"$1"),i[n[0]]=n[1]);return i},hn=function(t){function i(){an(this,i);var e=un(this,t.call(this));return e.customParsers=[],e}return on(i,t),i.prototype.push=function(e){var t=void 0,i=void 0;if(0!==(e=e.replace(/^[\u0000\s]+|[\u0000\s]+$/g,"")).length)if("#"===e[0]){for(var r=0;r<this.customParsers.length;r++)if(this.customParsers[r].call(this,e))return;if(0===e.indexOf("#EXT"))if(e=e.replace("\r",""),t=/^#EXTM3U/.exec(e))this.trigger("data",{type:"tag",tagType:"m3u"});else{if(t=/^#EXTINF:?([0-9\.]*)?,?(.*)?$/.exec(e))return i={type:"tag",tagType:"inf"},t[1]&&(i.duration=parseFloat(t[1])),t[2]&&(i.title=t[2]),void this.trigger("data",i);if(t=/^#EXT-X-TARGETDURATION:?([0-9.]*)?/.exec(e))return i={type:"tag",tagType:"targetduration"},t[1]&&(i.duration=parseInt(t[1],10)),void this.trigger("data",i);if(t=/^#ZEN-TOTAL-DURATION:?([0-9.]*)?/.exec(e))return i={type:"tag",tagType:"totalduration"},t[1]&&(i.duration=parseInt(t[1],10)),void this.trigger("data",i);if(t=/^#EXT-X-VERSION:?([0-9.]*)?/.exec(e))return i={type:"tag",tagType:"version"},t[1]&&(i.version=parseInt(t[1],10)),void this.trigger("data",i);if(t=/^#EXT-X-MEDIA-SEQUENCE:?(\-?[0-9.]*)?/.exec(e))return i={type:"tag",tagType:"media-sequence"},t[1]&&(i.number=parseInt(t[1],10)),void this.trigger("data",i);if(t=/^#EXT-X-DISCONTINUITY-SEQUENCE:?(\-?[0-9.]*)?/.exec(e))return i={type:"tag",tagType:"discontinuity-sequence"},t[1]&&(i.number=parseInt(t[1],10)),void this.trigger("data",i);if(t=/^#EXT-X-PLAYLIST-TYPE:?(.*)?$/.exec(e))return i={type:"tag",tagType:"playlist-type"},t[1]&&(i.playlistType=t[1]),void this.trigger("data",i);if(t=/^#EXT-X-BYTERANGE:?([0-9.]*)?@?([0-9.]*)?/.exec(e))return i={type:"tag",tagType:"byterange"},t[1]&&(i.length=parseInt(t[1],10)),t[2]&&(i.offset=parseInt(t[2],10)),void this.trigger("data",i);if(t=/^#EXT-X-ALLOW-CACHE:?(YES|NO)?/.exec(e))return i={type:"tag",tagType:"allow-cache"},t[1]&&(i.allowed=!/NO/.test(t[1])),void this.trigger("data",i);if(t=/^#EXT-X-MAP:?(.*)$/.exec(e)){if(i={type:"tag",tagType:"map"},t[1]){var n=dn(t[1]);if(n.URI&&(i.uri=n.URI),n.BYTERANGE){var a=n.BYTERANGE.split("@"),s=a[0],o=a[1];i.byterange={},s&&(i.byterange.length=parseInt(s,10)),o&&(i.byterange.offset=parseInt(o,10))}}this.trigger("data",i)}else if(t=/^#EXT-X-STREAM-INF:?(.*)$/.exec(e)){if(i={type:"tag",tagType:"stream-inf"},t[1]){if(i.attributes=dn(t[1]),i.attributes.RESOLUTION){var u=i.attributes.RESOLUTION.split("x"),c={};u[0]&&(c.width=parseInt(u[0],10)),u[1]&&(c.height=parseInt(u[1],10)),i.attributes.RESOLUTION=c}i.attributes.BANDWIDTH&&(i.attributes.BANDWIDTH=parseInt(i.attributes.BANDWIDTH,10)),i.attributes["PROGRAM-ID"]&&(i.attributes["PROGRAM-ID"]=parseInt(i.attributes["PROGRAM-ID"],10))}this.trigger("data",i)}else{if(t=/^#EXT-X-MEDIA:?(.*)$/.exec(e))return i={type:"tag",tagType:"media"},t[1]&&(i.attributes=dn(t[1])),void this.trigger("data",i);if(t=/^#EXT-X-ENDLIST/.exec(e))this.trigger("data",{type:"tag",tagType:"endlist"});else if(t=/^#EXT-X-DISCONTINUITY/.exec(e))this.trigger("data",{type:"tag",tagType:"discontinuity"});else{if(t=/^#EXT-X-PROGRAM-DATE-TIME:?(.*)$/.exec(e))return i={type:"tag",tagType:"program-date-time"},t[1]&&(i.dateTimeString=t[1],i.dateTimeObject=new Date(t[1])),void this.trigger("data",i);if(t=/^#EXT-X-KEY:?(.*)$/.exec(e))return i={type:"tag",tagType:"key"},t[1]&&(i.attributes=dn(t[1]),i.attributes.IV&&("0x"===i.attributes.IV.substring(0,2).toLowerCase()&&(i.attributes.IV=i.attributes.IV.substring(2)),i.attributes.IV=i.attributes.IV.match(/.{8}/g),i.attributes.IV[0]=parseInt(i.attributes.IV[0],16),i.attributes.IV[1]=parseInt(i.attributes.IV[1],16),i.attributes.IV[2]=parseInt(i.attributes.IV[2],16),i.attributes.IV[3]=parseInt(i.attributes.IV[3],16),i.attributes.IV=new Uint32Array(i.attributes.IV))),void this.trigger("data",i);if(t=/^#EXT-X-START:?(.*)$/.exec(e))return i={type:"tag",tagType:"start"},t[1]&&(i.attributes=dn(t[1]),i.attributes["TIME-OFFSET"]=parseFloat(i.attributes["TIME-OFFSET"]),i.attributes.PRECISE=/YES/.test(i.attributes.PRECISE)),void this.trigger("data",i);if(t=/^#EXT-X-CUE-OUT-CONT:?(.*)?$/.exec(e))return i={type:"tag",tagType:"cue-out-cont"},t[1]?i.data=t[1]:i.data="",void this.trigger("data",i);if(t=/^#EXT-X-CUE-OUT:?(.*)?$/.exec(e))return i={type:"tag",tagType:"cue-out"},t[1]?i.data=t[1]:i.data="",void this.trigger("data",i);if(t=/^#EXT-X-CUE-IN:?(.*)?$/.exec(e))return i={type:"tag",tagType:"cue-in"},t[1]?i.data=t[1]:i.data="",void this.trigger("data",i);this.trigger("data",{type:"tag",data:e.slice(4)})}}}else this.trigger("data",{type:"comment",text:e.slice(1)})}else this.trigger("data",{type:"uri",uri:e})},i.prototype.addParser=function(e){var t=this,i=e.expression,r=e.customType,n=e.dataParser,a=e.segment;"function"!=typeof n&&(n=function(e){return e}),this.customParsers.push(function(e){if(i.exec(e))return t.trigger("data",{type:"custom",data:n(e),customType:r,segment:a}),!0})},i}(cn),pn=function(t){function i(){an(this,i);var e=un(this,t.call(this));e.lineStream=new ln,e.parseStream=new hn,e.lineStream.pipe(e.parseStream);var n=e,a=[],s={},o=void 0,u=void 0,c={AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},l=0;return e.manifest={allowCache:!0,discontinuityStarts:[],segments:[]},e.parseStream.on("data",function(t){var i=void 0,r=void 0;({tag:function(){({"allow-cache":function(){this.manifest.allowCache=t.allowed,"allowed"in t||(this.trigger("info",{message:"defaulting allowCache to YES"}),this.manifest.allowCache=!0)},byterange:function(){var e={};"length"in t&&((s.byterange=e).length=t.length,"offset"in t||(this.trigger("info",{message:"defaulting offset to zero"}),t.offset=0)),"offset"in t&&((s.byterange=e).offset=t.offset)},endlist:function(){this.manifest.endList=!0},inf:function(){"mediaSequence"in this.manifest||(this.manifest.mediaSequence=0,this.trigger("info",{message:"defaulting media sequence to zero"})),"discontinuitySequence"in this.manifest||(this.manifest.discontinuitySequence=0,this.trigger("info",{message:"defaulting discontinuity sequence to zero"})),0<t.duration&&(s.duration=t.duration),0===t.duration&&(s.duration=.01,this.trigger("info",{message:"updating zero segment duration to a small value"})),this.manifest.segments=a},key:function(){t.attributes?"NONE"!==t.attributes.METHOD?t.attributes.URI?(t.attributes.METHOD||this.trigger("warn",{message:"defaulting key method to AES-128"}),u={method:t.attributes.METHOD||"AES-128",uri:t.attributes.URI},"undefined"!=typeof t.attributes.IV&&(u.iv=t.attributes.IV)):this.trigger("warn",{message:"ignoring key declaration without URI"}):u=null:this.trigger("warn",{message:"ignoring key declaration without attribute list"})},"media-sequence":function(){isFinite(t.number)?this.manifest.mediaSequence=t.number:this.trigger("warn",{message:"ignoring invalid media sequence: "+t.number})},"discontinuity-sequence":function(){isFinite(t.number)?(this.manifest.discontinuitySequence=t.number,l=t.number):this.trigger("warn",{message:"ignoring invalid discontinuity sequence: "+t.number})},"playlist-type":function(){/VOD|EVENT/.test(t.playlistType)?this.manifest.playlistType=t.playlistType:this.trigger("warn",{message:"ignoring unknown playlist type: "+t.playlist})},map:function(){o={},t.uri&&(o.uri=t.uri),t.byterange&&(o.byterange=t.byterange)},"stream-inf":function(){this.manifest.playlists=a,this.manifest.mediaGroups=this.manifest.mediaGroups||c,t.attributes?(s.attributes||(s.attributes={}),sn(s.attributes,t.attributes)):this.trigger("warn",{message:"ignoring empty stream-inf attributes"})},media:function(){if(this.manifest.mediaGroups=this.manifest.mediaGroups||c,t.attributes&&t.attributes.TYPE&&t.attributes["GROUP-ID"]&&t.attributes.NAME){var e=this.manifest.mediaGroups[t.attributes.TYPE];e[t.attributes["GROUP-ID"]]=e[t.attributes["GROUP-ID"]]||{},i=e[t.attributes["GROUP-ID"]],(r={default:/yes/i.test(t.attributes.DEFAULT)}).default?r.autoselect=!0:r.autoselect=/yes/i.test(t.attributes.AUTOSELECT),t.attributes.LANGUAGE&&(r.language=t.attributes.LANGUAGE),t.attributes.URI&&(r.uri=t.attributes.URI),t.attributes["INSTREAM-ID"]&&(r.instreamId=t.attributes["INSTREAM-ID"]),t.attributes.CHARACTERISTICS&&(r.characteristics=t.attributes.CHARACTERISTICS),t.attributes.FORCED&&(r.forced=/yes/i.test(t.attributes.FORCED)),i[t.attributes.NAME]=r}else this.trigger("warn",{message:"ignoring incomplete or missing media group"})},discontinuity:function(){l+=1,s.discontinuity=!0,this.manifest.discontinuityStarts.push(a.length)},"program-date-time":function(){"undefined"==typeof this.manifest.dateTimeString&&(this.manifest.dateTimeString=t.dateTimeString,this.manifest.dateTimeObject=t.dateTimeObject),s.dateTimeString=t.dateTimeString,s.dateTimeObject=t.dateTimeObject},targetduration:function(){!isFinite(t.duration)||t.duration<0?this.trigger("warn",{message:"ignoring invalid target duration: "+t.duration}):this.manifest.targetDuration=t.duration},totalduration:function(){!isFinite(t.duration)||t.duration<0?this.trigger("warn",{message:"ignoring invalid total duration: "+t.duration}):this.manifest.totalDuration=t.duration},start:function(){t.attributes&&!isNaN(t.attributes["TIME-OFFSET"])?this.manifest.start={timeOffset:t.attributes["TIME-OFFSET"],precise:t.attributes.PRECISE}:this.trigger("warn",{message:"ignoring start declaration without appropriate attribute list"})},"cue-out":function(){s.cueOut=t.data},"cue-out-cont":function(){s.cueOutCont=t.data},"cue-in":function(){s.cueIn=t.data}}[t.tagType]||function(){}).call(n)},uri:function(){s.uri=t.uri,a.push(s),!this.manifest.targetDuration||"duration"in s||(this.trigger("warn",{message:"defaulting segment duration to the target duration"}),s.duration=this.manifest.targetDuration),u&&(s.key=u),s.timeline=l,o&&(s.map=o),s={}},comment:function(){},custom:function(){t.segment?(s.custom=s.custom||{},s.custom[t.customType]=t.data):(this.manifest.custom=this.manifest.custom||{},this.manifest.custom[t.customType]=t.data)}})[t.type].call(n)}),e}return on(i,t),i.prototype.push=function(e){this.lineStream.push(e)},i.prototype.end=function(){this.lineStream.push("\n")},i.prototype.addParser=function(e){this.parseStream.addParser(e)},i}(cn),fn=function(e){var t,i=e.attributes,r=e.segments,n={attributes:(t={NAME:i.id,AUDIO:"audio",SUBTITLES:"subs",RESOLUTION:{width:i.width,height:i.height},CODECS:i.codecs,BANDWIDTH:i.bandwidth},t["PROGRAM-ID"]=1,t),uri:"",endList:"static"===(i.type||"static"),timeline:i.periodIndex,resolvedUri:"",targetDuration:i.duration,segments:r,mediaSequence:r.length?r[0].number:1};return i.contentProtection&&(n.contentProtection=i.contentProtection),n},mn=function(e){return!!e&&"object"===("undefined"==typeof e?"undefined":v(e))},gn=function r(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return t.reduce(function(t,i){return Object.keys(i).forEach(function(e){Array.isArray(t[e])&&Array.isArray(i[e])?t[e]=t[e].concat(i[e]):mn(t[e])&&mn(i[e])?t[e]=r(t[e],i[e]):t[e]=i[e]}),t},{})},yn=function(e,t){return/^[a-z]+:/i.test(t)?t:(/\/\//i.test(e)||(e=nn.buildAbsoluteURL(g.location.href,e)),nn.buildAbsoluteURL(e,t))},vn=function(e){var t=e.baseUrl,i=void 0===t?"":t,r=e.source,n=void 0===r?"":r,a=e.range,s=void 0===a?"":a,o={uri:n,resolvedUri:yn(i||"",n)};if(s){var u=s.split("-"),c=parseInt(u[0],10),l=parseInt(u[1],10);o.byterange={length:l-c,offset:c}}return o},_n=function(e,t){for(var i,r,n,a,s,o,u,c,l,d,h,p,f=e.type,m=void 0===f?"static":f,g=e.minimumUpdatePeriod,y=void 0===g?0:g,v=e.media,_=void 0===v?"":v,b=e.sourceDuration,T=e.timescale,S=void 0===T?1:T,E=e.startNumber,w=void 0===E?1:E,k=e.periodIndex,C=[],A=-1,L=0;L<t.length;L++){var O=t[L],P=O.d,I=O.r||0,R=O.t||0;A<0&&(A=R),R&&A<R&&(A=R);var x=void 0;if(I<0){var D=L+1;D===t.length?"dynamic"===m&&0<y&&0<_.indexOf("$Number$")?(r=A,n=P,void 0,a=(i=e).NOW,s=i.clientOffset,o=i.availabilityStartTime,u=i.timescale,c=void 0===u?1:u,l=i.start,d=void 0===l?0:l,h=i.minimumUpdatePeriod,p=(a+s)/1e3+(void 0===h?0:h)-(o+d),x=Math.ceil((p*c-r)/n)):x=(b*S-A)/P:x=(t[D].t-A)/P}else x=I+1;for(var U=w+C.length+x,N=w+C.length;N<U;)C.push({number:N,duration:P/S,time:A,timeline:k}),A+=P,N++}return C},bn=function(e){return e.reduce(function(e,t){return e.concat(t)},[])},Tn=function(e){if(!e.length)return[];for(var t=[],i=0;i<e.length;i++)t.push(e[i]);return t},Sn={static:function(e){var t=e.duration,i=e.timescale,r=void 0===i?1:i,n=e.sourceDuration;return{start:0,end:Math.ceil(n/(t/r))}},dynamic:function(e){var t=e.NOW,i=e.clientOffset,r=e.availabilityStartTime,n=e.timescale,a=void 0===n?1:n,s=e.duration,o=e.start,u=void 0===o?0:o,c=e.minimumUpdatePeriod,l=void 0===c?0:c,d=e.timeShiftBufferDepth,h=void 0===d?1/0:d,p=(t+i)/1e3,f=r+u,m=p+l-f,g=Math.ceil(m*a/s),y=Math.floor((p-f-h)*a/s),v=Math.floor((p-f)*a/s);return{start:Math.max(0,y),end:Math.min(g,v)}}},En=function(e){var o,t=e.type,i=void 0===t?"static":t,r=e.duration,n=e.timescale,a=void 0===n?1:n,s=e.sourceDuration,u=Sn[i](e),c=function(e,t){for(var i=[],r=e;r<t;r++)i.push(r);return i}(u.start,u.end).map((o=e,function(e,t){var i=o.duration,r=o.timescale,n=void 0===r?1:r,a=o.periodIndex,s=o.startNumber;return{number:(void 0===s?1:s)+e,duration:i/n,timeline:a,time:t*i}}));if("static"===i){var l=c.length-1;c[l].duration=s-r/a*l}return c},wn=/\$([A-z]*)(?:(%0)([0-9]+)d)?\$/g,kn=function(e,t){return e.replace(wn,(a=t,function(e,t,i,r){if("$$"===e)return"$";if("undefined"==typeof a[t])return e;var n=""+a[t];return"RepresentationID"===t?n:(r=i?parseInt(r,10):1)<=n.length?n:""+new Array(r-n.length+1).join("0")+n}));var a},Cn=function(i,e){var t,r,n={RepresentationID:i.id,Bandwidth:i.bandwidth||0},a=i.initialization,s=void 0===a?{sourceURL:"",range:""}:a,o=vn({baseUrl:i.baseUrl,source:kn(s.sourceURL,n),range:s.range});return(r=e,(t=i).duration||r?t.duration?En(t):_n(t,r):[{number:t.startNumber||1,duration:t.sourceDuration,time:0,timeline:t.periodIndex}]).map(function(e){n.Number=e.number,n.Time=e.time;var t=kn(i.media||"",n);return{uri:t,timeline:e.timeline,duration:e.duration,resolvedUri:yn(i.baseUrl||"",t),map:o,number:e.number}})},An="INVALID_NUMBER_OF_PERIOD",Ln="DASH_EMPTY_MANIFEST",On="DASH_INVALID_XML",Pn="NO_BASE_URL",In="SEGMENT_TIME_UNSPECIFIED",Rn="UNSUPPORTED_UTC_TIMING_SCHEME",xn=function(u,e){var t=u.duration,i=u.segmentUrls,r=void 0===i?[]:i;if(!t&&!e||t&&e)throw new Error(In);var n=r.map(function(e){return i=e,r=(t=u).baseUrl,n=t.initialization,s=vn({baseUrl:r,source:(a=void 0===n?{}:n).sourceURL,range:a.range}),(o=vn({baseUrl:r,source:i.media,range:i.mediaRange})).map=s,o;var t,i,r,n,a,s,o}),a=void 0;return t&&(a=En(u)),e&&(a=_n(u,e)),a.map(function(e,t){if(n[t]){var i=n[t];return i.timeline=e.timeline,i.duration=e.duration,i.number=e.number,i}}).filter(function(e){return e})},Dn=function(e){var t=e.baseUrl,i=e.initialization,r=void 0===i?{}:i,n=e.sourceDuration,a=e.timescale,s=void 0===a?1:a,o=e.indexRange,u=void 0===o?"":o,c=e.duration;if(!t)throw new Error(Pn);var l=vn({baseUrl:t,source:r.sourceURL,range:r.range}),d=vn({baseUrl:t,source:t,range:u});if(d.map=l,c){var h=En(e);h.length&&(d.duration=h[0].duration,d.timeline=h[0].timeline)}else n&&(d.duration=n/s,d.timeline=0);return d.number=0,[d]},Un=function(e){var t=e.attributes,i=e.segmentInfo,r=void 0,n=void 0;if(i.template?(n=Cn,r=gn(t,i.template)):i.base?(n=Dn,r=gn(t,i.base)):i.list&&(n=xn,r=gn(t,i.list)),!n)return{attributes:t};var a=n(r,i.timeline);if(r.duration){var s=r,o=s.duration,u=s.timescale,c=void 0===u?1:u;r.duration=o/c}else a.length?r.duration=a.reduce(function(e,t){return Math.max(e,Math.ceil(t.duration))},0):r.duration=0;return{attributes:r,segments:a}},Nn=function(e,t){return Tn(e.childNodes).filter(function(e){return e.tagName===t})},Mn=function(e){return e.textContent.trim()},Bn=function(e){var t=/P(?:(\d*)Y)?(?:(\d*)M)?(?:(\d*)D)?(?:T(?:(\d*)H)?(?:(\d*)M)?(?:([\d.]*)S)?)?/.exec(e);if(!t)return 0;var i=t.slice(1),r=i[0],n=i[1],a=i[2],s=i[3],o=i[4],u=i[5];return 31536e3*parseFloat(r||0)+2592e3*parseFloat(n||0)+86400*parseFloat(a||0)+3600*parseFloat(s||0)+60*parseFloat(o||0)+parseFloat(u||0)},jn={mediaPresentationDuration:function(e){return Bn(e)},availabilityStartTime:function(e){return/^\d+-\d+-\d+T\d+:\d+:\d+(\.\d+)?$/.test(t=e)&&(t+="Z"),Date.parse(t)/1e3;var t},minimumUpdatePeriod:function(e){return Bn(e)},timeShiftBufferDepth:function(e){return Bn(e)},start:function(e){return Bn(e)},width:function(e){return parseInt(e,10)},height:function(e){return parseInt(e,10)},bandwidth:function(e){return parseInt(e,10)},startNumber:function(e){return parseInt(e,10)},timescale:function(e){return parseInt(e,10)},duration:function(e){var t=parseInt(e,10);return isNaN(t)?Bn(e):t},d:function(e){return parseInt(e,10)},t:function(e){return parseInt(e,10)},r:function(e){return parseInt(e,10)},DEFAULT:function(e){return e}},Fn=function(e){return e&&e.attributes?Tn(e.attributes).reduce(function(e,t){var i=jn[t.name]||jn.DEFAULT;return e[t.name]=i(t.value),e},{}):{}};var Hn,qn,Vn={"urn:uuid:1077efec-c0b2-4d02-ace3-3c1e52e2fb4b":"org.w3.clearkey","urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed":"com.widevine.alpha","urn:uuid:9a04f079-9840-4286-ab92-e65be0885f95":"com.microsoft.playready","urn:uuid:f239e769-efa3-4850-9c16-a903c6932efb":"com.adobe.primetime"},Wn=function(e,i){return i.length?bn(e.map(function(t){return i.map(function(e){return yn(t,Mn(e))})})):e},Gn=function(e){var t=Nn(e,"SegmentTemplate")[0],i=Nn(e,"SegmentList")[0],r=i&&Nn(i,"SegmentURL").map(function(e){return gn({tag:"SegmentURL"},Fn(e))}),n=Nn(e,"SegmentBase")[0],a=i||t,s=a&&Nn(a,"SegmentTimeline")[0],o=i||n||t,u=o&&Nn(o,"Initialization")[0],c=t&&Fn(t);c&&u?c.initialization=u&&Fn(u):c&&c.initialization&&(c.initialization={sourceURL:c.initialization});var l={template:c,timeline:s&&Nn(s,"S").map(function(e){return Fn(e)}),list:i&&gn(Fn(i),{segmentUrls:r,initialization:Fn(u)}),base:n&&gn(Fn(n),{initialization:Fn(u)})};return Object.keys(l).forEach(function(e){l[e]||delete l[e]}),l},zn=function(e){return e.reduce(function(e,t){var i=Fn(t),r=Vn[i.schemeIdUri];if(r){e[r]={attributes:i};var n=Nn(t,"cenc:pssh")[0];if(n){var a=Mn(n),s=a&&function(e){for(var t=g.atob(e),i=new Uint8Array(t.length),r=0;r<t.length;r++)i[r]=t.charCodeAt(r);return i}(a);e[r].pssh=s}}return e},{})},Xn=function(p,f,m){return function(e){var t=Fn(e),i=Wn(f,Nn(e,"BaseURL")),r=Nn(e,"Role")[0],n={role:Fn(r)},a=gn(p,t,n),s=zn(Nn(e,"ContentProtection"));Object.keys(s).length&&(a=gn(a,{contentProtection:s}));var o,u,c,l=Gn(e),d=Nn(e,"Representation"),h=gn(m,l);return bn(d.map((o=a,u=i,c=h,function(e){var t=Nn(e,"BaseURL"),i=Wn(u,t),r=gn(o,Fn(e)),n=Gn(e);return i.map(function(e){return{segmentInfo:gn(c,n),attributes:gn(r,{baseUrl:e})}})})))}},Yn=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},i=t.manifestUri,r=void 0===i?"":i,n=t.NOW,a=void 0===n?Date.now():n,s=t.clientOffset,o=void 0===s?0:s,u=Nn(e,"Period");if(1!==u.length)throw new Error(An);var c,l,d=Fn(e),h=Wn([r],Nn(e,"BaseURL"));return d.sourceDuration=d.mediaPresentationDuration||0,d.NOW=a,d.clientOffset=o,bn(u.map((c=d,l=h,function(e,t){var i=Wn(l,Nn(e,"BaseURL")),r=Fn(e),n=gn(c,r,{periodIndex:t}),a=Nn(e,"AdaptationSet"),s=Gn(e);return bn(a.map(Xn(n,i,s)))})))},$n=function(e){if(""===e)throw new Error(Ln);var t=(new g.DOMParser).parseFromString(e,"application/xml"),i=t&&"MPD"===t.documentElement.tagName?t.documentElement:null;if(!i||i&&0<i.getElementsByTagName("parsererror").length)throw new Error(On);return i},Kn=function(e,t){return function(e){var t;if(!e.length)return{};var i=e[0].attributes,r=i.sourceDuration,n=i.minimumUpdatePeriod,a=void 0===n?0:n,s=e.filter(function(e){var t=e.attributes;return"video/mp4"===t.mimeType||"video"===t.contentType}).map(fn),o=e.filter(function(e){var t=e.attributes;return"audio/mp4"===t.mimeType||"audio"===t.contentType}),u=e.filter(function(e){var t=e.attributes;return"text/vtt"===t.mimeType||"text"===t.contentType}),c={allowCache:!0,discontinuityStarts:[],segments:[],endList:!0,mediaGroups:(t={AUDIO:{},VIDEO:{}},t["CLOSED-CAPTIONS"]={},t.SUBTITLES={},t),uri:"",duration:r,playlists:s,minimumUpdatePeriod:1e3*a};return o.length&&(c.mediaGroups.AUDIO.audio=o.reduce(function(e,t){var i,r,n,a,s,o=t.attributes.role&&t.attributes.role.value||"main",u=t.attributes.lang||"",c="main";return u&&(c=t.attributes.lang+" ("+o+")"),e[c]&&e[c].playlists[0].attributes.BANDWIDTH>t.attributes.bandwidth||(e[c]={language:u,autoselect:!0,default:"main"===o,playlists:[(i=t,n=i.attributes,a=i.segments,s={attributes:(r={NAME:n.id,BANDWIDTH:n.bandwidth,CODECS:n.codecs},r["PROGRAM-ID"]=1,r),uri:"",endList:"static"===(n.type||"static"),timeline:n.periodIndex,resolvedUri:"",targetDuration:n.duration,segments:a,mediaSequence:a.length?a[0].number:1},n.contentProtection&&(s.contentProtection=n.contentProtection),s)],uri:""}),e},{})),u.length&&(c.mediaGroups.SUBTITLES.subs=u.reduce(function(e,t){var i,r,n,a,s=t.attributes.lang||"text";return e[s]||(e[s]={language:s,default:!1,autoselect:!1,playlists:[(i=t,n=i.attributes,a=i.segments,"undefined"==typeof a&&(a=[{uri:n.baseUrl,timeline:n.periodIndex,resolvedUri:n.baseUrl||"",duration:n.sourceDuration,number:0}],n.duration=n.sourceDuration),{attributes:(r={NAME:n.id,BANDWIDTH:n.bandwidth},r["PROGRAM-ID"]=1,r),uri:"",endList:"static"===(n.type||"static"),timeline:n.periodIndex,resolvedUri:n.baseUrl||"",targetDuration:n.duration,segments:a,mediaSequence:a.length?a[0].number:1})],uri:""}),e},{})),c}(Yn($n(e),t).map(Un))},Qn=function(e){return function(e){var t=Nn(e,"UTCTiming")[0];if(!t)return null;var i=Fn(t);switch(i.schemeIdUri){case"urn:mpeg:dash:utc:http-head:2014":case"urn:mpeg:dash:utc:http-head:2012":i.method="HEAD";break;case"urn:mpeg:dash:utc:http-xsdate:2014":case"urn:mpeg:dash:utc:http-iso:2014":case"urn:mpeg:dash:utc:http-xsdate:2012":case"urn:mpeg:dash:utc:http-iso:2012":i.method="GET";break;case"urn:mpeg:dash:utc:direct:2014":case"urn:mpeg:dash:utc:direct:2012":i.method="DIRECT",i.value=Date.parse(i.value);break;case"urn:mpeg:dash:utc:http-ntp:2014":case"urn:mpeg:dash:utc:ntp:2014":case"urn:mpeg:dash:utc:sntp:2014":default:throw new Error(Rn)}return i}($n(e))},Jn={toUnsigned:function(e){return e>>>0}},Zn=Jn.toUnsigned,ea=Object.freeze({default:Jn,__moduleExports:Jn,toUnsigned:Zn}),ta=(ea&&Jn||ea).toUnsigned;Hn=function(e,t){var i,r,n,a,s,o=[];if(!t.length)return null;for(i=0;i<e.byteLength;)r=ta(e[i]<<24|e[i+1]<<16|e[i+2]<<8|e[i+3]),n=qn(e.subarray(i+4,i+8)),a=1<r?i+r:e.byteLength,n===t[0]&&(1===t.length?o.push(e.subarray(i+8,a)):(s=Hn(e.subarray(i+8,a),t.slice(1))).length&&(o=o.concat(s))),i=a;return o};qn=function(e){var t="";return t+=String.fromCharCode(e[0]),t+=String.fromCharCode(e[1]),t+=String.fromCharCode(e[2]),t+=String.fromCharCode(e[3])};var ia=function(e){return Hn(e,["moov","trak"]).reduce(function(e,t){var i,r,n,a,s;return(i=Hn(t,["tkhd"])[0])?(r=i[0],a=ta(i[n=0===r?12:20]<<24|i[n+1]<<16|i[n+2]<<8|i[n+3]),(s=Hn(t,["mdia","mdhd"])[0])?(n=0===(r=s[0])?12:20,e[a]=ta(s[n]<<24|s[n+1]<<16|s[n+2]<<8|s[n+3]),e):null):null},{})},ra=function(n,e){var t,i,r;return t=Hn(e,["moof","traf"]),i=[].concat.apply([],t.map(function(r){return Hn(r,["tfhd"]).map(function(e){var t,i;return t=ta(e[4]<<24|e[5]<<16|e[6]<<8|e[7]),i=n[t]||9e4,(Hn(r,["tfdt"]).map(function(e){var t,i;return t=e[0],i=ta(e[4]<<24|e[5]<<16|e[6]<<8|e[7]),1===t&&(i*=Math.pow(2,32),i+=ta(e[8]<<24|e[9]<<16|e[10]<<8|e[11])),i})[0]||1/0)/i})})),r=Math.min.apply(null,i),isFinite(r)?r:0},na={H264_STREAM_TYPE:27,ADTS_STREAM_TYPE:15,METADATA_STREAM_TYPE:21},aa=na.H264_STREAM_TYPE,sa=na.ADTS_STREAM_TYPE,oa=na.METADATA_STREAM_TYPE,ua=Object.freeze({default:na,__moduleExports:na,H264_STREAM_TYPE:aa,ADTS_STREAM_TYPE:sa,METADATA_STREAM_TYPE:oa}),ca=function(){this.init=function(){var a={};this.on=function(e,t){a[e]||(a[e]=[]),a[e]=a[e].concat(t)},this.off=function(e,t){var i;return!!a[e]&&(i=a[e].indexOf(t),a[e]=a[e].slice(),a[e].splice(i,1),-1<i)},this.trigger=function(e){var t,i,r,n;if(t=a[e])if(2===arguments.length)for(r=t.length,i=0;i<r;++i)t[i].call(this,arguments[1]);else{for(n=[],i=arguments.length,i=1;i<arguments.length;++i)n.push(arguments[i]);for(r=t.length,i=0;i<r;++i)t[i].apply(this,n)}},this.dispose=function(){a={}}}};ca.prototype.pipe=function(t){return this.on("data",function(e){t.push(e)}),this.on("done",function(e){t.flush(e)}),t},ca.prototype.push=function(e){this.trigger("data",e)},ca.prototype.flush=function(e){this.trigger("done",e)};var la=ca,da=Object.freeze({default:la,__moduleExports:la}),ha=function(e,t){var i=1;for(t<e&&(i=-1);4294967296<Math.abs(t-e);)e+=8589934592*i;return e},pa=function e(t){var i,r;e.prototype.init.call(this),this.type_=t,this.push=function(e){e.type===this.type_&&(void 0===r&&(r=e.dts),e.dts=ha(e.dts,r),e.pts=ha(e.pts,r),i=e.dts,this.trigger("data",e))},this.flush=function(){r=i,this.trigger("done")},this.discontinuity=function(){i=r=void 0}};pa.prototype=new(da&&la||da);var fa={TimestampRolloverStream:pa,handleRollover:ha},ma=fa.TimestampRolloverStream,ga=fa.handleRollover,ya=Object.freeze({default:fa,__moduleExports:fa,TimestampRolloverStream:ma,handleRollover:ga}),va=ua&&na||ua,_a=function(e){var t=31&e[1];return t<<=8,t|=e[2]},ba=function(e){return!!(64&e[1])},Ta=function(e){var t=0;return 1<(48&e[3])>>>4&&(t+=e[4]+1),t},Sa=function(e){switch(e){case 5:return"slice_layer_without_partitioning_rbsp_idr";case 6:return"sei_rbsp";case 7:return"seq_parameter_set_rbsp";case 8:return"pic_parameter_set_rbsp";case 9:return"access_unit_delimiter_rbsp";default:return null}},Ea={parseType:function(e,t){var i=_a(e);return 0===i?"pat":i===t?"pmt":t?"pes":null},parsePat:function(e){var t=ba(e),i=4+Ta(e);return t&&(i+=e[i]+1),(31&e[i+10])<<8|e[i+11]},parsePmt:function(e){var t={},i=ba(e),r=4+Ta(e);if(i&&(r+=e[r]+1),1&e[r+5]){var n;n=3+((15&e[r+1])<<8|e[r+2])-4;for(var a=12+((15&e[r+10])<<8|e[r+11]);a<n;){var s=r+a;t[(31&e[s+1])<<8|e[s+2]]=e[s],a+=5+((15&e[s+3])<<8|e[s+4])}return t}},parsePayloadUnitStartIndicator:ba,parsePesType:function(e,t){switch(t[_a(e)]){case va.H264_STREAM_TYPE:return"video";case va.ADTS_STREAM_TYPE:return"audio";case va.METADATA_STREAM_TYPE:return"timed-metadata";default:return null}},parsePesTime:function(e){if(!ba(e))return null;var t=4+Ta(e);if(t>=e.byteLength)return null;var i,r=null;return 192&(i=e[t+7])&&((r={}).pts=(14&e[t+9])<<27|(255&e[t+10])<<20|(254&e[t+11])<<12|(255&e[t+12])<<5|(254&e[t+13])>>>3,r.pts*=4,r.pts+=(6&e[t+13])>>>1,r.dts=r.pts,64&i&&(r.dts=(14&e[t+14])<<27|(255&e[t+15])<<20|(254&e[t+16])<<12|(255&e[t+17])<<5|(254&e[t+18])>>>3,r.dts*=4,r.dts+=(6&e[t+18])>>>1)),r},videoPacketContainsKeyFrame:function(e){for(var t=4+Ta(e),i=e.subarray(t),r=0,n=0,a=!1;n<i.byteLength-3;n++)if(1===i[n+2]){r=n+5;break}for(;r<i.byteLength;)switch(i[r]){case 0:if(0!==i[r-1]){r+=2;break}if(0!==i[r-2]){r++;break}for(n+3!==r-2&&"slice_layer_without_partitioning_rbsp_idr"===Sa(31&i[n+3])&&(a=!0);1!==i[++r]&&r<i.length;);n=r-2,r+=3;break;case 1:if(0!==i[r-1]||0!==i[r-2]){r+=3;break}"slice_layer_without_partitioning_rbsp_idr"===Sa(31&i[n+3])&&(a=!0),n=r-2,r+=3;break;default:r+=3}return i=i.subarray(n),r-=n,n=0,i&&3<i.byteLength&&"slice_layer_without_partitioning_rbsp_idr"===Sa(31&i[n+3])&&(a=!0),a}},wa=Ea.parseType,ka=Ea.parsePat,Ca=Ea.parsePmt,Aa=Ea.parsePayloadUnitStartIndicator,La=Ea.parsePesType,Oa=Ea.parsePesTime,Pa=Ea.videoPacketContainsKeyFrame,Ia=Object.freeze({default:Ea,__moduleExports:Ea,parseType:wa,parsePat:ka,parsePmt:Ca,parsePayloadUnitStartIndicator:Aa,parsePesType:La,parsePesTime:Oa,videoPacketContainsKeyFrame:Pa}),Ra=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350],xa=function(e){return e[0]<<21|e[1]<<14|e[2]<<7|e[3]},Da={parseId3TagSize:function(e,t){var i=e[t+6]<<21|e[t+7]<<14|e[t+8]<<7|e[t+9];return(16&e[t+5])>>4?i+20:i+10},parseAdtsSize:function(e,t){var i=(224&e[t+5])>>5,r=e[t+4]<<3;return 6144&e[t+3]|r|i},parseType:function(e,t){return e[t]==="I".charCodeAt(0)&&e[t+1]==="D".charCodeAt(0)&&e[t+2]==="3".charCodeAt(0)?"timed-metadata":!0&e[t]&&240==(240&e[t+1])?"audio":null},parseSampleRate:function(e){for(var t=0;t+5<e.length;){if(255===e[t]&&240==(246&e[t+1]))return Ra[(60&e[t+2])>>>2];t++}return null},parseAacTimestamp:function(e){var t,i,r;t=10,64&e[5]&&(t+=4,t+=xa(e.subarray(10,14)));do{if((i=xa(e.subarray(t+4,t+8)))<1)return null;if("PRIV"===String.fromCharCode(e[t],e[t+1],e[t+2],e[t+3])){r=e.subarray(t+10,t+i+10);for(var n=0;n<r.byteLength;n++)if(0===r[n]){if("com.apple.streaming.transportStreamTimestamp"===unescape(function(e,t,i){var r,n="";for(r=t;r<i;r++)n+="%"+("00"+e[r].toString(16)).slice(-2);return n}(r,0,n))){var a=r.subarray(n+1),s=(1&a[3])<<30|a[4]<<22|a[5]<<14|a[6]<<6|a[7]>>>2;return s*=4,s+=3&a[7]}break}}t+=10,t+=i}while(t<e.byteLength);return null}},Ua=Da.parseId3TagSize,Na=Da.parseAdtsSize,Ma=Da.parseType,Ba=Da.parseSampleRate,ja=Da.parseAacTimestamp,Fa=Object.freeze({default:Da,__moduleExports:Da,parseId3TagSize:Ua,parseAdtsSize:Na,parseType:Ma,parseSampleRate:Ba,parseAacTimestamp:ja}),Ha=Ia&&Ea||Ia,qa=Fa&&Da||Fa,Va=(ya&&fa||ya).handleRollover,Wa={};Wa.ts=Ha,Wa.aac=qa;var Ga=188,za=function(e,t,i){for(var r,n,a,s,o=0,u=Ga,c=!1;u<e.byteLength;)if(71!==e[o]||71!==e[u])o++,u++;else{switch(r=e.subarray(o,u),Wa.ts.parseType(r,t.pid)){case"pes":n=Wa.ts.parsePesType(r,t.table),a=Wa.ts.parsePayloadUnitStartIndicator(r),"audio"===n&&a&&(s=Wa.ts.parsePesTime(r))&&(s.type="audio",i.audio.push(s),c=!0)}if(c)break;o+=Ga,u+=Ga}for(o=(u=e.byteLength)-Ga,c=!1;0<=o;)if(71!==e[o]||71!==e[u])o--,u--;else{switch(r=e.subarray(o,u),Wa.ts.parseType(r,t.pid)){case"pes":n=Wa.ts.parsePesType(r,t.table),a=Wa.ts.parsePayloadUnitStartIndicator(r),"audio"===n&&a&&(s=Wa.ts.parsePesTime(r))&&(s.type="audio",i.audio.push(s),c=!0)}if(c)break;o-=Ga,u-=Ga}},Xa=function(e,t,i){for(var r,n,a,s,o,u,c,l=0,d=Ga,h=!1,p={data:[],size:0};d<e.byteLength;)if(71!==e[l]||71!==e[d])l++,d++;else{switch(r=e.subarray(l,d),Wa.ts.parseType(r,t.pid)){case"pes":if(n=Wa.ts.parsePesType(r,t.table),a=Wa.ts.parsePayloadUnitStartIndicator(r),"video"===n&&(a&&!h&&(s=Wa.ts.parsePesTime(r))&&(s.type="video",i.video.push(s),h=!0),!i.firstKeyFrame)){if(a&&0!==p.size){for(o=new Uint8Array(p.size),u=0;p.data.length;)c=p.data.shift(),o.set(c,u),u+=c.byteLength;Wa.ts.videoPacketContainsKeyFrame(o)&&(i.firstKeyFrame=Wa.ts.parsePesTime(o),i.firstKeyFrame.type="video"),p.size=0}p.data.push(r),p.size+=r.byteLength}}if(h&&i.firstKeyFrame)break;l+=Ga,d+=Ga}for(l=(d=e.byteLength)-Ga,h=!1;0<=l;)if(71!==e[l]||71!==e[d])l--,d--;else{switch(r=e.subarray(l,d),Wa.ts.parseType(r,t.pid)){case"pes":n=Wa.ts.parsePesType(r,t.table),a=Wa.ts.parsePayloadUnitStartIndicator(r),"video"===n&&a&&(s=Wa.ts.parsePesTime(r))&&(s.type="video",i.video.push(s),h=!0)}if(h)break;l-=Ga,d-=Ga}},Ya=function(e){var t={pid:null,table:null},i={};for(var r in function(e,t){for(var i,r=0,n=Ga;n<e.byteLength;)if(71!==e[r]||71!==e[n])r++,n++;else{switch(i=e.subarray(r,n),Wa.ts.parseType(i,t.pid)){case"pat":t.pid||(t.pid=Wa.ts.parsePat(i));break;case"pmt":t.table||(t.table=Wa.ts.parsePmt(i))}if(t.pid&&t.table)return;r+=Ga,n+=Ga}}(e,t),t.table){if(t.table.hasOwnProperty(r))switch(t.table[r]){case va.H264_STREAM_TYPE:i.video=[],Xa(e,t,i),0===i.video.length&&delete i.video;break;case va.ADTS_STREAM_TYPE:i.audio=[],za(e,t,i),0===i.audio.length&&delete i.audio}}return i},$a=function(e,t){var i,r;return(r=(i=e)[0]==="I".charCodeAt(0)&&i[1]==="D".charCodeAt(0)&&i[2]==="3".charCodeAt(0)?function(e){for(var t,i=!1,r=0,n=null,a=null,s=0,o=0;3<=e.length-o;){switch(Wa.aac.parseType(e,o)){case"timed-metadata":if(e.length-o<10){i=!0;break}if((s=Wa.aac.parseId3TagSize(e,o))>e.length){i=!0;break}null===a&&(t=e.subarray(o,o+s),a=Wa.aac.parseAacTimestamp(t)),o+=s;break;case"audio":if(e.length-o<7){i=!0;break}if((s=Wa.aac.parseAdtsSize(e,o))>e.length){i=!0;break}null===n&&(t=e.subarray(o,o+s),n=Wa.aac.parseSampleRate(t)),r++,o+=s;break;default:o++}if(i)return null}if(null===n||null===a)return null;var u=9e4/n;return{audio:[{type:"audio",dts:a,pts:a},{type:"audio",dts:a+1024*r*u,pts:a+1024*r*u}]}}(e):Ya(e))&&(r.audio||r.video)?(function(e,t){if(e.audio&&e.audio.length){var i=t;"undefined"==typeof i&&(i=e.audio[0].dts),e.audio.forEach(function(e){e.dts=Va(e.dts,i),e.pts=Va(e.pts,i),e.dtsTime=e.dts/9e4,e.ptsTime=e.pts/9e4})}if(e.video&&e.video.length){var r=t;if("undefined"==typeof r&&(r=e.video[0].dts),e.video.forEach(function(e){e.dts=Va(e.dts,r),e.pts=Va(e.pts,r),e.dtsTime=e.dts/9e4,e.ptsTime=e.pts/9e4}),e.firstKeyFrame){var n=e.firstKeyFrame;n.dts=Va(n.dts,r),n.pts=Va(n.pts,r),n.dtsTime=n.dts/9e4,n.ptsTime=n.dts/9e4}}}(r,t),r):null};var Ka=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},Qa=function(){function r(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(e,t,i){return t&&r(e.prototype,t),i&&r(e,i),e}}(),Ja=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==("undefined"==typeof t?"undefined":v(t))&&"function"!=typeof t?e:t},Za=function(){var e=[[[],[],[],[],[]],[[],[],[],[],[]]],t=e[0],i=e[1],r=t[4],n=i[4],a=void 0,s=void 0,o=void 0,u=[],c=[],l=void 0,d=void 0,h=void 0,p=void 0,f=void 0;for(a=0;a<256;a++)c[(u[a]=a<<1^283*(a>>7))^a]=a;for(s=o=0;!r[s];s^=l||1,o=c[o]||1)for(h=(h=o^o<<1^o<<2^o<<3^o<<4)>>8^255&h^99,f=16843009*u[d=u[l=u[n[r[s]=h]=s]]]^65537*d^257*l^16843008*s,p=257*u[h]^16843008*h,a=0;a<4;a++)t[a][s]=p=p<<24^p>>>8,i[a][h]=f=f<<24^f>>>8;for(a=0;a<5;a++)t[a]=t[a].slice(0),i[a]=i[a].slice(0);return e},es=null,ts=function(){function l(e){Ka(this,l),es||(es=Za()),this._tables=[[es[0][0].slice(),es[0][1].slice(),es[0][2].slice(),es[0][3].slice(),es[0][4].slice()],[es[1][0].slice(),es[1][1].slice(),es[1][2].slice(),es[1][3].slice(),es[1][4].slice()]];var t=void 0,i=void 0,r=void 0,n=void 0,a=void 0,s=this._tables[0][4],o=this._tables[1],u=e.length,c=1;if(4!==u&&6!==u&&8!==u)throw new Error("Invalid aes key size");for(n=e.slice(0),a=[],this._key=[n,a],t=u;t<4*u+28;t++)r=n[t-1],(t%u==0||8===u&&t%u==4)&&(r=s[r>>>24]<<24^s[r>>16&255]<<16^s[r>>8&255]<<8^s[255&r],t%u==0&&(r=r<<8^r>>>24^c<<24,c=c<<1^283*(c>>7))),n[t]=n[t-u]^r;for(i=0;t;i++,t--)r=n[3&i?t:t-4],a[i]=t<=4||i<4?r:o[0][s[r>>>24]]^o[1][s[r>>16&255]]^o[2][s[r>>8&255]]^o[3][s[255&r]]}return l.prototype.decrypt=function(e,t,i,r,n,a){var s=this._key[1],o=e^s[0],u=r^s[1],c=i^s[2],l=t^s[3],d=void 0,h=void 0,p=void 0,f=s.length/4-2,m=void 0,g=4,y=this._tables[1],v=y[0],_=y[1],b=y[2],T=y[3],S=y[4];for(m=0;m<f;m++)d=v[o>>>24]^_[u>>16&255]^b[c>>8&255]^T[255&l]^s[g],h=v[u>>>24]^_[c>>16&255]^b[l>>8&255]^T[255&o]^s[g+1],p=v[c>>>24]^_[l>>16&255]^b[o>>8&255]^T[255&u]^s[g+2],l=v[l>>>24]^_[o>>16&255]^b[u>>8&255]^T[255&c]^s[g+3],g+=4,o=d,u=h,c=p;for(m=0;m<4;m++)n[(3&-m)+a]=S[o>>>24]<<24^S[u>>16&255]<<16^S[c>>8&255]<<8^S[255&l]^s[g++],d=o,o=u,u=c,c=l,l=d},l}(),is=function(){function e(){Ka(this,e),this.listeners={}}return e.prototype.on=function(e,t){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push(t)},e.prototype.off=function(e,t){if(!this.listeners[e])return!1;var i=this.listeners[e].indexOf(t);return this.listeners[e].splice(i,1),-1<i},e.prototype.trigger=function(e){var t=this.listeners[e];if(t)if(2===arguments.length)for(var i=t.length,r=0;r<i;++r)t[r].call(this,arguments[1]);else for(var n=Array.prototype.slice.call(arguments,1),a=t.length,s=0;s<a;++s)t[s].apply(this,n)},e.prototype.dispose=function(){this.listeners={}},e.prototype.pipe=function(t){this.on("data",function(e){t.push(e)})},e}(),rs=function(t){function i(){Ka(this,i);var e=Ja(this,t.call(this,is));return e.jobs=[],e.delay=1,e.timeout_=null,e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+("undefined"==typeof t?"undefined":v(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(i,t),i.prototype.processJob_=function(){this.jobs.shift()(),this.jobs.length?this.timeout_=setTimeout(this.processJob_.bind(this),this.delay):this.timeout_=null},i.prototype.push=function(e){this.jobs.push(e),this.timeout_||(this.timeout_=setTimeout(this.processJob_.bind(this),this.delay))},i}(is),ns=function(e){return e<<24|(65280&e)<<8|(16711680&e)>>8|e>>>24},as=function(e,t,i){var r=new Int32Array(e.buffer,e.byteOffset,e.byteLength>>2),n=new ts(Array.prototype.slice.call(t)),a=new Uint8Array(e.byteLength),s=new Int32Array(a.buffer),o=void 0,u=void 0,c=void 0,l=void 0,d=void 0,h=void 0,p=void 0,f=void 0,m=void 0;for(o=i[0],u=i[1],c=i[2],l=i[3],m=0;m<r.length;m+=4)d=ns(r[m]),h=ns(r[m+1]),p=ns(r[m+2]),f=ns(r[m+3]),n.decrypt(d,h,p,f,s,m),s[m]=ns(s[m]^o),s[m+1]=ns(s[m+1]^u),s[m+2]=ns(s[m+2]^c),s[m+3]=ns(s[m+3]^l),o=d,u=h,c=p,l=f;return a},ss=function(){function u(e,t,i,r){Ka(this,u);var n=u.STEP,a=new Int32Array(e.buffer),s=new Uint8Array(e.byteLength),o=0;for(this.asyncStream_=new rs,this.asyncStream_.push(this.decryptChunk_(a.subarray(o,o+n),t,i,s)),o=n;o<a.length;o+=n)i=new Uint32Array([ns(a[o-4]),ns(a[o-3]),ns(a[o-2]),ns(a[o-1])]),this.asyncStream_.push(this.decryptChunk_(a.subarray(o,o+n),t,i,s));this.asyncStream_.push(function(){var e;r(null,(e=s).subarray(0,e.byteLength-e[e.byteLength-1]))})}return u.prototype.decryptChunk_=function(t,i,r,n){return function(){var e=as(t,i,r);n.set(e,t.byteOffset)}},Qa(u,null,[{key:"STEP",get:function(){return 32e3}}]),u}(),os=function(e,t){return/^[a-z]+:/i.test(t)?t:(/\/\//i.test(e)||(e=nn.buildAbsoluteURL(g.location.href,e)),nn.buildAbsoluteURL(e,t))},us=en.mergeOptions,cs=en.log,ls=function(n,a){["AUDIO","SUBTITLES"].forEach(function(e){for(var t in n.mediaGroups[e])for(var i in n.mediaGroups[e][t]){var r=n.mediaGroups[e][t][i];a(r,e,t,i)}})},ds=function(e,t){var i=us(e,{}),r=i.playlists[t.uri];if(!r)return null;if(r.segments&&t.segments&&r.segments.length===t.segments.length&&r.mediaSequence===t.mediaSequence)return null;var n=us(r,t);r.segments&&(n.segments=function(e,t,i){var r=t.slice();i=i||0;for(var n=Math.min(e.length,t.length+i),a=i;a<n;a++)r[a-i]=us(e[a],r[a-i]);return r}(r.segments,t.segments,t.mediaSequence-r.mediaSequence)),n.segments.forEach(function(e){var t,i;t=e,i=n.resolvedUri,t.resolvedUri||(t.resolvedUri=os(i,t.uri)),t.key&&!t.key.resolvedUri&&(t.key.resolvedUri=os(i,t.key.uri)),t.map&&!t.map.resolvedUri&&(t.map.resolvedUri=os(i,t.map.uri))});for(var a=0;a<i.playlists.length;a++)i.playlists[a].uri===t.uri&&(i.playlists[a]=n);return i.playlists[t.uri]=n,i},hs=function(e){for(var t=e.playlists.length;t--;){var i=e.playlists[t];(e.playlists[i.uri]=i).resolvedUri=os(e.uri,i.uri),i.id=t,i.attributes||(i.attributes={},cs.warn("Invalid playlist STREAM-INF detected. Missing BANDWIDTH attribute."))}},ps=function(t){ls(t,function(e){e.uri&&(e.resolvedUri=os(t.uri,e.uri))})},fs=function(e,t){var i=e.segments[e.segments.length-1];return t&&i&&i.duration?1e3*i.duration:500*(e.targetDuration||10)},ms=function(n){function a(e,t,i){y(this,a);var r=b(this,n.call(this));if(r.srcUrl=e,r.hls_=t,r.withCredentials=i,!r.srcUrl)throw new Error("A non-empty playlist URL is required");return r.state="HAVE_NOTHING",r.on("mediaupdatetimeout",function(){"HAVE_METADATA"===r.state&&(r.state="HAVE_CURRENT_METADATA",r.request=r.hls_.xhr({uri:os(r.master.uri,r.media().uri),withCredentials:r.withCredentials},function(e,t){if(r.request)return e?r.playlistRequestError(r.request,r.media().uri,"HAVE_METADATA"):void r.haveMetadata(r.request,r.media().uri)}))}),r}return _(a,n),a.prototype.playlistRequestError=function(e,t,i){this.request=null,i&&(this.state=i),this.error={playlist:this.master.playlists[t],status:e.status,message:"HLS playlist request error at URL: "+t,responseText:e.responseText,code:500<=e.status?4:2},this.trigger("error")},a.prototype.haveMetadata=function(e,t){var i=this;this.request=null,this.state="HAVE_METADATA";var r=new pn;r.push(e.responseText),r.end(),r.manifest.uri=t,r.manifest.attributes=r.manifest.attributes||{};var n=ds(this.master,r.manifest);this.targetDuration=r.manifest.targetDuration,n?(this.master=n,this.media_=this.master.playlists[r.manifest.uri]):this.trigger("playlistunchanged"),this.media().endList||(g.clearTimeout(this.mediaUpdateTimeout),this.mediaUpdateTimeout=g.setTimeout(function(){i.trigger("mediaupdatetimeout")},fs(this.media(),!!n))),this.trigger("loadedplaylist")},a.prototype.dispose=function(){this.stopRequest(),g.clearTimeout(this.mediaUpdateTimeout)},a.prototype.stopRequest=function(){if(this.request){var e=this.request;this.request=null,e.onreadystatechange=null,e.abort()}},a.prototype.media=function(i){var r=this;if(!i)return this.media_;if("HAVE_NOTHING"===this.state)throw new Error("Cannot switch media playlist from "+this.state);var n=this.state;if("string"==typeof i){if(!this.master.playlists[i])throw new Error("Unknown playlist URI: "+i);i=this.master.playlists[i]}var e=!this.media_||i.uri!==this.media_.uri;if(this.master.playlists[i.uri].endList)return this.request&&(this.request.onreadystatechange=null,this.request.abort(),this.request=null),this.state="HAVE_METADATA",this.media_=i,void(e&&(this.trigger("mediachanging"),this.trigger("mediachange")));if(e){if(this.state="SWITCHING_MEDIA",this.request){if(os(this.master.uri,i.uri)===this.request.url)return;this.request.onreadystatechange=null,this.request.abort(),this.request=null}this.media_&&this.trigger("mediachanging"),this.request=this.hls_.xhr({uri:os(this.master.uri,i.uri),withCredentials:this.withCredentials},function(e,t){if(r.request){if(e)return r.playlistRequestError(r.request,i.uri,n);r.haveMetadata(t,i.uri),"HAVE_MASTER"===n?r.trigger("loadedmetadata"):r.trigger("mediachange")}})}},a.prototype.pause=function(){this.stopRequest(),g.clearTimeout(this.mediaUpdateTimeout),"HAVE_NOTHING"===this.state&&(this.started=!1),"SWITCHING_MEDIA"===this.state?this.media_?this.state="HAVE_METADATA":this.state="HAVE_MASTER":"HAVE_CURRENT_METADATA"===this.state&&(this.state="HAVE_METADATA")},a.prototype.load=function(e){var t=this;g.clearTimeout(this.mediaUpdateTimeout);var i=this.media();if(e){var r=i?i.targetDuration/2*1e3:5e3;this.mediaUpdateTimeout=g.setTimeout(function(){return t.load()},r)}else this.started?i&&!i.endList?this.trigger("mediaupdatetimeout"):this.trigger("loadedplaylist"):this.start()},a.prototype.start=function(){var r=this;this.started=!0,this.request=this.hls_.xhr({uri:this.srcUrl,withCredentials:this.withCredentials},function(e,t){if(r.request){if(r.request=null,e)return r.error={status:t.status,message:"HLS playlist request error at URL: "+r.srcUrl,responseText:t.responseText,code:2},"HAVE_NOTHING"===r.state&&(r.started=!1),r.trigger("error");var i=new pn;return i.push(t.responseText),i.end(),r.state="HAVE_MASTER",i.manifest.uri=r.srcUrl,i.manifest.playlists?(r.master=i.manifest,hs(r.master),ps(r.master),r.trigger("loadedplaylist"),void(r.request||r.media(i.manifest.playlists[0]))):(r.master={mediaGroups:{AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},uri:g.location.href,playlists:[{uri:r.srcUrl,id:0}]},r.master.playlists[r.srcUrl]=r.master.playlists[0],r.master.playlists[0].resolvedUri=r.srcUrl,r.master.playlists[0].attributes=r.master.playlists[0].attributes||{},r.haveMetadata(t,r.srcUrl),r.trigger("loadedmetadata"))}})},a}(en.EventTarget),gs=en.createTimeRange,ys=function(e,t,i){var r,n;return"undefined"==typeof t&&(t=e.mediaSequence+e.segments.length),t<e.mediaSequence?0:(r=function(e,t){var i=0,r=t-e.mediaSequence,n=e.segments[r];if(n){if("undefined"!=typeof n.start)return{result:n.start,precise:!0};if("undefined"!=typeof n.end)return{result:n.end-n.duration,precise:!0}}for(;r--;){if("undefined"!=typeof(n=e.segments[r]).end)return{result:i+n.end,precise:!0};if(i+=n.duration,"undefined"!=typeof n.start)return{result:i+n.start,precise:!0}}return{result:i,precise:!1}}(e,t)).precise?r.result:(n=function(e,t){for(var i=0,r=void 0,n=t-e.mediaSequence;n<e.segments.length;n++){if("undefined"!=typeof(r=e.segments[n]).start)return{result:r.start-i,precise:!0};if(i+=r.duration,"undefined"!=typeof r.end)return{result:r.end-i,precise:!0}}return{result:-1,precise:!1}}(e,t)).precise?n.result:r.result+i},vs=function(e,t,i){if(!e)return 0;if("number"!=typeof i&&(i=0),"undefined"==typeof t){if(e.totalDuration)return e.totalDuration;if(!e.endList)return g.Infinity}return ys(e,t,i)},_s=function(e,t,i){var r=0;if(i<t){var n=[i,t];t=n[0],i=n[1]}if(t<0){for(var a=t;a<Math.min(0,i);a++)r+=e.targetDuration;t=0}for(var s=t;s<i;s++)r+=e.segments[s].duration;return r},bs=function(e){if(!e.segments.length)return 0;for(var t=e.segments.length-1,i=e.segments[t].duration||e.targetDuration,r=i+2*e.targetDuration;t--&&!(r<=(i+=e.segments[t].duration)););return Math.max(0,t)},Ts=function(e,t,i){if(!e||!e.segments)return null;if(e.endList)return vs(e);if(null===t)return null;t=t||0;var r=i?bs(e):e.segments.length;return ys(e,e.mediaSequence+r,t)},Ss=function(e){return e-Math.floor(e)==0},Es=function(e,t){if(Ss(t))return t+.1*e;for(var i=t.toString().split(".")[1].length,r=1;r<=i;r++){var n=Math.pow(10,r),a=t*n;if(Ss(a)||r===i)return(a+e)/n}},ws=Es.bind(null,1),ks=Es.bind(null,-1),Cs=function(e){return e.excludeUntil&&e.excludeUntil>Date.now()},As=function(e){return e.excludeUntil&&e.excludeUntil===1/0},Ls=function(e){var t=Cs(e);return!e.disabled&&!t},Os=function(e,t){return t.attributes&&t.attributes[e]},Ps=function(e,t){if(1===e.playlists.length)return!0;var i=t.attributes.BANDWIDTH||Number.MAX_VALUE;return 0===e.playlists.filter(function(e){return!!Ls(e)&&(e.attributes.BANDWIDTH||0)<i}).length},Is={duration:vs,seekable:function(e,t){var i=t||0,r=Ts(e,t,!0);return null===r?gs():gs(i,r)},safeLiveIndex:bs,getMediaInfoForTime:function(e,t,i,r){var n=void 0,a=void 0,s=e.segments.length,o=t-r;if(o<0){if(0<i)for(n=i-1;0<=n;n--)if(a=e.segments[n],0<(o+=ks(a.duration)))return{mediaIndex:n,startTime:r-_s(e,i,n)};return{mediaIndex:0,startTime:t}}if(i<0){for(n=i;n<0;n++)if((o-=e.targetDuration)<0)return{mediaIndex:0,startTime:t};i=0}for(n=i;n<s;n++)if(a=e.segments[n],(o-=ws(a.duration))<0)return{mediaIndex:n,startTime:r+_s(e,i,n)};return{mediaIndex:s-1,startTime:t}},isEnabled:Ls,isDisabled:function(e){return e.disabled},isBlacklisted:Cs,isIncompatible:As,playlistEnd:Ts,isAes:function(e){for(var t=0;t<e.segments.length;t++)if(e.segments[t].key)return!0;return!1},isFmp4:function(e){for(var t=0;t<e.segments.length;t++)if(e.segments[t].map)return!0;return!1},hasAttribute:Os,estimateSegmentRequestTime:function(e,t,i){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:0;return Os("BANDWIDTH",i)?(e*i.attributes.BANDWIDTH-8*r)/t:NaN},isLowestEnabledRendition:Ps},Rs=en.xhr,xs=en.mergeOptions,Ds=function(){return function e(t,r){t=xs({timeout:45e3},t);var i=e.beforeRequest||en.Hls.xhr.beforeRequest;if(i&&"function"==typeof i){var n=i(t);n&&(t=n)}var a=Rs(t,function(e,t){var i=a.response;!e&&i&&(a.responseTime=Date.now(),a.roundTripTime=a.responseTime-a.requestTime,a.bytesReceived=i.byteLength||i.length,a.bandwidth||(a.bandwidth=Math.floor(a.bytesReceived/a.roundTripTime*8*1e3))),t.headers&&(a.responseHeaders=t.headers),e&&"ETIMEDOUT"===e.code&&(a.timedout=!0),e||a.aborted||200===t.statusCode||206===t.statusCode||0===t.statusCode||(e=new Error("XHR Failed with a response of: "+(a&&(i||a.responseText)))),r(e,a)}),s=a.abort;return a.abort=function(){return a.aborted=!0,s.apply(a,arguments)},a.uri=t.uri,a.requestTime=Date.now(),a}},Us=function(e,t){var i=e.toString(16);return"00".substring(0,2-i.length)+i+(t%2?" ":"")},Ns=function(e){return 32<=e&&e<126?String.fromCharCode(e):"."},Ms=function(i){var r={};return Object.keys(i).forEach(function(e){var t=i[e];ArrayBuffer.isView(t)?r[e]={bytes:t.buffer,byteOffset:t.byteOffset,byteLength:t.byteLength}:r[e]=t}),r},Bs=function(e){var t=e.byterange||{length:1/0,offset:0};return[t.length,t.offset,e.resolvedUri].join(",")},js=function(e){for(var t=Array.prototype.slice.call(e),i="",r=0;r<t.length/16;r++)i+=t.slice(16*r,16*r+16).map(Us).join("")+" "+t.slice(16*r,16*r+16).map(Ns).join("")+"\n";return i},Fs=Object.freeze({createTransferableMessage:Ms,initSegmentId:Bs,hexDump:js,tagDump:function(e){var t=e.bytes;return js(t)},textRanges:function(e){var t,i,r="",n=void 0;for(n=0;n<e.length;n++)r+=(i=n,(t=e).start(i)+"-"+t.end(i)+" ");return r}}),Hs=1/30,qs=function(e,t){var i=[],r=void 0;if(e&&e.length)for(r=0;r<e.length;r++)t(e.start(r),e.end(r))&&i.push([e.start(r),e.end(r)]);return en.createTimeRanges(i)},Vs=function(e,i){return qs(e,function(e,t){return e-Hs<=i&&i<=t+Hs})},Ws=function(e,t){return qs(e,function(e){return t<=e-Hs})},Gs=function(e){var t=[];if(!e||!e.length)return"";for(var i=0;i<e.length;i++)t.push(e.start(i)+" => "+e.end(i));return t.join(", ")},zs=function(e){for(var t=[],i=0;i<e.length;i++)t.push({start:e.start(i),end:e.end(i)});return t},Xs=function(e,t,i){var r=void 0,n=void 0;if(i&&i.cues)for(r=i.cues.length;r--;)(n=i.cues[r]).startTime<=t&&n.endTime>=e&&i.removeCue(n)},Ys=function(e){return isNaN(e)||Math.abs(e)===1/0?Number.MAX_VALUE:e},$s=function(e,t,i){var n=g.WebKitDataCue||g.VTTCue;if(t&&t.forEach(function(e){var t=e.stream;this.inbandTextTracks_[t].addCue(new n(e.startTime+this.timestampOffset,e.endTime+this.timestampOffset,e.text))},e),i){var a=Ys(e.mediaSource_.duration);if(i.forEach(function(e){var r=e.cueTime+this.timestampOffset;e.frames.forEach(function(e){var t,i=new n(r,r,e.value||e.url||e.data||"");i.frame=e,i.value=e,t=i,Object.defineProperties(t.frame,{id:{get:function(){return en.log.warn("cue.frame.id is deprecated. Use cue.value.key instead."),t.value.key}},value:{get:function(){return en.log.warn("cue.frame.value is deprecated. Use cue.value.data instead."),t.value.data}},privateData:{get:function(){return en.log.warn("cue.frame.privateData is deprecated. Use cue.value.data instead."),t.value.data}}}),this.metadataTrack_.addCue(i)},this)},e),e.metadataTrack_&&e.metadataTrack_.cues&&e.metadataTrack_.cues.length){for(var r=e.metadataTrack_.cues,s=[],o=0;o<r.length;o++)r[o]&&s.push(r[o]);var u=s.reduce(function(e,t){var i=e[t.startTime]||[];return i.push(t),e[t.startTime]=i,e},{}),c=Object.keys(u).sort(function(e,t){return Number(e)-Number(t)});c.forEach(function(e,t){var i=u[e],r=Number(c[t+1])||a;i.forEach(function(e){e.endTime=r})})}}},Ks="undefined"!=typeof window?window:{},Qs="undefined"==typeof Symbol?"__target":Symbol(),Js="application/javascript",Zs=Ks.BlobBuilder||Ks.WebKitBlobBuilder||Ks.MozBlobBuilder||Ks.MSBlobBuilder,eo=Ks.URL||Ks.webkitURL||eo&&eo.msURL,to=Ks.Worker;function io(n,a){return function(e){var t=this;if(!a)return new to(n);if(to&&!e){var i=so(a.toString().replace(/^function.+?{/,"").slice(0,-1));return this[Qs]=new to(i),function(e,t){if(!e||!t)return;var i=e.terminate;e.objURL=t,e.terminate=function(){e.objURL&&eo.revokeObjectURL(e.objURL),i.call(e)}}(this[Qs],i),this[Qs]}var r={postMessage:function(e){t.onmessage&&setTimeout(function(){t.onmessage({data:e,target:r})})}};a.call(r),this.postMessage=function(e){setTimeout(function(){r.onmessage({data:e,target:t})})},this.isThisThread=!0}}if(to){var ro,no=so("self.onmessage = function () {}"),ao=new Uint8Array(1);try{(ro=new to(no)).postMessage(ao,[ao.buffer])}catch(e){to=null}finally{eo.revokeObjectURL(no),ro&&ro.terminate()}}function so(t){try{return eo.createObjectURL(new Blob([t],{type:Js}))}catch(e){var i=new Zs;return i.append(t),eo.createObjectURL(i.getBlob(type))}}var oo=new io("./transmuxer-worker.worker.js",function(it,e){var rt=this;!function(){var o,t,i,n,a,r,e,s,u,c,l,d,h,p,f,m,g,y,v,_,b,T,S,E,w,k,C,A,L,O,P,I,R,x,D,U,N,M,B,j,F="undefined"!=typeof it?it:"undefined"!=typeof global?global:"undefined"!=typeof rt?rt:{},H="undefined"!=typeof it?it:"undefined"!=typeof F?F:"undefined"!=typeof rt?rt:{},q=Math.pow(2,32)-1;!function(){var e;if(T={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],smhd:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],styp:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[]},"undefined"!=typeof Uint8Array){for(e in T)T.hasOwnProperty(e)&&(T[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]);S=new Uint8Array(["i".charCodeAt(0),"s".charCodeAt(0),"o".charCodeAt(0),"m".charCodeAt(0)]),w=new Uint8Array(["a".charCodeAt(0),"v".charCodeAt(0),"c".charCodeAt(0),"1".charCodeAt(0)]),E=new Uint8Array([0,0,0,1]),k=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),C=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),A={video:k,audio:C},P=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),O=new Uint8Array([0,0,0,0,0,0,0,0]),I=new Uint8Array([0,0,0,0,0,0,0,0]),R=I,x=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),D=I,L=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])}}(),o=function(e){var t,i,r=[],n=0;for(t=1;t<arguments.length;t++)r.push(arguments[t]);for(t=r.length;t--;)n+=r[t].byteLength;for(i=new Uint8Array(n+8),new DataView(i.buffer,i.byteOffset,i.byteLength).setUint32(0,i.byteLength),i.set(e,4),t=0,n=8;t<r.length;t++)i.set(r[t],n),n+=r[t].byteLength;return i},t=function(){return o(T.dinf,o(T.dref,P))},i=function(e){return o(T.esds,new Uint8Array([0,0,0,0,3,25,0,0,0,4,17,64,21,0,6,0,0,0,218,192,0,0,218,192,5,2,e.audioobjecttype<<3|e.samplingfrequencyindex>>>1,e.samplingfrequencyindex<<7|e.channelcount<<3,6,1,2]))},f=function(e){return o(T.hdlr,A[e])},p=function(e){var t=new Uint8Array([0,0,0,0,0,0,0,2,0,0,0,3,0,1,95,144,e.duration>>>24&255,e.duration>>>16&255,e.duration>>>8&255,255&e.duration,85,196,0,0]);return e.samplerate&&(t[12]=e.samplerate>>>24&255,t[13]=e.samplerate>>>16&255,t[14]=e.samplerate>>>8&255,t[15]=255&e.samplerate),o(T.mdhd,t)},h=function(e){return o(T.mdia,p(e),f(e.type),r(e))},a=function(e){return o(T.mfhd,new Uint8Array([0,0,0,0,(4278190080&e)>>24,(16711680&e)>>16,(65280&e)>>8,255&e]))},r=function(e){return o(T.minf,"video"===e.type?o(T.vmhd,L):o(T.smhd,O),t(),g(e))},e=function(e,t){for(var i=[],r=t.length;r--;)i[r]=v(t[r]);return o.apply(null,[T.moof,a(e)].concat(i))},s=function(e){for(var t=e.length,i=[];t--;)i[t]=l(e[t]);return o.apply(null,[T.moov,c(4294967295)].concat(i).concat(u(e)))},u=function(e){for(var t=e.length,i=[];t--;)i[t]=_(e[t]);return o.apply(null,[T.mvex].concat(i))},c=function(e){var t=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,2,0,1,95,144,(4278190080&e)>>24,(16711680&e)>>16,(65280&e)>>8,255&e,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return o(T.mvhd,t)},m=function(e){var t,i,r=e.samples||[],n=new Uint8Array(4+r.length);for(i=0;i<r.length;i++)t=r[i].flags,n[i+4]=t.dependsOn<<4|t.isDependedOn<<2|t.hasRedundancy;return o(T.sdtp,n)},g=function(e){return o(T.stbl,y(e),o(T.stts,D),o(T.stsc,R),o(T.stsz,x),o(T.stco,I))},y=function(e){return o(T.stsd,new Uint8Array([0,0,0,0,0,0,0,1]),"video"===e.type?U(e):N(e))},U=function(e){var t,i=e.sps||[],r=e.pps||[],n=[],a=[];for(t=0;t<i.length;t++)n.push((65280&i[t].byteLength)>>>8),n.push(255&i[t].byteLength),n=n.concat(Array.prototype.slice.call(i[t]));for(t=0;t<r.length;t++)a.push((65280&r[t].byteLength)>>>8),a.push(255&r[t].byteLength),a=a.concat(Array.prototype.slice.call(r[t]));return o(T.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,(65280&e.width)>>8,255&e.width,(65280&e.height)>>8,255&e.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,19,118,105,100,101,111,106,115,45,99,111,110,116,114,105,98,45,104,108,115,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),o(T.avcC,new Uint8Array([1,e.profileIdc,e.profileCompatibility,e.levelIdc,255].concat([i.length]).concat(n).concat([r.length]).concat(a))),o(T.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192])))},N=function(e){return o(T.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,(65280&e.channelcount)>>8,255&e.channelcount,(65280&e.samplesize)>>8,255&e.samplesize,0,0,0,0,(65280&e.samplerate)>>8,255&e.samplerate,0,0]),i(e))},d=function(e){var t=new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,0,(4278190080&e.duration)>>24,(16711680&e.duration)>>16,(65280&e.duration)>>8,255&e.duration,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,(65280&e.width)>>8,255&e.width,0,0,(65280&e.height)>>8,255&e.height,0,0]);return o(T.tkhd,t)},v=function(e){var t,i,r,n,a,s;return t=o(T.tfhd,new Uint8Array([0,0,0,58,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0])),a=Math.floor(e.baseMediaDecodeTime/(q+1)),s=Math.floor(e.baseMediaDecodeTime%(q+1)),i=o(T.tfdt,new Uint8Array([1,0,0,0,a>>>24&255,a>>>16&255,a>>>8&255,255&a,s>>>24&255,s>>>16&255,s>>>8&255,255&s])),92,"audio"===e.type?(r=b(e,92),o(T.traf,t,i,r)):(n=m(e),r=b(e,n.length+92),o(T.traf,t,i,r,n))},l=function(e){return e.duration=e.duration||4294967295,o(T.trak,d(e),h(e))},_=function(e){var t=new Uint8Array([0,0,0,0,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return"video"!==e.type&&(t[t.length-1]=0),o(T.trex,t)},j=function(e,t){var i=0,r=0,n=0,a=0;return e.length&&(void 0!==e[0].duration&&(i=1),void 0!==e[0].size&&(r=2),void 0!==e[0].flags&&(n=4),void 0!==e[0].compositionTimeOffset&&(a=8)),[0,0,i|r|n|a,1,(4278190080&e.length)>>>24,(16711680&e.length)>>>16,(65280&e.length)>>>8,255&e.length,(4278190080&t)>>>24,(16711680&t)>>>16,(65280&t)>>>8,255&t]},B=function(e,t){var i,r,n,a;for(t+=20+16*(r=e.samples||[]).length,i=j(r,t),a=0;a<r.length;a++)n=r[a],i=i.concat([(4278190080&n.duration)>>>24,(16711680&n.duration)>>>16,(65280&n.duration)>>>8,255&n.duration,(4278190080&n.size)>>>24,(16711680&n.size)>>>16,(65280&n.size)>>>8,255&n.size,n.flags.isLeading<<2|n.flags.dependsOn,n.flags.isDependedOn<<6|n.flags.hasRedundancy<<4|n.flags.paddingValue<<1|n.flags.isNonSyncSample,61440&n.flags.degradationPriority,15&n.flags.degradationPriority,(4278190080&n.compositionTimeOffset)>>>24,(16711680&n.compositionTimeOffset)>>>16,(65280&n.compositionTimeOffset)>>>8,255&n.compositionTimeOffset]);return o(T.trun,new Uint8Array(i))},M=function(e,t){var i,r,n,a;for(t+=20+8*(r=e.samples||[]).length,i=j(r,t),a=0;a<r.length;a++)n=r[a],i=i.concat([(4278190080&n.duration)>>>24,(16711680&n.duration)>>>16,(65280&n.duration)>>>8,255&n.duration,(4278190080&n.size)>>>24,(16711680&n.size)>>>16,(65280&n.size)>>>8,255&n.size]);return o(T.trun,new Uint8Array(i))},b=function(e,t){return"audio"===e.type?M(e,t):B(e,t)};var V={ftyp:n=function(){return o(T.ftyp,S,E,S,w)},mdat:function(e){return o(T.mdat,e)},moof:e,moov:s,initSegment:function(e){var t,i=n(),r=s(e);return(t=new Uint8Array(i.byteLength+r.byteLength)).set(i),t.set(r,i.byteLength),t}},W=function(){this.init=function(){var a={};this.on=function(e,t){a[e]||(a[e]=[]),a[e]=a[e].concat(t)},this.off=function(e,t){var i;return!!a[e]&&(i=a[e].indexOf(t),a[e]=a[e].slice(),a[e].splice(i,1),-1<i)},this.trigger=function(e){var t,i,r,n;if(t=a[e])if(2===arguments.length)for(r=t.length,i=0;i<r;++i)t[i].call(this,arguments[1]);else{for(n=[],i=arguments.length,i=1;i<arguments.length;++i)n.push(arguments[i]);for(r=t.length,i=0;i<r;++i)t[i].apply(this,n)}},this.dispose=function(){a={}}}};W.prototype.pipe=function(t){return this.on("data",function(e){t.push(e)}),this.on("done",function(e){t.flush(e)}),t},W.prototype.push=function(e){this.trigger("data",e)},W.prototype.flush=function(e){this.trigger("done",e)};var G=W,z=function e(){e.prototype.init.call(this),this.captionPackets_=[],this.ccStreams_=[new Q(0,0),new Q(0,1),new Q(1,0),new Q(1,1)],this.reset(),this.ccStreams_.forEach(function(e){e.on("data",this.trigger.bind(this,"data")),e.on("done",this.trigger.bind(this,"done"))},this)};(z.prototype=new G).push=function(e){var t,i,r;if("sei_rbsp"===e.nalUnitType&&(4===(t=function(e){for(var t=0,i={payloadType:-1,payloadSize:0},r=0,n=0;t<e.byteLength&&128!==e[t];){for(;255===e[t];)r+=255,t++;for(r+=e[t++];255===e[t];)n+=255,t++;if(n+=e[t++],!i.payload&&4===r){i.payloadType=r,i.payloadSize=n,i.payload=e.subarray(t,t+n);break}t+=n,n=r=0}return i}(e.escapedRBSP)).payloadType&&(i=181!==(r=t).payload[0]?null:49!=(r.payload[1]<<8|r.payload[2])?null:"GA94"!==String.fromCharCode(r.payload[3],r.payload[4],r.payload[5],r.payload[6])?null:3!==r.payload[7]?null:r.payload.subarray(8,r.payload.length-1))))if(e.dts<this.latestDts_)this.ignoreNextEqualDts_=!0;else{if(e.dts===this.latestDts_&&this.ignoreNextEqualDts_)return this.numSameDts_--,void(this.numSameDts_||(this.ignoreNextEqualDts_=!1));this.captionPackets_=this.captionPackets_.concat(function(e,t){var i,r,n,a,s=[];if(!(64&t[0]))return s;for(r=31&t[0],i=0;i<r;i++)a={type:3&t[2+(n=3*i)],pts:e},4&t[n+2]&&(a.ccData=t[n+3]<<8|t[n+4],s.push(a));return s}(e.pts,i)),this.latestDts_!==e.dts&&(this.numSameDts_=0),this.numSameDts_++,this.latestDts_=e.dts}},z.prototype.flush=function(){this.captionPackets_.length?(this.captionPackets_.forEach(function(e,t){e.presortIndex=t}),this.captionPackets_.sort(function(e,t){return e.pts===t.pts?e.presortIndex-t.presortIndex:e.pts-t.pts}),this.captionPackets_.forEach(function(e){e.type<2&&this.dispatchCea608Packet(e)},this),this.captionPackets_.length=0,this.ccStreams_.forEach(function(e){e.flush()},this)):this.ccStreams_.forEach(function(e){e.flush()},this)},z.prototype.reset=function(){this.latestDts_=null,this.ignoreNextEqualDts_=!1,this.numSameDts_=0,this.activeCea608Channel_=[null,null],this.ccStreams_.forEach(function(e){e.reset()})},z.prototype.dispatchCea608Packet=function(e){this.setsChannel1Active(e)?this.activeCea608Channel_[e.type]=0:this.setsChannel2Active(e)&&(this.activeCea608Channel_[e.type]=1),null!==this.activeCea608Channel_[e.type]&&this.ccStreams_[(e.type<<1)+this.activeCea608Channel_[e.type]].push(e)},z.prototype.setsChannel1Active=function(e){return 4096==(30720&e.ccData)},z.prototype.setsChannel2Active=function(e){return 6144==(30720&e.ccData)};var X={42:225,92:233,94:237,95:243,96:250,123:231,124:247,125:209,126:241,127:9608,304:174,305:176,306:189,307:191,308:8482,309:162,310:163,311:9834,312:224,313:160,314:232,315:226,316:234,317:238,318:244,319:251,544:193,545:201,546:211,547:218,548:220,549:252,550:8216,551:161,552:42,553:39,554:8212,555:169,556:8480,557:8226,558:8220,559:8221,560:192,561:194,562:199,563:200,564:202,565:203,566:235,567:206,568:207,569:239,570:212,571:217,572:249,573:219,574:171,575:187,800:195,801:227,802:205,803:204,804:236,805:210,806:242,807:213,808:245,809:123,810:125,811:92,812:94,813:95,814:124,815:126,816:196,817:228,818:214,819:246,820:223,821:165,822:164,823:9474,824:197,825:229,826:216,827:248,828:9484,829:9488,830:9492,831:9496},Y=function(e){return null===e?"":(e=X[e]||e,String.fromCharCode(e))},$=[4352,4384,4608,4640,5376,5408,5632,5664,5888,5920,4096,4864,4896,5120,5152],K=function(){for(var e=[],t=15;t--;)e.push("");return e},Q=function e(t,i){e.prototype.init.call(this),this.field_=t||0,this.dataChannel_=i||0,this.name_="CC"+(1+(this.field_<<1|this.dataChannel_)),this.setConstants(),this.reset(),this.push=function(e){var t,i,r,n,a;if((t=32639&e.ccData)!==this.lastControlCode_){if(4096==(61440&t)?this.lastControlCode_=t:t!==this.PADDING_&&(this.lastControlCode_=null),r=t>>>8,n=255&t,t!==this.PADDING_)if(t===this.RESUME_CAPTION_LOADING_)this.mode_="popOn";else if(t===this.END_OF_CAPTION_)this.clearFormatting(e.pts),this.flushDisplayed(e.pts),i=this.displayed_,this.displayed_=this.nonDisplayed_,this.nonDisplayed_=i,this.startPts_=e.pts;else if(t===this.ROLL_UP_2_ROWS_)this.topRow_=13,this.mode_="rollUp";else if(t===this.ROLL_UP_3_ROWS_)this.topRow_=12,this.mode_="rollUp";else if(t===this.ROLL_UP_4_ROWS_)this.topRow_=11,this.mode_="rollUp";else if(t===this.CARRIAGE_RETURN_)this.clearFormatting(e.pts),this.flushDisplayed(e.pts),this.shiftRowsUp_(),this.startPts_=e.pts;else if(t===this.BACKSPACE_)"popOn"===this.mode_?this.nonDisplayed_[14]=this.nonDisplayed_[14].slice(0,-1):this.displayed_[14]=this.displayed_[14].slice(0,-1);else if(t===this.ERASE_DISPLAYED_MEMORY_)this.flushDisplayed(e.pts),this.displayed_=K();else if(t===this.ERASE_NON_DISPLAYED_MEMORY_)this.nonDisplayed_=K();else if(t===this.RESUME_DIRECT_CAPTIONING_)this.mode_="paintOn";else if(this.isSpecialCharacter(r,n))a=Y((r=(3&r)<<8)|n),this[this.mode_](e.pts,a),this.column_++;else if(this.isExtCharacter(r,n))"popOn"===this.mode_?this.nonDisplayed_[this.row_]=this.nonDisplayed_[this.row_].slice(0,-1):this.displayed_[14]=this.displayed_[14].slice(0,-1),a=Y((r=(3&r)<<8)|n),this[this.mode_](e.pts,a),this.column_++;else if(this.isMidRowCode(r,n))this.clearFormatting(e.pts),this[this.mode_](e.pts," "),this.column_++,14==(14&n)&&this.addFormatting(e.pts,["i"]),1==(1&n)&&this.addFormatting(e.pts,["u"]);else if(this.isOffsetControlCode(r,n))this.column_+=3&n;else if(this.isPAC(r,n)){var s=$.indexOf(7968&t);s!==this.row_&&(this.clearFormatting(e.pts),this.row_=s),1&n&&-1===this.formatting_.indexOf("u")&&this.addFormatting(e.pts,["u"]),16==(16&t)&&(this.column_=4*((14&t)>>1)),this.isColorPAC(n)&&14==(14&n)&&this.addFormatting(e.pts,["i"])}else this.isNormalChar(r)&&(0===n&&(n=null),a=Y(r),a+=Y(n),this[this.mode_](e.pts,a),this.column_+=a.length)}else this.lastControlCode_=null}};Q.prototype=new G,Q.prototype.flushDisplayed=function(e){var t=this.displayed_.map(function(e){return e.trim()}).join("\n").replace(/^\n+|\n+$/g,"");t.length&&this.trigger("data",{startPts:this.startPts_,endPts:e,text:t,stream:this.name_})},Q.prototype.reset=function(){this.mode_="popOn",this.topRow_=0,this.startPts_=0,this.displayed_=K(),this.nonDisplayed_=K(),this.lastControlCode_=null,this.column_=0,this.row_=14,this.formatting_=[]},Q.prototype.setConstants=function(){0===this.dataChannel_?(this.BASE_=16,this.EXT_=17,this.CONTROL_=(20|this.field_)<<8,this.OFFSET_=23):1===this.dataChannel_&&(this.BASE_=24,this.EXT_=25,this.CONTROL_=(28|this.field_)<<8,this.OFFSET_=31),this.PADDING_=0,this.RESUME_CAPTION_LOADING_=32|this.CONTROL_,this.END_OF_CAPTION_=47|this.CONTROL_,this.ROLL_UP_2_ROWS_=37|this.CONTROL_,this.ROLL_UP_3_ROWS_=38|this.CONTROL_,this.ROLL_UP_4_ROWS_=39|this.CONTROL_,this.CARRIAGE_RETURN_=45|this.CONTROL_,this.RESUME_DIRECT_CAPTIONING_=41|this.CONTROL_,this.BACKSPACE_=33|this.CONTROL_,this.ERASE_DISPLAYED_MEMORY_=44|this.CONTROL_,this.ERASE_NON_DISPLAYED_MEMORY_=46|this.CONTROL_},Q.prototype.isSpecialCharacter=function(e,t){return e===this.EXT_&&48<=t&&t<=63},Q.prototype.isExtCharacter=function(e,t){return(e===this.EXT_+1||e===this.EXT_+2)&&32<=t&&t<=63},Q.prototype.isMidRowCode=function(e,t){return e===this.EXT_&&32<=t&&t<=47},Q.prototype.isOffsetControlCode=function(e,t){return e===this.OFFSET_&&33<=t&&t<=35},Q.prototype.isPAC=function(e,t){return e>=this.BASE_&&e<this.BASE_+8&&64<=t&&t<=127},Q.prototype.isColorPAC=function(e){return 64<=e&&e<=79||96<=e&&e<=127},Q.prototype.isNormalChar=function(e){return 32<=e&&e<=127},Q.prototype.addFormatting=function(e,t){this.formatting_=this.formatting_.concat(t);var i=t.reduce(function(e,t){return e+"<"+t+">"},"");this[this.mode_](e,i)},Q.prototype.clearFormatting=function(e){if(this.formatting_.length){var t=this.formatting_.reverse().reduce(function(e,t){return e+"</"+t+">"},"");this.formatting_=[],this[this.mode_](e,t)}},Q.prototype.popOn=function(e,t){var i=this.nonDisplayed_[this.row_];i+=t,this.nonDisplayed_[this.row_]=i},Q.prototype.rollUp=function(e,t){var i=this.displayed_[14];i+=t,this.displayed_[14]=i},Q.prototype.shiftRowsUp_=function(){var e;for(e=0;e<this.topRow_;e++)this.displayed_[e]="";for(e=this.topRow_;e<14;e++)this.displayed_[e]=this.displayed_[e+1];this.displayed_[14]=""},Q.prototype.paintOn=function(){};var J={CaptionStream:z,Cea608Stream:Q},Z={H264_STREAM_TYPE:27,ADTS_STREAM_TYPE:15,METADATA_STREAM_TYPE:21},ee=function(e,t){var i=1;for(t<e&&(i=-1);4294967296<Math.abs(t-e);)e+=8589934592*i;return e},te=function e(t){var i,r;e.prototype.init.call(this),this.type_=t,this.push=function(e){e.type===this.type_&&(void 0===r&&(r=e.dts),e.dts=ee(e.dts,r),e.pts=ee(e.pts,r),i=e.dts,this.trigger("data",e))},this.flush=function(){r=i,this.trigger("done")},this.discontinuity=function(){i=r=void 0}};te.prototype=new G;var ie,re=te,ne=function(e,t,i){var r,n="";for(r=t;r<i;r++)n+="%"+("00"+e[r].toString(16)).slice(-2);return n},ae=function(e,t,i){return decodeURIComponent(ne(e,t,i))},se=function(e){return e[0]<<21|e[1]<<14|e[2]<<7|e[3]},oe={TXXX:function(e){var t;if(3===e.data[0]){for(t=1;t<e.data.length;t++)if(0===e.data[t]){e.description=ae(e.data,1,t),e.value=ae(e.data,t+1,e.data.length).replace(/\0*$/,"");break}e.data=e.value}},WXXX:function(e){var t;if(3===e.data[0])for(t=1;t<e.data.length;t++)if(0===e.data[t]){e.description=ae(e.data,1,t),e.url=ae(e.data,t+1,e.data.length);break}},PRIV:function(e){var t,i;for(t=0;t<e.data.length;t++)if(0===e.data[t]){e.owner=(i=e.data,unescape(ne(i,0,t)));break}e.privateData=e.data.subarray(t+1),e.data=e.privateData}};(ie=function(e){var t,u={debug:!(!e||!e.debug),descriptor:e&&e.descriptor},c=0,l=[],d=0;if(ie.prototype.init.call(this),this.dispatchType=Z.METADATA_STREAM_TYPE.toString(16),u.descriptor)for(t=0;t<u.descriptor.length;t++)this.dispatchType+=("00"+u.descriptor[t].toString(16)).slice(-2);this.push=function(e){var t,i,r,n,a;if("timed-metadata"===e.type)if(e.dataAlignmentIndicator&&(d=0,l.length=0),0===l.length&&(e.data.length<10||e.data[0]!=="I".charCodeAt(0)||e.data[1]!=="D".charCodeAt(0)||e.data[2]!=="3".charCodeAt(0)))u.debug;else if(l.push(e),d+=e.data.byteLength,1===l.length&&(c=se(e.data.subarray(6,10)),c+=10),!(d<c)){for(t={data:new Uint8Array(c),frames:[],pts:l[0].pts,dts:l[0].dts},a=0;a<c;)t.data.set(l[0].data.subarray(0,c-a),a),a+=l[0].data.byteLength,d-=l[0].data.byteLength,l.shift();i=10,64&t.data[5]&&(i+=4,i+=se(t.data.subarray(10,14)),c-=se(t.data.subarray(16,20)));do{if((r=se(t.data.subarray(i+4,i+8)))<1)return;if((n={id:String.fromCharCode(t.data[i],t.data[i+1],t.data[i+2],t.data[i+3]),data:t.data.subarray(i+10,i+r+10)}).key=n.id,oe[n.id]&&(oe[n.id](n),"com.apple.streaming.transportStreamTimestamp"===n.owner)){var s=n.data,o=(1&s[3])<<30|s[4]<<22|s[5]<<14|s[6]<<6|s[7]>>>2;o*=4,o+=3&s[7],n.timeStamp=o,void 0===t.pts&&void 0===t.dts&&(t.pts=n.timeStamp,t.dts=n.timeStamp),this.trigger("timestamp",n)}t.frames.push(n),i+=10,i+=r}while(i<c);this.trigger("data",t)}}}).prototype=new G;var ue,ce,le,de=ie,he=re;(ue=function(){var n=new Uint8Array(188),a=0;ue.prototype.init.call(this),this.push=function(e){var t,i=0,r=188;for(a?((t=new Uint8Array(e.byteLength+a)).set(n.subarray(0,a)),t.set(e,a),a=0):t=e;r<t.byteLength;)71!==t[i]||71!==t[r]?(i++,r++):(this.trigger("data",t.subarray(i,r)),i+=188,r+=188);i<t.byteLength&&(n.set(t.subarray(i),0),a=t.byteLength-i)},this.flush=function(){188===a&&71===n[0]&&(this.trigger("data",n),a=0),this.trigger("done")}}).prototype=new G,(ce=function(){var r,n,a,s;ce.prototype.init.call(this),(s=this).packetsWaitingForPmt=[],this.programMapTable=void 0,r=function(e,t){var i=0;t.payloadUnitStartIndicator&&(i+=e[i]+1),"pat"===t.type?n(e.subarray(i),t):a(e.subarray(i),t)},n=function(e,t){t.section_number=e[7],t.last_section_number=e[8],s.pmtPid=(31&e[10])<<8|e[11],t.pmtPid=s.pmtPid},a=function(e,t){var i,r;if(1&e[5]){for(s.programMapTable={video:null,audio:null,"timed-metadata":{}},i=3+((15&e[1])<<8|e[2])-4,r=12+((15&e[10])<<8|e[11]);r<i;){var n=e[r],a=(31&e[r+1])<<8|e[r+2];n===Z.H264_STREAM_TYPE&&null===s.programMapTable.video?s.programMapTable.video=a:n===Z.ADTS_STREAM_TYPE&&null===s.programMapTable.audio?s.programMapTable.audio=a:n===Z.METADATA_STREAM_TYPE&&(s.programMapTable["timed-metadata"][a]=n),r+=5+((15&e[r+3])<<8|e[r+4])}t.programMapTable=s.programMapTable}},this.push=function(e){var t={},i=4;if(t.payloadUnitStartIndicator=!!(64&e[1]),t.pid=31&e[1],t.pid<<=8,t.pid|=e[2],1<(48&e[3])>>>4&&(i+=e[i]+1),0===t.pid)t.type="pat",r(e.subarray(i),t),this.trigger("data",t);else if(t.pid===this.pmtPid)for(t.type="pmt",r(e.subarray(i),t),this.trigger("data",t);this.packetsWaitingForPmt.length;)this.processPes_.apply(this,this.packetsWaitingForPmt.shift());else void 0===this.programMapTable?this.packetsWaitingForPmt.push([e,i,t]):this.processPes_(e,i,t)},this.processPes_=function(e,t,i){i.pid===this.programMapTable.video?i.streamType=Z.H264_STREAM_TYPE:i.pid===this.programMapTable.audio?i.streamType=Z.ADTS_STREAM_TYPE:i.streamType=this.programMapTable["timed-metadata"][i.pid],i.type="pes",i.data=e.subarray(t),this.trigger("data",i)}}).prototype=new G,ce.STREAM_TYPES={h264:27,adts:15},(le=function(){var h=this,r={data:[],size:0},n={data:[],size:0},a={data:[],size:0},s=function(e,t,i){var r,n,a=new Uint8Array(e.size),s={type:t},o=0,u=0;if(e.data.length&&!(e.size<9)){for(s.trackId=e.data[0].pid,o=0;o<e.data.length;o++)n=e.data[o],a.set(n.data,u),u+=n.data.byteLength;var c,l,d;c=a,(l=s).packetLength=6+(c[4]<<8|c[5]),l.dataAlignmentIndicator=0!=(4&c[6]),192&(d=c[7])&&(l.pts=(14&c[9])<<27|(255&c[10])<<20|(254&c[11])<<12|(255&c[12])<<5|(254&c[13])>>>3,l.pts*=4,l.pts+=(6&c[13])>>>1,l.dts=l.pts,64&d&&(l.dts=(14&c[14])<<27|(255&c[15])<<20|(254&c[16])<<12|(255&c[17])<<5|(254&c[18])>>>3,l.dts*=4,l.dts+=(6&c[18])>>>1)),l.data=c.subarray(9+c[8]),r="video"===t||s.packetLength<=e.size,(i||r)&&(e.size=0,e.data.length=0),r&&h.trigger("data",s)}};le.prototype.init.call(this),this.push=function(i){({pat:function(){},pes:function(){var e,t;switch(i.streamType){case Z.H264_STREAM_TYPE:case Z.H264_STREAM_TYPE:e=r,t="video";break;case Z.ADTS_STREAM_TYPE:e=n,t="audio";break;case Z.METADATA_STREAM_TYPE:e=a,t="timed-metadata";break;default:return}i.payloadUnitStartIndicator&&s(e,t,!0),e.data.push(i),e.size+=i.data.byteLength},pmt:function(){var e={type:"metadata",tracks:[]},t=i.programMapTable;null!==t.video&&e.tracks.push({timelineStartInfo:{baseMediaDecodeTime:0},id:+t.video,codec:"avc",type:"video"}),null!==t.audio&&e.tracks.push({timelineStartInfo:{baseMediaDecodeTime:0},id:+t.audio,codec:"adts",type:"audio"}),h.trigger("data",e)}})[i.type]()},this.flush=function(){s(r,"video"),s(n,"audio"),s(a,"timed-metadata"),this.trigger("done")}}).prototype=new G;var pe={PAT_PID:0,MP2T_PACKET_LENGTH:188,TransportPacketStream:ue,TransportParseStream:ce,ElementaryStream:le,TimestampRolloverStream:he,CaptionStream:J.CaptionStream,Cea608Stream:J.Cea608Stream,MetadataStream:de};for(var fe in Z)Z.hasOwnProperty(fe)&&(pe[fe]=Z[fe]);var me,ge=pe,ye=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];(me=function(){var c;me.prototype.init.call(this),this.push=function(e){var t,i,r,n,a,s,o=0,u=0;if("audio"===e.type)for(c?(n=c,(c=new Uint8Array(n.byteLength+e.data.byteLength)).set(n),c.set(e.data,n.byteLength)):c=e.data;o+5<c.length;)if(255===c[o]&&240==(246&c[o+1])){if(i=2*(1&~c[o+1]),t=(3&c[o+3])<<11|c[o+4]<<3|(224&c[o+5])>>5,s=9e4*(a=1024*(1+(3&c[o+6])))/ye[(60&c[o+2])>>>2],r=o+t,c.byteLength<r)return;if(this.trigger("data",{pts:e.pts+u*s,dts:e.dts+u*s,sampleCount:a,audioobjecttype:1+(c[o+2]>>>6&3),channelcount:(1&c[o+2])<<2|(192&c[o+3])>>>6,samplerate:ye[(60&c[o+2])>>>2],samplingfrequencyindex:(60&c[o+2])>>>2,samplesize:16,data:c.subarray(o+7+i,r)}),c.byteLength===r)return void(c=void 0);u++,c=c.subarray(r)}else o++},this.flush=function(){this.trigger("done")}}).prototype=new G;var ve,_e,be,Te=me,Se=function(r){var n=r.byteLength,a=0,s=0;this.length=function(){return 8*n},this.bitsAvailable=function(){return 8*n+s},this.loadWord=function(){var e=r.byteLength-n,t=new Uint8Array(4),i=Math.min(4,n);if(0===i)throw new Error("no bytes available");t.set(r.subarray(e,e+i)),a=new DataView(t.buffer).getUint32(0),s=8*i,n-=i},this.skipBits=function(e){var t;e<s||(e-=s,e-=8*(t=Math.floor(e/8)),n-=t,this.loadWord()),a<<=e,s-=e},this.readBits=function(e){var t=Math.min(s,e),i=a>>>32-t;return 0<(s-=t)?a<<=t:0<n&&this.loadWord(),0<(t=e-t)?i<<t|this.readBits(t):i},this.skipLeadingZeros=function(){var e;for(e=0;e<s;++e)if(0!=(a&2147483648>>>e))return a<<=e,s-=e,e;return this.loadWord(),e+this.skipLeadingZeros()},this.skipUnsignedExpGolomb=function(){this.skipBits(1+this.skipLeadingZeros())},this.skipExpGolomb=function(){this.skipBits(1+this.skipLeadingZeros())},this.readUnsignedExpGolomb=function(){var e=this.skipLeadingZeros();return this.readBits(e+1)-1},this.readExpGolomb=function(){var e=this.readUnsignedExpGolomb();return 1&e?1+e>>>1:-1*(e>>>1)},this.readBoolean=function(){return 1===this.readBits(1)},this.readUnsignedByte=function(){return this.readBits(8)},this.loadWord()};(_e=function(){var i,r,n=0;_e.prototype.init.call(this),this.push=function(e){var t;for(r?((t=new Uint8Array(r.byteLength+e.data.byteLength)).set(r),t.set(e.data,r.byteLength),r=t):r=e.data;n<r.byteLength-3;n++)if(1===r[n+2]){i=n+5;break}for(;i<r.byteLength;)switch(r[i]){case 0:if(0!==r[i-1]){i+=2;break}if(0!==r[i-2]){i++;break}for(n+3!==i-2&&this.trigger("data",r.subarray(n+3,i-2));1!==r[++i]&&i<r.length;);n=i-2,i+=3;break;case 1:if(0!==r[i-1]||0!==r[i-2]){i+=3;break}this.trigger("data",r.subarray(n+3,i-2)),n=i-2,i+=3;break;default:i+=3}r=r.subarray(n),i-=n,n=0},this.flush=function(){r&&3<r.byteLength&&this.trigger("data",r.subarray(n+3)),r=null,n=0,this.trigger("done")}}).prototype=new G,be={100:!0,110:!0,122:!0,244:!0,44:!0,83:!0,86:!0,118:!0,128:!0,138:!0,139:!0,134:!0},(ve=function(){var i,r,n,a,s,o,_,t=new _e;ve.prototype.init.call(this),(i=this).push=function(e){"video"===e.type&&(r=e.trackId,n=e.pts,a=e.dts,t.push(e))},t.on("data",function(e){var t={trackId:r,pts:n,dts:a,data:e};switch(31&e[0]){case 5:t.nalUnitType="slice_layer_without_partitioning_rbsp_idr";break;case 6:t.nalUnitType="sei_rbsp",t.escapedRBSP=s(e.subarray(1));break;case 7:t.nalUnitType="seq_parameter_set_rbsp",t.escapedRBSP=s(e.subarray(1)),t.config=o(t.escapedRBSP);break;case 8:t.nalUnitType="pic_parameter_set_rbsp";break;case 9:t.nalUnitType="access_unit_delimiter_rbsp"}i.trigger("data",t)}),t.on("done",function(){i.trigger("done")}),this.flush=function(){t.flush()},_=function(e,t){var i,r=8,n=8;for(i=0;i<e;i++)0!==n&&(n=(r+t.readExpGolomb()+256)%256),r=0===n?r:n},s=function(e){for(var t,i,r=e.byteLength,n=[],a=1;a<r-2;)0===e[a]&&0===e[a+1]&&3===e[a+2]?(n.push(a+2),a+=2):a++;if(0===n.length)return e;t=r-n.length,i=new Uint8Array(t);var s=0;for(a=0;a<t;s++,a++)s===n[0]&&(s++,n.shift()),i[a]=e[s];return i},o=function(e){var t,i,r,n,a,s,o,u,c,l,d,h,p,f=0,m=0,g=0,y=0,v=1;if(i=(t=new Se(e)).readUnsignedByte(),n=t.readUnsignedByte(),r=t.readUnsignedByte(),t.skipUnsignedExpGolomb(),be[i]&&(3===(a=t.readUnsignedExpGolomb())&&t.skipBits(1),t.skipUnsignedExpGolomb(),t.skipUnsignedExpGolomb(),t.skipBits(1),t.readBoolean()))for(d=3!==a?8:12,p=0;p<d;p++)t.readBoolean()&&_(p<6?16:64,t);if(t.skipUnsignedExpGolomb(),0===(s=t.readUnsignedExpGolomb()))t.readUnsignedExpGolomb();else if(1===s)for(t.skipBits(1),t.skipExpGolomb(),t.skipExpGolomb(),o=t.readUnsignedExpGolomb(),p=0;p<o;p++)t.skipExpGolomb();if(t.skipUnsignedExpGolomb(),t.skipBits(1),u=t.readUnsignedExpGolomb(),c=t.readUnsignedExpGolomb(),0===(l=t.readBits(1))&&t.skipBits(1),t.skipBits(1),t.readBoolean()&&(f=t.readUnsignedExpGolomb(),m=t.readUnsignedExpGolomb(),g=t.readUnsignedExpGolomb(),y=t.readUnsignedExpGolomb()),t.readBoolean()&&t.readBoolean()){switch(t.readUnsignedByte()){case 1:h=[1,1];break;case 2:h=[12,11];break;case 3:h=[10,11];break;case 4:h=[16,11];break;case 5:h=[40,33];break;case 6:h=[24,11];break;case 7:h=[20,11];break;case 8:h=[32,11];break;case 9:h=[80,33];break;case 10:h=[18,11];break;case 11:h=[15,11];break;case 12:h=[64,33];break;case 13:h=[160,99];break;case 14:h=[4,3];break;case 15:h=[3,2];break;case 16:h=[2,1];break;case 255:h=[t.readUnsignedByte()<<8|t.readUnsignedByte(),t.readUnsignedByte()<<8|t.readUnsignedByte()]}h&&(v=h[0]/h[1])}return{profileIdc:i,levelIdc:r,profileCompatibility:n,width:Math.ceil((16*(u+1)-2*f-2*m)*v),height:(2-l)*(c+1)*16-2*g-2*y}}}).prototype=new G;var Ee,we={H264Stream:ve,NalByteStream:_e};(Ee=function(){var o=new Uint8Array,u=0;Ee.prototype.init.call(this),this.setTimestamp=function(e){u=e},this.parseId3TagSize=function(e,t){var i=e[t+6]<<21|e[t+7]<<14|e[t+8]<<7|e[t+9];return(16&e[t+5])>>4?i+20:i+10},this.parseAdtsSize=function(e,t){var i=(224&e[t+5])>>5,r=e[t+4]<<3;return 6144&e[t+3]|r|i},this.push=function(e){var t,i,r,n,a=0,s=0;for(o.length?(n=o.length,(o=new Uint8Array(e.byteLength+n)).set(o.subarray(0,n)),o.set(e,n)):o=e;3<=o.length-s;)if(o[s]!=="I".charCodeAt(0)||o[s+1]!=="D".charCodeAt(0)||o[s+2]!=="3".charCodeAt(0))if(!0&o[s]&&240==(240&o[s+1])){if(o.length-s<7)break;if((a=this.parseAdtsSize(o,s))>o.length)break;r={type:"audio",data:o.subarray(s,s+a),pts:u,dts:u},this.trigger("data",r),s+=a}else s++;else{if(o.length-s<10)break;if((a=this.parseId3TagSize(o,s))>o.length)break;i={type:"timed-metadata",data:o.subarray(s,s+a)},this.trigger("data",i),s+=a}t=o.length-s,o=0<t?o.subarray(s):new Uint8Array}}).prototype=new G;var ke,Ce,Ae,Le,Oe,Pe,Ie,Re,xe,De,Ue,Ne,Me,Be,je,Fe=Ee,He=[33,16,5,32,164,27],qe=[33,65,108,84,1,2,4,8,168,2,4,8,17,191,252],Ve=function(e){for(var t=[];e--;)t.push(0);return t},We={96000:[He,[227,64],Ve(154),[56]],88200:[He,[231],Ve(170),[56]],64000:[He,[248,192],Ve(240),[56]],48000:[He,[255,192],Ve(268),[55,148,128],Ve(54),[112]],44100:[He,[255,192],Ve(268),[55,163,128],Ve(84),[112]],32000:[He,[255,192],Ve(268),[55,234],Ve(226),[112]],24000:[He,[255,192],Ve(268),[55,255,128],Ve(268),[111,112],Ve(126),[224]],16000:[He,[255,192],Ve(268),[55,255,128],Ve(268),[111,255],Ve(269),[223,108],Ve(195),[1,192]],12000:[qe,Ve(268),[3,127,248],Ve(268),[6,255,240],Ve(268),[13,255,224],Ve(268),[27,253,128],Ve(259),[56]],11025:[qe,Ve(268),[3,127,248],Ve(268),[6,255,240],Ve(268),[13,255,224],Ve(268),[27,255,192],Ve(268),[55,175,128],Ve(108),[112]],8000:[qe,Ve(268),[3,121,16],Ve(47),[7]]},Ge=(ke=We,Object.keys(ke).reduce(function(e,t){return e[t]=new Uint8Array(ke[t].reduce(function(e,t){return e.concat(t)},[])),e},{})),ze=(Ce=function(e){return 9e4*e},Ae=function(e,t){return e*t},Le=function(e){return e/9e4},Oe=function(e,t){return e/t},function(e,t){return Ce(Oe(e,t))}),Xe=function(e,t){return Ae(Le(e),t)},Ye=we.H264Stream,$e=["audioobjecttype","channelcount","samplerate","samplingfrequencyindex","samplesize"],Ke=["width","height","profileIdc","levelIdc","profileCompatibility"];De=function(e){return e[0]==="I".charCodeAt(0)&&e[1]==="D".charCodeAt(0)&&e[2]==="3".charCodeAt(0)},Be=function(e,t){var i;if(e.length!==t.length)return!1;for(i=0;i<e.length;i++)if(e[i]!==t[i])return!1;return!0},je=function(e){var t,i=0;for(t=0;t<e.length;t++)i+=e[t].data.byteLength;return i},(Ie=function(n,a){var s=[],o=0,t=0,c=0,l=1/0;a=a||{},Ie.prototype.init.call(this),this.push=function(t){Ue(n,t),n&&$e.forEach(function(e){n[e]=t[e]}),s.push(t)},this.setEarliestDts=function(e){t=e-n.timelineStartInfo.baseMediaDecodeTime},this.setVideoBaseMediaDecodeTime=function(e){l=e},this.setAudioAppendStart=function(e){c=e},this.flush=function(){var e,t,i,r;0!==s.length&&(e=this.trimAdtsFramesByEarliestDts_(s),n.baseMediaDecodeTime=Me(n,a.keepOriginalTimestamps),this.prefixWithSilence_(n,e),n.samples=this.generateSampleTable_(e),i=V.mdat(this.concatenateFrameData_(e)),s=[],t=V.moof(o,[n]),r=new Uint8Array(t.byteLength+i.byteLength),o++,r.set(t),r.set(i,t.byteLength),Ne(n),this.trigger("data",{track:n,boxes:r})),this.trigger("done","AudioSegmentStream")},this.prefixWithSilence_=function(e,t){var i,r,n,a,s=0,o=0,u=0;if(t.length&&(i=ze(e.baseMediaDecodeTime,e.samplerate),r=Math.ceil(9e4/(e.samplerate/1024)),c&&l&&(s=i-Math.max(c,l),u=(o=Math.floor(s/r))*r),!(o<1||45e3<u))){for((n=Ge[e.samplerate])||(n=t[0].data),a=0;a<o;a++)t.splice(a,0,{data:n});e.baseMediaDecodeTime-=Math.floor(Xe(u,e.samplerate))}},this.trimAdtsFramesByEarliestDts_=function(e){return n.minSegmentDts>=t?e:(n.minSegmentDts=1/0,e.filter(function(e){return e.dts>=t&&(n.minSegmentDts=Math.min(n.minSegmentDts,e.dts),n.minSegmentPts=n.minSegmentDts,!0)}))},this.generateSampleTable_=function(e){var t,i,r=[];for(t=0;t<e.length;t++)i=e[t],r.push({size:i.data.byteLength,duration:1024});return r},this.concatenateFrameData_=function(e){var t,i,r=0,n=new Uint8Array(je(e));for(t=0;t<e.length;t++)i=e[t],n.set(i.data,r),r+=i.data.byteLength;return n}}).prototype=new G,(Pe=function(o,u){var t,i,c=0,l=[],d=[];u=u||{},Pe.prototype.init.call(this),delete o.minPTS,this.gopCache_=[],this.push=function(e){Ue(o,e),"seq_parameter_set_rbsp"!==e.nalUnitType||t||(t=e.config,o.sps=[e.data],Ke.forEach(function(e){o[e]=t[e]},this)),"pic_parameter_set_rbsp"!==e.nalUnitType||i||(i=e.data,o.pps=[e.data]),l.push(e)},this.flush=function(){for(var e,t,i,r,n,a;l.length&&"access_unit_delimiter_rbsp"!==l[0].nalUnitType;)l.shift();if(0===l.length)return this.resetStream_(),void this.trigger("done","VideoSegmentStream");if(e=this.groupNalsIntoFrames_(l),(i=this.groupFramesIntoGops_(e))[0][0].keyFrame||((t=this.getGopForFusion_(l[0],o))?(i.unshift(t),i.byteLength+=t.byteLength,i.nalCount+=t.nalCount,i.pts=t.pts,i.dts=t.dts,i.duration+=t.duration):i=this.extendFirstKeyFrame_(i)),d.length){var s;if(!(s=u.alignGopsAtEnd?this.alignGopsAtEnd_(i):this.alignGopsAtStart_(i)))return this.gopCache_.unshift({gop:i.pop(),pps:o.pps,sps:o.sps}),this.gopCache_.length=Math.min(6,this.gopCache_.length),l=[],this.resetStream_(),void this.trigger("done","VideoSegmentStream");Ne(o),i=s}Ue(o,i),o.samples=this.generateSampleTable_(i),n=V.mdat(this.concatenateNalData_(i)),o.baseMediaDecodeTime=Me(o,u.keepOriginalTimestamps),this.trigger("processedGopsInfo",i.map(function(e){return{pts:e.pts,dts:e.dts,byteLength:e.byteLength}})),this.gopCache_.unshift({gop:i.pop(),pps:o.pps,sps:o.sps}),this.gopCache_.length=Math.min(6,this.gopCache_.length),l=[],this.trigger("baseMediaDecodeTime",o.baseMediaDecodeTime),this.trigger("timelineStartInfo",o.timelineStartInfo),r=V.moof(c,[o]),a=new Uint8Array(r.byteLength+n.byteLength),c++,a.set(r),a.set(n,r.byteLength),this.trigger("data",{track:o,boxes:a}),this.resetStream_(),this.trigger("done","VideoSegmentStream")},this.resetStream_=function(){Ne(o),i=t=void 0},this.getGopForFusion_=function(e){var t,i,r,n,a,s=1/0;for(a=0;a<this.gopCache_.length;a++)r=(n=this.gopCache_[a]).gop,o.pps&&Be(o.pps[0],n.pps[0])&&o.sps&&Be(o.sps[0],n.sps[0])&&(r.dts<o.timelineStartInfo.dts||-1e4<=(t=e.dts-r.dts-r.duration)&&t<=45e3&&(!i||t<s)&&(i=n,s=t));return i?i.gop:null},this.extendFirstKeyFrame_=function(e){var t;return!e[0][0].keyFrame&&1<e.length&&(t=e.shift(),e.byteLength-=t.byteLength,e.nalCount-=t.nalCount,e[0][0].dts=t.dts,e[0][0].pts=t.pts,e[0][0].duration+=t.duration),e},this.groupNalsIntoFrames_=function(e){var t,i,r=[],n=[];for(t=r.byteLength=0;t<e.length;t++)"access_unit_delimiter_rbsp"===(i=e[t]).nalUnitType?(r.length&&(r.duration=i.dts-r.dts,n.push(r)),(r=[i]).byteLength=i.data.byteLength,r.pts=i.pts,r.dts=i.dts):("slice_layer_without_partitioning_rbsp_idr"===i.nalUnitType&&(r.keyFrame=!0),r.duration=i.dts-r.dts,r.byteLength+=i.data.byteLength,r.push(i));return n.length&&(!r.duration||r.duration<=0)&&(r.duration=n[n.length-1].duration),n.push(r),n},this.groupFramesIntoGops_=function(e){var t,i,r=[],n=[];for(r.byteLength=0,r.nalCount=0,r.duration=0,r.pts=e[0].pts,r.dts=e[0].dts,n.byteLength=0,n.nalCount=0,n.duration=0,n.pts=e[0].pts,n.dts=e[0].dts,t=0;t<e.length;t++)(i=e[t]).keyFrame?(r.length&&(n.push(r),n.byteLength+=r.byteLength,n.nalCount+=r.nalCount,n.duration+=r.duration),(r=[i]).nalCount=i.length,r.byteLength=i.byteLength,r.pts=i.pts,r.dts=i.dts,r.duration=i.duration):(r.duration+=i.duration,r.nalCount+=i.length,r.byteLength+=i.byteLength,r.push(i));return n.length&&r.duration<=0&&(r.duration=n[n.length-1].duration),n.byteLength+=r.byteLength,n.nalCount+=r.nalCount,n.duration+=r.duration,n.push(r),n},this.generateSampleTable_=function(e,t){var i,r,n,a,s,o=t||0,u=[];for(i=0;i<e.length;i++)for(a=e[i],r=0;r<a.length;r++)s=a[r],(n={size:0,flags:{isLeading:0,dependsOn:1,isDependedOn:0,hasRedundancy:0,degradationPriority:0}}).dataOffset=o,n.compositionTimeOffset=s.pts-s.dts,n.duration=s.duration,n.size=4*s.length,n.size+=s.byteLength,s.keyFrame&&(n.flags.dependsOn=2),o+=n.size,u.push(n);return u},this.concatenateNalData_=function(e){var t,i,r,n,a,s,o=0,u=e.byteLength,c=e.nalCount,l=new Uint8Array(u+4*c),d=new DataView(l.buffer);for(t=0;t<e.length;t++)for(n=e[t],i=0;i<n.length;i++)for(a=n[i],r=0;r<a.length;r++)s=a[r],d.setUint32(o,s.data.byteLength),o+=4,l.set(s.data,o),o+=s.data.byteLength;return l},this.alignGopsAtStart_=function(e){var t,i,r,n,a,s,o,u;for(a=e.byteLength,s=e.nalCount,o=e.duration,t=i=0;t<d.length&&i<e.length&&(r=d[t],n=e[i],r.pts!==n.pts);)n.pts>r.pts?t++:(i++,a-=n.byteLength,s-=n.nalCount,o-=n.duration);return 0===i?e:i===e.length?null:((u=e.slice(i)).byteLength=a,u.duration=o,u.nalCount=s,u.pts=u[0].pts,u.dts=u[0].dts,u)},this.alignGopsAtEnd_=function(e){var t,i,r,n,a,s,o;for(t=d.length-1,i=e.length-1,a=null,s=!1;0<=t&&0<=i;){if(r=d[t],n=e[i],r.pts===n.pts){s=!0;break}r.pts>n.pts?t--:(t===d.length-1&&(a=i),i--)}if(!s&&null===a)return null;if(0===(o=s?i:a))return e;var u=e.slice(o),c=u.reduce(function(e,t){return e.byteLength+=t.byteLength,e.duration+=t.duration,e.nalCount+=t.nalCount,e},{byteLength:0,duration:0,nalCount:0});return u.byteLength=c.byteLength,u.duration=c.duration,u.nalCount=c.nalCount,u.pts=u[0].pts,u.dts=u[0].dts,u},this.alignGopsWith=function(e){d=e}}).prototype=new G,Ue=function(e,t){"number"==typeof t.pts&&(void 0===e.timelineStartInfo.pts&&(e.timelineStartInfo.pts=t.pts),void 0===e.minSegmentPts?e.minSegmentPts=t.pts:e.minSegmentPts=Math.min(e.minSegmentPts,t.pts),void 0===e.maxSegmentPts?e.maxSegmentPts=t.pts:e.maxSegmentPts=Math.max(e.maxSegmentPts,t.pts)),"number"==typeof t.dts&&(void 0===e.timelineStartInfo.dts&&(e.timelineStartInfo.dts=t.dts),void 0===e.minSegmentDts?e.minSegmentDts=t.dts:e.minSegmentDts=Math.min(e.minSegmentDts,t.dts),void 0===e.maxSegmentDts?e.maxSegmentDts=t.dts:e.maxSegmentDts=Math.max(e.maxSegmentDts,t.dts))},Ne=function(e){delete e.minSegmentDts,delete e.maxSegmentDts,delete e.minSegmentPts,delete e.maxSegmentPts},Me=function(e,t){var i,r=e.minSegmentDts;return t||(r-=e.timelineStartInfo.dts),i=e.timelineStartInfo.baseMediaDecodeTime,i+=r,i=Math.max(0,i),"audio"===e.type&&(i*=e.samplerate/9e4,i=Math.floor(i)),i},(xe=function(e,t){this.numberOfTracks=0,this.metadataStream=t,"undefined"!=typeof e.remux?this.remuxTracks=!!e.remux:this.remuxTracks=!0,this.pendingTracks=[],this.videoTrack=null,this.pendingBoxes=[],this.pendingCaptions=[],this.pendingMetadata=[],this.pendingBytes=0,this.emittedTracks=0,xe.prototype.init.call(this),this.push=function(e){return e.text?this.pendingCaptions.push(e):e.frames?this.pendingMetadata.push(e):(this.pendingTracks.push(e.track),this.pendingBoxes.push(e.boxes),this.pendingBytes+=e.boxes.byteLength,"video"===e.track.type&&(this.videoTrack=e.track),void("audio"===e.track.type&&(this.audioTrack=e.track)))}}).prototype=new G,xe.prototype.flush=function(e){var t,i,r,n,a=0,s={captions:[],captionStreams:{},metadata:[],info:{}},o=0;if(this.pendingTracks.length<this.numberOfTracks){if("VideoSegmentStream"!==e&&"AudioSegmentStream"!==e)return;if(this.remuxTracks)return;if(0===this.pendingTracks.length)return this.emittedTracks++,void(this.emittedTracks>=this.numberOfTracks&&(this.trigger("done"),this.emittedTracks=0))}for(this.videoTrack?(o=this.videoTrack.timelineStartInfo.pts,Ke.forEach(function(e){s.info[e]=this.videoTrack[e]},this)):this.audioTrack&&(o=this.audioTrack.timelineStartInfo.pts,$e.forEach(function(e){s.info[e]=this.audioTrack[e]},this)),1===this.pendingTracks.length?s.type=this.pendingTracks[0].type:s.type="combined",this.emittedTracks+=this.pendingTracks.length,r=V.initSegment(this.pendingTracks),s.initSegment=new Uint8Array(r.byteLength),s.initSegment.set(r),s.data=new Uint8Array(this.pendingBytes),n=0;n<this.pendingBoxes.length;n++)s.data.set(this.pendingBoxes[n],a),a+=this.pendingBoxes[n].byteLength;for(n=0;n<this.pendingCaptions.length;n++)(t=this.pendingCaptions[n]).startTime=t.startPts-o,t.startTime/=9e4,t.endTime=t.endPts-o,t.endTime/=9e4,s.captionStreams[t.stream]=!0,s.captions.push(t);for(n=0;n<this.pendingMetadata.length;n++)(i=this.pendingMetadata[n]).cueTime=i.pts-o,i.cueTime/=9e4,s.metadata.push(i);s.metadata.dispatchType=this.metadataStream.dispatchType,this.pendingTracks.length=0,this.videoTrack=null,this.pendingBoxes.length=0,this.pendingCaptions.length=0,this.pendingBytes=0,this.pendingMetadata.length=0,this.trigger("data",s),this.emittedTracks>=this.numberOfTracks&&(this.trigger("done"),this.emittedTracks=0)},(Re=function(r){var n,a,s=this,i=!0;Re.prototype.init.call(this),r=r||{},this.baseMediaDecodeTime=r.baseMediaDecodeTime||0,this.transmuxPipeline_={},this.setupAacPipeline=function(){var t={};(this.transmuxPipeline_=t).type="aac",t.metadataStream=new ge.MetadataStream,t.aacStream=new Fe,t.audioTimestampRolloverStream=new ge.TimestampRolloverStream("audio"),t.timedMetadataTimestampRolloverStream=new ge.TimestampRolloverStream("timed-metadata"),t.adtsStream=new Te,t.coalesceStream=new xe(r,t.metadataStream),t.headOfPipeline=t.aacStream,t.aacStream.pipe(t.audioTimestampRolloverStream).pipe(t.adtsStream),t.aacStream.pipe(t.timedMetadataTimestampRolloverStream).pipe(t.metadataStream).pipe(t.coalesceStream),t.metadataStream.on("timestamp",function(e){t.aacStream.setTimestamp(e.timeStamp)}),t.aacStream.on("data",function(e){"timed-metadata"!==e.type||t.audioSegmentStream||(a=a||{timelineStartInfo:{baseMediaDecodeTime:s.baseMediaDecodeTime},codec:"adts",type:"audio"},t.coalesceStream.numberOfTracks++,t.audioSegmentStream=new Ie(a,r),t.adtsStream.pipe(t.audioSegmentStream).pipe(t.coalesceStream))}),t.coalesceStream.on("data",this.trigger.bind(this,"data")),t.coalesceStream.on("done",this.trigger.bind(this,"done"))},this.setupTsPipeline=function(){var i={};(this.transmuxPipeline_=i).type="ts",i.metadataStream=new ge.MetadataStream,i.packetStream=new ge.TransportPacketStream,i.parseStream=new ge.TransportParseStream,i.elementaryStream=new ge.ElementaryStream,i.videoTimestampRolloverStream=new ge.TimestampRolloverStream("video"),i.audioTimestampRolloverStream=new ge.TimestampRolloverStream("audio"),i.timedMetadataTimestampRolloverStream=new ge.TimestampRolloverStream("timed-metadata"),i.adtsStream=new Te,i.h264Stream=new Ye,i.captionStream=new ge.CaptionStream,i.coalesceStream=new xe(r,i.metadataStream),i.headOfPipeline=i.packetStream,i.packetStream.pipe(i.parseStream).pipe(i.elementaryStream),i.elementaryStream.pipe(i.videoTimestampRolloverStream).pipe(i.h264Stream),i.elementaryStream.pipe(i.audioTimestampRolloverStream).pipe(i.adtsStream),i.elementaryStream.pipe(i.timedMetadataTimestampRolloverStream).pipe(i.metadataStream).pipe(i.coalesceStream),i.h264Stream.pipe(i.captionStream).pipe(i.coalesceStream),i.elementaryStream.on("data",function(e){var t;if("metadata"===e.type){for(t=e.tracks.length;t--;)n||"video"!==e.tracks[t].type?a||"audio"!==e.tracks[t].type||((a=e.tracks[t]).timelineStartInfo.baseMediaDecodeTime=s.baseMediaDecodeTime):(n=e.tracks[t]).timelineStartInfo.baseMediaDecodeTime=s.baseMediaDecodeTime;n&&!i.videoSegmentStream&&(i.coalesceStream.numberOfTracks++,i.videoSegmentStream=new Pe(n,r),i.videoSegmentStream.on("timelineStartInfo",function(e){a&&(a.timelineStartInfo=e,i.audioSegmentStream.setEarliestDts(e.dts))}),i.videoSegmentStream.on("processedGopsInfo",s.trigger.bind(s,"gopInfo")),i.videoSegmentStream.on("baseMediaDecodeTime",function(e){a&&i.audioSegmentStream.setVideoBaseMediaDecodeTime(e)}),i.h264Stream.pipe(i.videoSegmentStream).pipe(i.coalesceStream)),a&&!i.audioSegmentStream&&(i.coalesceStream.numberOfTracks++,i.audioSegmentStream=new Ie(a,r),i.adtsStream.pipe(i.audioSegmentStream).pipe(i.coalesceStream))}}),i.coalesceStream.on("data",this.trigger.bind(this,"data")),i.coalesceStream.on("done",this.trigger.bind(this,"done"))},this.setBaseMediaDecodeTime=function(e){var t=this.transmuxPipeline_;this.baseMediaDecodeTime=e,a&&(a.timelineStartInfo.dts=void 0,a.timelineStartInfo.pts=void 0,Ne(a),a.timelineStartInfo.baseMediaDecodeTime=e,t.audioTimestampRolloverStream&&t.audioTimestampRolloverStream.discontinuity()),n&&(t.videoSegmentStream&&(t.videoSegmentStream.gopCache_=[],t.videoTimestampRolloverStream.discontinuity()),n.timelineStartInfo.dts=void 0,n.timelineStartInfo.pts=void 0,Ne(n),t.captionStream.reset(),n.timelineStartInfo.baseMediaDecodeTime=e),t.timedMetadataTimestampRolloverStream&&t.timedMetadataTimestampRolloverStream.discontinuity()},this.setAudioAppendStart=function(e){a&&this.transmuxPipeline_.audioSegmentStream.setAudioAppendStart(e)},this.alignGopsWith=function(e){n&&this.transmuxPipeline_.videoSegmentStream&&this.transmuxPipeline_.videoSegmentStream.alignGopsWith(e)},this.push=function(e){if(i){var t=De(e);t&&"aac"!==this.transmuxPipeline_.type?this.setupAacPipeline():t||"ts"===this.transmuxPipeline_.type||this.setupTsPipeline(),i=!1}this.transmuxPipeline_.headOfPipeline.push(e)},this.flush=function(){i=!0,this.transmuxPipeline_.headOfPipeline.flush()},this.resetCaptions=function(){this.transmuxPipeline_.captionStream&&this.transmuxPipeline_.captionStream.reset()}}).prototype=new G;var Qe={Transmuxer:Re,VideoSegmentStream:Pe,AudioSegmentStream:Ie,AUDIO_PROPERTIES:$e,VIDEO_PROPERTIES:Ke},Je={generator:V,Transmuxer:Qe.Transmuxer,AudioSegmentStream:Qe.AudioSegmentStream,VideoSegmentStream:Qe.VideoSegmentStream},Ze=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},et=function(){function r(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(e,t,i){return t&&r(e.prototype,t),i&&r(e,i),e}}(),tt=function(){function t(e){Ze(this,t),this.options=e||{},this.init()}return et(t,[{key:"init",value:function(){var e;this.transmuxer&&this.transmuxer.dispose(),this.transmuxer=new Je.Transmuxer(this.options),(e=this.transmuxer).on("data",function(e){var t=e.initSegment;e.initSegment={data:t.buffer,byteOffset:t.byteOffset,byteLength:t.byteLength};var i=e.data;e.data=i.buffer,H.postMessage({action:"data",segment:e,byteOffset:i.byteOffset,byteLength:i.byteLength},[e.data])}),e.captionStream&&e.captionStream.on("data",function(e){H.postMessage({action:"caption",data:e})}),e.on("done",function(e){H.postMessage({action:"done"})}),e.on("gopInfo",function(e){H.postMessage({action:"gopInfo",gopInfo:e})})}},{key:"push",value:function(e){var t=new Uint8Array(e.data,e.byteOffset,e.byteLength);this.transmuxer.push(t)}},{key:"reset",value:function(){this.init()}},{key:"setTimestampOffset",value:function(e){var t=e.timestampOffset||0;this.transmuxer.setBaseMediaDecodeTime(Math.round(9e4*t))}},{key:"setAudioAppendStart",value:function(e){this.transmuxer.setAudioAppendStart(Math.ceil(9e4*e.appendStart))}},{key:"flush",value:function(e){this.transmuxer.flush()}},{key:"resetCaptions",value:function(){this.transmuxer.resetCaptions()}},{key:"alignGopsWith",value:function(e){this.transmuxer.alignGopsWith(e.gopsToAlignWith.slice())}}]),t}();new function(e){e.onmessage=function(e){"init"===e.data.action&&e.data.options?this.messageHandlers=new tt(e.data.options):(this.messageHandlers||(this.messageHandlers=new tt),e.data&&e.data.action&&"init"!==e.data.action&&this.messageHandlers[e.data.action]&&this.messageHandlers[e.data.action](e.data))}}(rt)}()}),uo=function(e){return/mp4a\.\d+.\d+/i.test(e)},co=function(e){return/avc1\.[\da-f]+/i.test(e)},lo=function(e){return e.map(function(e){return e.replace(/avc1\.(\d+)\.(\d+)/i,function(e,t,i){return"avc1."+("00"+Number(t).toString(16)).slice(-2)+"00"+("00"+Number(i).toString(16)).slice(-2)})})},ho=function(n){function a(e,t){y(this,a);var i=b(this,n.call(this,en.EventTarget));i.timestampOffset_=0,i.pendingBuffers_=[],i.bufferUpdating_=!1,i.mediaSource_=e,i.codecs_=t,i.audioCodec_=null,i.videoCodec_=null,i.audioDisabled_=!1,i.appendAudioInitSegment_=!0,i.gopBuffer_=[],i.timeMapping_=0,i.safeAppend_=11<=en.browser.IE_VERSION;var r={remux:!1,alignGopsAtEnd:i.safeAppend_};return i.codecs_.forEach(function(e){uo(e)?i.audioCodec_=e:co(e)&&(i.videoCodec_=e)}),i.transmuxer_=new oo,i.transmuxer_.postMessage({action:"init",options:r}),i.transmuxer_.onmessage=function(e){return"data"===e.data.action?i.data_(e):"done"===e.data.action?i.done_(e):"gopInfo"===e.data.action?i.appendGopInfo_(e):void 0},Object.defineProperty(i,"timestampOffset",{get:function(){return this.timestampOffset_},set:function(e){"number"==typeof e&&0<=e&&(this.timestampOffset_=e,this.appendAudioInitSegment_=!0,this.gopBuffer_.length=0,this.timeMapping_=0,this.transmuxer_.postMessage({action:"setTimestampOffset",timestampOffset:e}))}}),Object.defineProperty(i,"appendWindowStart",{get:function(){return(this.videoBuffer_||this.audioBuffer_).appendWindowStart},set:function(e){this.videoBuffer_&&(this.videoBuffer_.appendWindowStart=e),this.audioBuffer_&&(this.audioBuffer_.appendWindowStart=e)}}),Object.defineProperty(i,"updating",{get:function(){return!!(this.bufferUpdating_||!this.audioDisabled_&&this.audioBuffer_&&this.audioBuffer_.updating||this.videoBuffer_&&this.videoBuffer_.updating)}}),Object.defineProperty(i,"buffered",{get:function(){var e=null,t=null,i=0,r=[],n=[];if(!this.videoBuffer_&&!this.audioBuffer_)return en.createTimeRange();if(!this.videoBuffer_)return this.audioBuffer_.buffered;if(!this.audioBuffer_)return this.videoBuffer_.buffered;if(this.audioDisabled_)return this.videoBuffer_.buffered;if(0===this.videoBuffer_.buffered.length&&0===this.audioBuffer_.buffered.length)return en.createTimeRange();for(var a=this.videoBuffer_.buffered,s=this.audioBuffer_.buffered,o=a.length;o--;)r.push({time:a.start(o),type:"start"}),r.push({time:a.end(o),type:"end"});for(o=s.length;o--;)r.push({time:s.start(o),type:"start"}),r.push({time:s.end(o),type:"end"});for(r.sort(function(e,t){return e.time-t.time}),o=0;o<r.length;o++)"start"===r[o].type?2===++i&&(e=r[o].time):"end"===r[o].type&&1===--i&&(t=r[o].time),null!==e&&null!==t&&(n.push([e,t]),t=e=null);return en.createTimeRanges(n)}}),i}return _(a,n),a.prototype.data_=function(e){var t=e.data.segment;t.data=new Uint8Array(t.data,e.data.byteOffset,e.data.byteLength),t.initSegment=new Uint8Array(t.initSegment.data,t.initSegment.byteOffset,t.initSegment.byteLength),function(e,t,i){var r=t.player_;if(i.captions&&i.captions.length)for(var n in e.inbandTextTracks_||(e.inbandTextTracks_={}),i.captionStreams)if(!e.inbandTextTracks_[n]){r.tech_.trigger({type:"usage",name:"hls-608"});var a=r.textTracks().getTrackById(n);e.inbandTextTracks_[n]=a||r.addRemoteTextTrack({kind:"captions",id:n,label:n},!1).track}i.metadata&&i.metadata.length&&!e.metadataTrack_&&(e.metadataTrack_=r.addRemoteTextTrack({kind:"metadata",label:"Timed Metadata"},!1).track,e.metadataTrack_.inBandMetadataTrackDispatchType=i.metadata.dispatchType)}(this,this.mediaSource_,t),this.pendingBuffers_.push(t)},a.prototype.done_=function(e){"closed"!==this.mediaSource_.readyState?this.processPendingSegments_():this.pendingBuffers_.length=0},a.prototype.createRealSourceBuffers_=function(){var r=this,n=["audio","video"];n.forEach(function(t){if(r[t+"Codec_"]&&!r[t+"Buffer_"]){var i=null;if(r.mediaSource_[t+"Buffer_"])(i=r.mediaSource_[t+"Buffer_"]).updating=!1;else{var e=t+'/mp4;codecs="'+r[t+"Codec_"]+'"';i=function(e,t){var i=e.addSourceBuffer(t),r=Object.create(null);r.updating=!1,r.realBuffer_=i;var n=function(t){"function"==typeof i[t]?r[t]=function(){return i[t].apply(i,arguments)}:"undefined"==typeof r[t]&&Object.defineProperty(r,t,{get:function(){return i[t]},set:function(e){return i[t]=e}})};for(var a in i)n(a);return r}(r.mediaSource_.nativeMediaSource_,e),r.mediaSource_[t+"Buffer_"]=i}r[t+"Buffer_"]=i,["update","updatestart","updateend"].forEach(function(e){i.addEventListener(e,function(){if("audio"!==t||!r.audioDisabled_)return"updateend"===e&&(r[t+"Buffer_"].updating=!1),n.every(function(e){return!("audio"!==e||!r.audioDisabled_)||(t===e||!r[e+"Buffer_"]||!r[e+"Buffer_"].updating)})?r.trigger(e):void 0})})}})},a.prototype.appendBuffer=function(e){if(this.bufferUpdating_=!0,this.audioBuffer_&&this.audioBuffer_.buffered.length){var t=this.audioBuffer_.buffered;this.transmuxer_.postMessage({action:"setAudioAppendStart",appendStart:t.end(t.length-1)})}this.videoBuffer_&&this.transmuxer_.postMessage({action:"alignGopsWith",gopsToAlignWith:function(e,t,i){if(!t||!e.length)return[];var r=Math.ceil(9e4*(t.currentTime()-i+3)),n=void 0;for(n=0;n<e.length&&!(e[n].pts>r);n++);return e.slice(n)}(this.gopBuffer_,this.mediaSource_.player_,this.timeMapping_)}),this.transmuxer_.postMessage({action:"push",data:e.buffer,byteOffset:e.byteOffset,byteLength:e.byteLength},[e.buffer]),this.transmuxer_.postMessage({action:"flush"})},a.prototype.appendGopInfo_=function(e){this.gopBuffer_=function(e,t,i){if(!t.length)return e;if(i)return t.slice();for(var r=t[0].pts,n=0;n<e.length&&!(e[n].pts>=r);n++);return e.slice(0,n).concat(t)}(this.gopBuffer_,e.data.gopInfo,this.safeAppend_)},a.prototype.remove=function(e,t){if(this.videoBuffer_&&(this.videoBuffer_.updating=!0,this.videoBuffer_.remove(e,t),this.gopBuffer_=function(e,t,i,r){for(var n=Math.ceil(9e4*(t-r)),a=Math.ceil(9e4*(i-r)),s=e.slice(),o=e.length;o--&&!(e[o].pts<=a););if(-1===o)return s;for(var u=o+1;u--&&!(e[u].pts<=n););return u=Math.max(u,0),s.splice(u,o-u+1),s}(this.gopBuffer_,e,t,this.timeMapping_)),!this.audioDisabled_&&this.audioBuffer_&&(this.audioBuffer_.updating=!0,this.audioBuffer_.remove(e,t)),Xs(e,t,this.metadataTrack_),this.inbandTextTracks_)for(var i in this.inbandTextTracks_)Xs(e,t,this.inbandTextTracks_[i])},a.prototype.processPendingSegments_=function(){var e={video:{segments:[],bytes:0},audio:{segments:[],bytes:0},captions:[],metadata:[]};e=this.pendingBuffers_.reduce(function(e,t){var i=t.type,r=t.data,n=t.initSegment;return e[i].segments.push(r),e[i].bytes+=r.byteLength,e[i].initSegment=n,t.captions&&(e.captions=e.captions.concat(t.captions)),t.info&&(e[i].info=t.info),t.metadata&&(e.metadata=e.metadata.concat(t.metadata)),e},e),this.videoBuffer_||this.audioBuffer_||(0===e.video.bytes&&(this.videoCodec_=null),0===e.audio.bytes&&(this.audioCodec_=null),this.createRealSourceBuffers_()),e.audio.info&&this.mediaSource_.trigger({type:"audioinfo",info:e.audio.info}),e.video.info&&this.mediaSource_.trigger({type:"videoinfo",info:e.video.info}),this.appendAudioInitSegment_&&(!this.audioDisabled_&&this.audioBuffer_&&(e.audio.segments.unshift(e.audio.initSegment),e.audio.bytes+=e.audio.initSegment.byteLength),this.appendAudioInitSegment_=!1);var t=!1;this.videoBuffer_&&e.video.bytes?(e.video.segments.unshift(e.video.initSegment),e.video.bytes+=e.video.initSegment.byteLength,this.concatAndAppendSegments_(e.video,this.videoBuffer_),$s(this,e.captions,e.metadata)):!this.videoBuffer_||!this.audioDisabled_&&this.audioBuffer_||(t=!0),!this.audioDisabled_&&this.audioBuffer_&&this.concatAndAppendSegments_(e.audio,this.audioBuffer_),this.pendingBuffers_.length=0,t&&this.trigger("updateend"),this.bufferUpdating_=!1},a.prototype.concatAndAppendSegments_=function(e,t){var i=0,r=void 0;if(e.bytes){r=new Uint8Array(e.bytes),e.segments.forEach(function(e){r.set(e,i),i+=e.byteLength});try{t.updating=!0,t.appendBuffer(r)}catch(e){this.mediaSource_.player_&&this.mediaSource_.player_.error({code:-3,type:"APPEND_BUFFER_ERR",message:e.message,originalError:e})}}},a.prototype.abort=function(){this.videoBuffer_&&this.videoBuffer_.abort(),!this.audioDisabled_&&this.audioBuffer_&&this.audioBuffer_.abort(),this.transmuxer_&&this.transmuxer_.postMessage({action:"reset"}),this.pendingBuffers_.length=0,this.bufferUpdating_=!1},a}(en.EventTarget),po=function(t){function i(){y(this,i);var a=b(this,t.call(this)),e=void 0;for(e in a.nativeMediaSource_=new g.MediaSource,a.nativeMediaSource_)e in i.prototype||"function"!=typeof a.nativeMediaSource_[e]||(a[e]=a.nativeMediaSource_[e].bind(a.nativeMediaSource_));return a.duration_=NaN,Object.defineProperty(a,"duration",{get:function(){return this.duration_===1/0?this.duration_:this.nativeMediaSource_.duration},set:function(e){(this.duration_=e)===1/0||(this.nativeMediaSource_.duration=e)}}),Object.defineProperty(a,"seekable",{get:function(){return this.duration_===1/0?en.createTimeRanges([[0,this.nativeMediaSource_.duration]]):this.nativeMediaSource_.seekable}}),Object.defineProperty(a,"readyState",{get:function(){return this.nativeMediaSource_.readyState}}),Object.defineProperty(a,"activeSourceBuffers",{get:function(){return this.activeSourceBuffers_}}),a.sourceBuffers=[],a.activeSourceBuffers_=[],a.updateActiveSourceBuffers_=function(){if(a.activeSourceBuffers_.length=0,1===a.sourceBuffers.length){var e=a.sourceBuffers[0];return e.appendAudioInitSegment_=!0,e.audioDisabled_=!e.audioCodec_,void a.activeSourceBuffers_.push(e)}for(var i=!1,r=!0,t=0;t<a.player_.audioTracks().length;t++){var n=a.player_.audioTracks()[t];if(n.enabled&&"main"!==n.kind){r=!(i=!0);break}}a.sourceBuffers.forEach(function(e,t){if(e.appendAudioInitSegment_=!0,e.videoCodec_&&e.audioCodec_)e.audioDisabled_=i;else if(e.videoCodec_&&!e.audioCodec_)e.audioDisabled_=!0,r=!1;else if(!e.videoCodec_&&e.audioCodec_&&(e.audioDisabled_=t?r:!r,e.audioDisabled_))return;a.activeSourceBuffers_.push(e)})},a.onPlayerMediachange_=function(){a.sourceBuffers.forEach(function(e){e.appendAudioInitSegment_=!0})},a.onHlsReset_=function(){a.sourceBuffers.forEach(function(e){e.transmuxer_&&e.transmuxer_.postMessage({action:"resetCaptions"})})},a.onHlsSegmentTimeMapping_=function(t){a.sourceBuffers.forEach(function(e){return e.timeMapping_=t.mapping})},["sourceopen","sourceclose","sourceended"].forEach(function(e){this.nativeMediaSource_.addEventListener(e,this.trigger.bind(this))},a),a.on("sourceopen",function(e){var t=p.querySelector('[src="'+a.url_+'"]');t&&(a.player_=en(t.parentNode),a.player_.tech_.on("hls-reset",a.onHlsReset_),a.player_.tech_.on("hls-segment-time-mapping",a.onHlsSegmentTimeMapping_),a.player_.audioTracks&&a.player_.audioTracks()&&(a.player_.audioTracks().on("change",a.updateActiveSourceBuffers_),a.player_.audioTracks().on("addtrack",a.updateActiveSourceBuffers_),a.player_.audioTracks().on("removetrack",a.updateActiveSourceBuffers_)),a.player_.on("mediachange",a.onPlayerMediachange_))}),a.on("sourceended",function(e){for(var t=Ys(a.duration),i=0;i<a.sourceBuffers.length;i++){var r=a.sourceBuffers[i],n=r.metadataTrack_&&r.metadataTrack_.cues;n&&n.length&&(n[n.length-1].endTime=t)}}),a.on("sourceclose",function(e){this.sourceBuffers.forEach(function(e){e.transmuxer_&&e.transmuxer_.terminate()}),this.sourceBuffers.length=0,this.player_&&(this.player_.audioTracks&&this.player_.audioTracks()&&(this.player_.audioTracks().off("change",this.updateActiveSourceBuffers_),this.player_.audioTracks().off("addtrack",this.updateActiveSourceBuffers_),this.player_.audioTracks().off("removetrack",this.updateActiveSourceBuffers_)),this.player_.el_&&(this.player_.off("mediachange",this.onPlayerMediachange_),this.player_.tech_.off("hls-reset",this.onHlsReset_),this.player_.tech_.off("hls-segment-time-mapping",this.onHlsSegmentTimeMapping_)))}),a}return _(i,t),i.prototype.addSeekableRange_=function(e,t){var i=void 0;if(this.duration!==1/0)throw(i=new Error("MediaSource.addSeekableRange() can only be invoked when the duration is Infinity")).name="InvalidStateError",i.code=11,i;(t>this.nativeMediaSource_.duration||isNaN(this.nativeMediaSource_.duration))&&(this.nativeMediaSource_.duration=t)},i.prototype.addSourceBuffer=function(e){var n,t,i=void 0,r=(n={type:"",parameters:{}},t=e.trim().split(";"),n.type=t.shift().trim(),t.forEach(function(e){var t=e.trim().split("=");if(1<t.length){var i=t[0].replace(/"/g,"").trim(),r=t[1].replace(/"/g,"").trim();n.parameters[i]=r}}),n);if(/^(video|audio)\/mp2t$/i.test(r.type)){var a=[];r.parameters&&r.parameters.codecs&&(a=r.parameters.codecs.split(","),a=(a=lo(a)).filter(function(e){return uo(e)||co(e)})),0===a.length&&(a=["avc1.4d400d","mp4a.40.2"]),i=new ho(this,a),0!==this.sourceBuffers.length&&(this.sourceBuffers[0].createRealSourceBuffers_(),i.createRealSourceBuffers_(),this.sourceBuffers[0].audioDisabled_=!0)}else i=this.nativeMediaSource_.addSourceBuffer(e);return this.sourceBuffers.push(i),i},i}(en.EventTarget),fo=0;en.mediaSources={};var mo=function(e,t){var i=en.mediaSources[e];if(!i)throw new Error("Media Source not found (Video.js)");i.trigger({type:"sourceopen",swfId:t})},go=function(){return!!g.MediaSource&&!!g.MediaSource.isTypeSupported&&g.MediaSource.isTypeSupported('video/mp4;codecs="avc1.4d400d,mp4a.40.2"')},yo=function(){if(this.MediaSource={open:mo,supportsNativeMediaSources:go},go())return new po;throw new Error("Cannot use create a virtual MediaSource for this video")};yo.open=mo,yo.supportsNativeMediaSources=go;var vo={createObjectURL:function(e){var t=void 0;return e instanceof po?(t=g.URL.createObjectURL(e.nativeMediaSource_),e.url_=t):e instanceof po?(t="blob:vjs-media-source/"+fo,fo++,en.mediaSources[t]=e,t):(t=g.URL.createObjectURL(e),e.url_=t)}};en.MediaSource=yo,en.URL=vo;var _o=en.mergeOptions,bo=function(e,t){for(var s=_o(e,{duration:t.duration,minimumUpdatePeriod:t.minimumUpdatePeriod}),i=0;i<t.playlists.length;i++){var r=ds(s,t.playlists[i]);r&&(s=r)}return ls(t,function(e,t,i,r){if(e.playlists&&e.playlists.length){var n=e.playlists[0].uri,a=ds(s,e.playlists[0]);a&&((s=a).mediaGroups[t][i][r].playlists[0]=s.playlists[n])}}),s},To=function(a){function s(e,t,i,r){y(this,s);var n=b(this,a.call(this));if(n.hls_=t,n.withCredentials=i,!e)throw new Error("A non-empty playlist URL or playlist is required");return n.on("minimumUpdatePeriod",function(){n.refreshXml_()}),n.on("mediaupdatetimeout",function(){n.refreshMedia_()}),"string"==typeof e?(n.srcUrl=e,n.state="HAVE_NOTHING",b(n)):(n.masterPlaylistLoader_=r,n.state="HAVE_METADATA",n.started=!0,n.media(e),g.setTimeout(function(){n.trigger("loadedmetadata")},0),n)}return _(s,a),s.prototype.dispose=function(){this.stopRequest(),g.clearTimeout(this.mediaUpdateTimeout)},s.prototype.stopRequest=function(){if(this.request){var e=this.request;this.request=null,e.onreadystatechange=null,e.abort()}},s.prototype.media=function(e){if(!e)return this.media_;if("HAVE_NOTHING"===this.state)throw new Error("Cannot switch media playlist from "+this.state);var t=this.state;if("string"==typeof e){if(!this.master.playlists[e])throw new Error("Unknown playlist URI: "+e);e=this.master.playlists[e]}var i=!this.media_||e.uri!==this.media_.uri;this.state="HAVE_METADATA",i&&(this.media_&&this.trigger("mediachanging"),this.media_=e,this.refreshMedia_(),"HAVE_MASTER"!==t&&this.trigger("mediachange"))},s.prototype.pause=function(){this.stopRequest(),"HAVE_NOTHING"===this.state&&(this.started=!1)},s.prototype.load=function(){this.started?this.trigger("loadedplaylist"):this.start()},s.prototype.parseMasterXml=function(){var a=Kn(this.masterXml_,{manifestUri:this.srcUrl,clientOffset:this.clientOffset_});a.uri=this.srcUrl;for(var e=0;e<a.playlists.length;e++){var t="placeholder-uri-"+e;a.playlists[e].uri=t,a.playlists[t]=a.playlists[e]}return ls(a,function(e,t,i,r){if(e.playlists&&e.playlists.length){var n="placeholder-uri-"+t+"-"+i+"-"+r;e.playlists[0].uri=n,a.playlists[n]=e.playlists[0]}}),hs(a),ps(a),a},s.prototype.start=function(){var i=this;this.started=!0,this.request=this.hls_.xhr({uri:this.srcUrl,withCredentials:this.withCredentials},function(e,t){if(i.request){if(i.request=null,e)return i.error={status:t.status,message:"DASH playlist request error at URL: "+i.srcUrl,responseText:t.responseText,code:2},"HAVE_NOTHING"===i.state&&(i.started=!1),i.trigger("error");i.masterXml_=t.responseText,t.responseHeaders&&t.responseHeaders.date?i.masterLoaded_=Date.parse(t.responseHeaders.date):i.masterLoaded_=Date.now(),i.syncClientServerClock_(i.onClientServerClockSync_.bind(i))}})},s.prototype.syncClientServerClock_=function(r){var n=this,a=Qn(this.masterXml_);return null===a?(this.clientOffset_=this.masterLoaded_-Date.now(),r()):"DIRECT"===a.method?(this.clientOffset_=a.value-Date.now(),r()):void(this.request=this.hls_.xhr({uri:os(this.srcUrl,a.value),method:a.method,withCredentials:this.withCredentials},function(e,t){if(n.request){if(e)return n.clientOffset_=n.masterLoaded_-Date.now(),r();var i=void 0;i="HEAD"===a.method?t.responseHeaders&&t.responseHeaders.date?Date.parse(t.responseHeaders.date):n.masterLoaded_:Date.parse(t.responseText),n.clientOffset_=i-Date.now(),r()}}))},s.prototype.onClientServerClockSync_=function(){var e=this;this.master=this.parseMasterXml(),this.state="HAVE_MASTER",this.trigger("loadedplaylist"),this.media_||this.media(this.master.playlists[0]),g.setTimeout(function(){e.trigger("loadedmetadata")},0),this.master.minimumUpdatePeriod&&g.setTimeout(function(){e.trigger("minimumUpdatePeriod")},this.master.minimumUpdatePeriod)},s.prototype.refreshXml_=function(){var r=this;this.request=this.hls_.xhr({uri:this.srcUrl,withCredentials:this.withCredentials},function(e,t){if(r.request){if(r.request=null,e)return r.error={status:t.status,message:"DASH playlist request error at URL: "+r.srcUrl,responseText:t.responseText,code:2},"HAVE_NOTHING"===r.state&&(r.started=!1),r.trigger("error");r.masterXml_=t.responseText;var i=r.parseMasterXml();r.master=bo(r.master,i),g.setTimeout(function(){r.trigger("minimumUpdatePeriod")},r.master.minimumUpdatePeriod)}})},s.prototype.refreshMedia_=function(){var e=this,t=void 0,i=void 0;this.masterPlaylistLoader_?(t=this.masterPlaylistLoader_.master,i=this.masterPlaylistLoader_.parseMasterXml()):(t=this.master,i=this.parseMasterXml());var r=bo(t,i);r?(this.masterPlaylistLoader_?this.masterPlaylistLoader_.master=r:this.master=r,this.media_=r.playlists[this.media_.uri]):this.trigger("playlistunchanged"),this.media().endList||(this.mediaUpdateTimeout=g.setTimeout(function(){e.trigger("mediaupdatetimeout")},fs(this.media(),!!r))),this.trigger("loadedplaylist")},s}(en.EventTarget),So=function(e){return en.log.debug?en.log.debug.bind(en,"VHS:",e+" >"):function(){}};function Eo(){}var wo=function(){function n(e,t,i,r){y(this,n),this.callbacks_=[],this.pendingCallback_=null,this.timestampOffset_=0,this.mediaSource=e,this.processedAppend_=!1,this.type_=i,this.mimeType_=t,this.logger_=So("SourceUpdater["+i+"]["+t+"]"),"closed"===e.readyState?e.addEventListener("sourceopen",this.createSourceBuffer_.bind(this,t,r)):this.createSourceBuffer_(t,r)}return n.prototype.createSourceBuffer_=function(e,t){var i=this;this.sourceBuffer_=this.mediaSource.addSourceBuffer(e),this.logger_("created SourceBuffer"),t&&(t.trigger("sourcebufferadded"),this.mediaSource.sourceBuffers.length<2)?t.on("sourcebufferadded",function(){i.start_()}):this.start_()},n.prototype.start_=function(){var t=this;this.started_=!0,this.onUpdateendCallback_=function(){var e=t.pendingCallback_;t.pendingCallback_=null,t.logger_("buffered ["+Gs(t.buffered())+"]"),e&&e(),t.runCallback_()},this.sourceBuffer_.addEventListener("updateend",this.onUpdateendCallback_),this.runCallback_()},n.prototype.abort=function(e){var t=this;this.processedAppend_&&this.queueCallback_(function(){t.sourceBuffer_.abort()},e)},n.prototype.appendBuffer=function(e,t){var i=this;this.processedAppend_=!0,this.queueCallback_(function(){i.sourceBuffer_.appendBuffer(e)},t)},n.prototype.buffered=function(){return this.sourceBuffer_?this.sourceBuffer_.buffered:en.createTimeRanges()},n.prototype.remove=function(e,t){var i=this;this.processedAppend_&&this.queueCallback_(function(){i.logger_("remove ["+e+" => "+t+"]"),i.sourceBuffer_.remove(e,t)},Eo)},n.prototype.updating=function(){return!this.sourceBuffer_||this.sourceBuffer_.updating||this.pendingCallback_},n.prototype.timestampOffset=function(e){var t=this;return"undefined"!=typeof e&&(this.queueCallback_(function(){t.sourceBuffer_.timestampOffset=e}),this.timestampOffset_=e),this.timestampOffset_},n.prototype.queueCallback_=function(e,t){this.callbacks_.push([e.bind(this),t]),this.runCallback_()},n.prototype.runCallback_=function(){var e=void 0;!this.updating()&&this.callbacks_.length&&this.started_&&(e=this.callbacks_.shift(),this.pendingCallback_=e[1],e[0]())},n.prototype.dispose=function(){this.sourceBuffer_.removeEventListener("updateend",this.onUpdateendCallback_),this.sourceBuffer_&&"open"===this.mediaSource.readyState&&this.sourceBuffer_.abort()},n}(),ko={GOAL_BUFFER_LENGTH:30,MAX_GOAL_BUFFER_LENGTH:60,GOAL_BUFFER_LENGTH_RATE:1,BANDWIDTH_VARIANCE:1.2,BUFFER_LOW_WATER_LINE:0,MAX_BUFFER_LOW_WATER_LINE:30,BUFFER_LOW_WATER_LINE_RATE:1},Co=2,Ao=-101,Lo=-102,Oo=function(e){var t,i,r={};return e.byterange&&(r.Range=(t=e.byterange,i=t.offset+t.length-1,"bytes="+t.offset+"-"+i)),r},Po=function(e){e.forEach(function(e){e.abort()})},Io=function(e,t){return t.timedout?{status:t.status,message:"HLS request timed-out at URL: "+t.uri,code:Ao,xhr:t}:t.aborted?{status:t.status,message:"HLS request aborted at URL: "+t.uri,code:Lo,xhr:t}:e?{status:t.status,message:"HLS request errored at URL: "+t.uri,code:Co,xhr:t}:null},Ro=function(s,o,u){var c=[],l=0;return function(e,t){if(e&&(Po(s),c.push(e)),(l+=1)===s.length){if(t.endOfAllRequests=Date.now(),0<c.length){var i=c.reduce(function(e,t){return t.code>e.code?t:e});return u(i,t)}return t.encryptedBytes?(n=t,a=u,(r=o).addEventListener("message",function e(t){if(t.data.source===n.requestId){r.removeEventListener("message",e);var i=t.data.decrypted;return n.bytes=new Uint8Array(i.bytes,i.byteOffset,i.byteLength),a(null,n)}}),void r.postMessage(Ms({source:n.requestId,encrypted:n.encryptedBytes,key:n.key.bytes,iv:n.key.iv}),[n.encryptedBytes.buffer,n.key.bytes.buffer])):u(null,t)}var r,n,a}},xo=function(n,a){return function(e){var t,i,r;return n.stats=en.mergeOptions(n.stats,(i=(t=e).target,(r={bandwidth:1/0,bytesReceived:0,roundTripTime:Date.now()-i.requestTime||0}).bytesReceived=t.loaded,r.bandwidth=Math.floor(r.bytesReceived/r.roundTripTime*8*1e3),r)),!n.stats.firstBytesReceivedAt&&n.stats.bytesReceived&&(n.stats.firstBytesReceivedAt=Date.now()),a(e,n)}},Do=function(e,t,i,r,n,a){var s,o,u,c,l=[],d=Ro(l,i,a);if(r.key){var h=e(en.mergeOptions(t,{uri:r.key.resolvedUri,responseType:"arraybuffer"}),(s=r,o=d,function(e,t){var i=t.response,r=Io(e,t);if(r)return o(r,s);if(16!==i.byteLength)return o({status:t.status,message:"Invalid HLS key at URL: "+t.uri,code:Co,xhr:t},s);var n=new DataView(i);return s.key.bytes=new Uint32Array([n.getUint32(0),n.getUint32(4),n.getUint32(8),n.getUint32(12)]),o(null,s)}));l.push(h)}if(r.map&&!r.map.bytes){var p=e(en.mergeOptions(t,{uri:r.map.resolvedUri,responseType:"arraybuffer",headers:Oo(r.map)}),(u=r,c=d,function(e,t){var i=t.response,r=Io(e,t);return r?c(r,u):0===i.byteLength?c({status:t.status,message:"Empty HLS segment content at URL: "+t.uri,code:Co,xhr:t},u):(u.map.bytes=new Uint8Array(t.response),c(null,u))}));l.push(p)}var f,m,g=e(en.mergeOptions(t,{uri:r.resolvedUri,responseType:"arraybuffer",headers:Oo(r)}),(f=r,m=d,function(e,t){var i,r=t.response,n=Io(e,t);return n?m(n,f):0===r.byteLength?m({status:t.status,message:"Empty HLS segment content at URL: "+t.uri,code:Co,xhr:t},f):(f.stats={bandwidth:(i=t).bandwidth,bytesReceived:i.bytesReceived||0,roundTripTime:i.roundTripTime||0},f.key?f.encryptedBytes=new Uint8Array(t.response):f.bytes=new Uint8Array(t.response),m(null,f))}));return g.addEventListener("progress",xo(r,n)),l.push(g),function(){return Po(l)}},Uo={videoCodec:"avc1",videoObjectTypeIndicator:".4d400d",audioProfile:"2"},No=function(){var e,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"",i={codecCount:0};return i.codecCount=t.split(",").length,i.codecCount=i.codecCount||2,(e=/(^|\s|,)+(avc[13])([^ ,]*)/i.exec(t))&&(i.videoCodec=e[2],i.videoObjectTypeIndicator=e[3]),i.audioProfile=/(^|\s|,)+mp4a.[0-9A-Fa-f]+\.([0-9A-Fa-f]+)/i.exec(t),i.audioProfile=i.audioProfile&&i.audioProfile[2],i},Mo=function(e,t,i){return e+"/"+t+'; codecs="'+i.filter(function(e){return!!e}).join(", ")+'"'},Bo=function(e,t){var i,r,n=(i=t).segments&&i.segments.length&&i.segments[0].map?"mp4":"mp2t",a=(r=t.attributes||{}).CODECS?No(r.CODECS):Uo,s=t.attributes||{},o=!0,u=!1;if(!t)return[];if(e.mediaGroups.AUDIO&&s.AUDIO){var c=e.mediaGroups.AUDIO[s.AUDIO];if(c)for(var l in o=!(u=!0),c)if(!c[l].uri&&!c[l].playlists){o=!0;break}}u&&!a.audioProfile&&(o||(a.audioProfile=function(e,t){if(!e.mediaGroups.AUDIO||!t)return null;var i=e.mediaGroups.AUDIO[t];if(!i)return null;for(var r in i){var n=i[r];if(n.default&&n.playlists)return No(n.playlists[0].attributes.CODECS).audioProfile}return null}(e,s.AUDIO)),a.audioProfile||(en.log.warn("Multiple audio tracks present but no audio codec string is specified. Attempting to use the default audio codec (mp4a.40.2)"),a.audioProfile=Uo.audioProfile));var d={};a.videoCodec&&(d.video=""+a.videoCodec+a.videoObjectTypeIndicator),a.audioProfile&&(d.audio="mp4a.40."+a.audioProfile);var h=Mo("audio",n,[d.audio]),p=Mo("video",n,[d.video]),f=Mo("video",n,[d.video,d.audio]);return u?!o&&d.video?[p,h]:o||d.video?[f,h]:[h,h]:d.video?[f]:[h]},jo=function(e,t){var i;return e&&(i=g.getComputedStyle(e))?i[t]:""},Fo=function(e,r){var n=e.slice();e.sort(function(e,t){var i=r(e,t);return 0===i?n.indexOf(e)-n.indexOf(t):i})},Ho=function(e,t){var i=void 0,r=void 0;return e.attributes.BANDWIDTH&&(i=e.attributes.BANDWIDTH),i=i||g.Number.MAX_VALUE,t.attributes.BANDWIDTH&&(r=t.attributes.BANDWIDTH),i-(r=r||g.Number.MAX_VALUE)},qo=function(e,t,i){if(!e||!t)return!1;var r=i===e.segments.length;return e.endList&&"open"===t.readyState&&r},Vo=function(e){return"number"==typeof e&&isFinite(e)},Wo=function(i){function r(e){y(this,r);var t=b(this,i.call(this));if(!e)throw new TypeError("Initialization settings are required");if("function"!=typeof e.currentTime)throw new TypeError("No currentTime getter specified");if(!e.mediaSource)throw new TypeError("No MediaSource specified");return t.bandwidth=e.bandwidth,t.throughput={rate:0,count:0},t.roundTrip=NaN,t.resetStats_(),t.mediaIndex=null,t.hasPlayed_=e.hasPlayed,t.currentTime_=e.currentTime,t.seekable_=e.seekable,t.seeking_=e.seeking,t.duration_=e.duration,t.mediaSource_=e.mediaSource,t.hls_=e.hls,t.loaderType_=e.loaderType,t.startingMedia_=void 0,t.segmentMetadataTrack_=e.segmentMetadataTrack,t.goalBufferLength_=e.goalBufferLength,t.sourceType_=e.sourceType,t.state_="INIT",t.checkBufferTimeout_=null,t.error_=void 0,t.currentTimeline_=-1,t.pendingSegment_=null,t.mimeType_=null,t.sourceUpdater_=null,t.xhrOptions_=null,t.activeInitSegmentId_=null,t.initSegments_={},t.decrypter_=e.decrypter,t.syncController_=e.syncController,t.syncPoint_={segmentIndex:0,time:0},t.syncController_.on("syncinfoupdate",function(){return t.trigger("syncinfoupdate")}),t.mediaSource_.addEventListener("sourceopen",function(){return t.ended_=!1}),t.fetchAtBuffer_=!1,t.logger_=So("SegmentLoader["+t.loaderType_+"]"),Object.defineProperty(t,"state",{get:function(){return this.state_},set:function(e){e!==this.state_&&(this.logger_(this.state_+" -> "+e),this.state_=e)}}),t}return _(r,i),r.prototype.resetStats_=function(){this.mediaBytesTransferred=0,this.mediaRequests=0,this.mediaRequestsAborted=0,this.mediaRequestsTimedout=0,this.mediaRequestsErrored=0,this.mediaTransferDuration=0,this.mediaSecondsLoaded=0},r.prototype.dispose=function(){this.state="DISPOSED",this.pause(),this.abort_(),this.sourceUpdater_&&this.sourceUpdater_.dispose(),this.resetStats_()},r.prototype.abort=function(){"WAITING"===this.state?(this.abort_(),this.state="READY",this.paused()||this.monitorBuffer_()):this.pendingSegment_&&(this.pendingSegment_=null)},r.prototype.abort_=function(){this.pendingSegment_&&this.pendingSegment_.abortRequests(),this.pendingSegment_=null},r.prototype.error=function(e){return"undefined"!=typeof e&&(this.error_=e),this.pendingSegment_=null,this.error_},r.prototype.endOfStream=function(){this.ended_=!0,this.pause(),this.trigger("ended")},r.prototype.buffered_=function(){return this.sourceUpdater_?this.sourceUpdater_.buffered():en.createTimeRanges()},r.prototype.initSegment=function(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1];if(!e)return null;var i=Bs(e),r=this.initSegments_[i];return t&&!r&&e.bytes&&(this.initSegments_[i]=r={resolvedUri:e.resolvedUri,byterange:e.byterange,bytes:e.bytes}),r||e},r.prototype.couldBeginLoading_=function(){return this.playlist_&&(this.sourceUpdater_||this.mimeType_&&"INIT"===this.state)&&!this.paused()},r.prototype.load=function(){if(this.monitorBuffer_(),this.playlist_){if(this.syncController_.setDateTimeMapping(this.playlist_),"INIT"===this.state&&this.couldBeginLoading_())return this.init_();!this.couldBeginLoading_()||"READY"!==this.state&&"INIT"!==this.state||(this.state="READY")}},r.prototype.init_=function(){return this.state="READY",this.sourceUpdater_=new wo(this.mediaSource_,this.mimeType_,this.loaderType_,this.sourceBufferEmitter_),this.resetEverything(),this.monitorBuffer_()},r.prototype.playlist=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(e){var i=this.playlist_,r=this.pendingSegment_;this.playlist_=e,this.xhrOptions_=t,this.hasPlayed_()||(e.syncInfo={mediaSequence:e.mediaSequence,time:0});var n=i?i.id:null;if(this.logger_("playlist update ["+n+" => "+e.id+"]"),this.trigger("syncinfoupdate"),"INIT"===this.state&&this.couldBeginLoading_())return this.init_();if(i&&i.uri===e.uri){var a=e.mediaSequence-i.mediaSequence;this.logger_("live window shift ["+a+"]"),null!==this.mediaIndex&&(this.mediaIndex-=a),r&&(r.mediaIndex-=a,0<=r.mediaIndex&&(r.segment=e.segments[r.mediaIndex])),this.syncController_.saveExpiredSegmentInfo(i,e)}else null!==this.mediaIndex&&this.resyncLoader()}},r.prototype.pause=function(){this.checkBufferTimeout_&&(g.clearTimeout(this.checkBufferTimeout_),this.checkBufferTimeout_=null)},r.prototype.paused=function(){return null===this.checkBufferTimeout_},r.prototype.mimeType=function(e,t){this.mimeType_||(this.mimeType_=e,this.sourceBufferEmitter_=t,"INIT"===this.state&&this.couldBeginLoading_()&&this.init_())},r.prototype.resetEverything=function(){this.ended_=!1,this.resetLoader(),this.remove(0,this.duration_()),this.trigger("reseteverything")},r.prototype.resetLoader=function(){this.fetchAtBuffer_=!1,this.resyncLoader()},r.prototype.resyncLoader=function(){this.mediaIndex=null,this.syncPoint_=null,this.abort()},r.prototype.remove=function(e,t){this.sourceUpdater_&&this.sourceUpdater_.remove(e,t),Xs(e,t,this.segmentMetadataTrack_)},r.prototype.monitorBuffer_=function(){this.checkBufferTimeout_&&g.clearTimeout(this.checkBufferTimeout_),this.checkBufferTimeout_=g.setTimeout(this.monitorBufferTick_.bind(this),1)},r.prototype.monitorBufferTick_=function(){"READY"===this.state&&this.fillBuffer_(),this.checkBufferTimeout_&&g.clearTimeout(this.checkBufferTimeout_),this.checkBufferTimeout_=g.setTimeout(this.monitorBufferTick_.bind(this),500)},r.prototype.fillBuffer_=function(){if(!this.sourceUpdater_.updating()){this.syncPoint_||(this.syncPoint_=this.syncController_.getSyncPoint(this.playlist_,this.duration_(),this.currentTimeline_,this.currentTime_()));var e=this.checkBuffer_(this.buffered_(),this.playlist_,this.mediaIndex,this.hasPlayed_(),this.currentTime_(),this.syncPoint_);if(e)qo(this.playlist_,this.mediaSource_,e.mediaIndex)?this.endOfStream():(e.mediaIndex!==this.playlist_.segments.length-1||"ended"!==this.mediaSource_.readyState||this.seeking_())&&((e.timeline!==this.currentTimeline_||null!==e.startOfSegment&&e.startOfSegment<this.sourceUpdater_.timestampOffset())&&(this.syncController_.reset(),e.timestampOffset=e.startOfSegment),this.loadSegment_(e))}},r.prototype.checkBuffer_=function(e,t,i,r,n,a){var s=0,o=void 0;e.length&&(s=e.end(e.length-1));var u=Math.max(0,s-n);if(!t.segments.length)return null;if(u>=this.goalBufferLength_())return null;if(!r&&1<=u)return null;if(null===a)return i=this.getSyncSegmentCandidate_(t),this.generateSegmentInfo_(t,i,null,!0);if(null!==i){var c=t.segments[i];return o=c&&c.end?c.end:s,this.generateSegmentInfo_(t,i+1,o,!1)}if(this.fetchAtBuffer_){var l=Is.getMediaInfoForTime(t,s,a.segmentIndex,a.time);i=l.mediaIndex,o=l.startTime}else{var d=Is.getMediaInfoForTime(t,n,a.segmentIndex,a.time);i=d.mediaIndex,o=d.startTime}return this.generateSegmentInfo_(t,i,o,!1)},r.prototype.getSyncSegmentCandidate_=function(e){var t=this;if(-1===this.currentTimeline_)return 0;var i=e.segments.map(function(e,t){return{timeline:e.timeline,segmentIndex:t}}).filter(function(e){return e.timeline===t.currentTimeline_});return i.length?i[Math.min(i.length-1,1)].segmentIndex:Math.max(e.segments.length-1,0)},r.prototype.generateSegmentInfo_=function(e,t,i,r){if(t<0||t>=e.segments.length)return null;var n=e.segments[t];return{requestId:"segment-loader-"+Math.random(),uri:n.resolvedUri,mediaIndex:t,isSyncRequest:r,startOfSegment:i,playlist:e,bytes:null,encryptedBytes:null,timestampOffset:null,timeline:n.timeline,duration:n.duration,segment:n}},r.prototype.abortRequestEarly_=function(e){if(this.hls_.tech_.paused()||!this.xhrOptions_.timeout||!this.playlist_.attributes.BANDWIDTH)return!1;if(Date.now()-(e.firstBytesReceivedAt||Date.now())<1e3)return!1;var t=this.currentTime_(),i=e.bandwidth,r=this.pendingSegment_.duration,n=Is.estimateSegmentRequestTime(r,i,this.playlist_,e.bytesReceived),a=function(e,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:1;return((e.length?e.end(e.length-1):0)-t)/i}(this.buffered_(),t,this.hls_.tech_.playbackRate())-1;if(n<=a)return!1;var s=function(e){var t=e.master,i=e.currentTime,r=e.bandwidth,n=e.duration,a=e.segmentDuration,s=e.timeUntilRebuffer,o=e.currentTimeline,u=e.syncController,c=t.playlists.filter(function(e){return!Is.isIncompatible(e)}),l=c.filter(Is.isEnabled);l.length||(l=c.filter(function(e){return!Is.isDisabled(e)}));var d=l.filter(Is.hasAttribute.bind(null,"BANDWIDTH")).map(function(e){var t=u.getSyncPoint(e,n,o,i)?1:2;return{playlist:e,rebufferingImpact:Is.estimateSegmentRequestTime(a,r,e)*t-s}}),h=d.filter(function(e){return e.rebufferingImpact<=0});return Fo(h,function(e,t){return Ho(t.playlist,e.playlist)}),h.length?h[0]:(Fo(d,function(e,t){return e.rebufferingImpact-t.rebufferingImpact}),d[0]||null)}({master:this.hls_.playlists.master,currentTime:t,bandwidth:i,duration:this.duration_(),segmentDuration:r,timeUntilRebuffer:a,currentTimeline:this.currentTimeline_,syncController:this.syncController_});if(s){var o=n-a-s.rebufferingImpact,u=.5;return a<=Hs&&(u=1),!s.playlist||s.playlist.uri===this.playlist_.uri||o<u?!1:(this.bandwidth=s.playlist.attributes.BANDWIDTH*ko.BANDWIDTH_VARIANCE+1,this.abort(),this.trigger("earlyabort"),!0)}},r.prototype.handleProgress_=function(e,t){this.pendingSegment_&&t.requestId===this.pendingSegment_.requestId&&!this.abortRequestEarly_(t.stats)&&this.trigger("progress")},r.prototype.loadSegment_=function(e){this.state="WAITING",this.pendingSegment_=e,this.trimBackBuffer_(e),e.abortRequests=Do(this.hls_.xhr,this.xhrOptions_,this.decrypter_,this.createSimplifiedSegmentObj_(e),this.handleProgress_.bind(this),this.segmentRequestFinished_.bind(this))},r.prototype.trimBackBuffer_=function(e){var t,i,r,n,a=(t=this.seekable_(),i=this.currentTime_(),r=this.playlist_.targetDuration||10,n=void 0,n=t.length&&0<t.start(0)&&t.start(0)<i?t.start(0):i-30,Math.min(n,i-r));0<a&&this.remove(0,a)},r.prototype.createSimplifiedSegmentObj_=function(e){var t=e.segment,i={resolvedUri:t.resolvedUri,byterange:t.byterange,requestId:e.requestId};if(t.key){var r=t.key.iv||new Uint32Array([0,0,0,e.mediaIndex+e.playlist.mediaSequence]);i.key={resolvedUri:t.key.resolvedUri,iv:r}}return t.map&&(i.map=this.initSegment(t.map)),i},r.prototype.segmentRequestFinished_=function(e,t){if(this.mediaRequests+=1,t.stats&&(this.mediaBytesTransferred+=t.stats.bytesReceived,this.mediaTransferDuration+=t.stats.roundTripTime),this.pendingSegment_){if(t.requestId===this.pendingSegment_.requestId){if(e)return this.pendingSegment_=null,this.state="READY",e.code===Lo?void(this.mediaRequestsAborted+=1):(this.pause(),e.code===Ao?(this.mediaRequestsTimedout+=1,this.bandwidth=1,this.roundTrip=NaN,void this.trigger("bandwidthupdate")):(this.mediaRequestsErrored+=1,this.error(e),void this.trigger("error")));this.bandwidth=t.stats.bandwidth,this.roundTrip=t.stats.roundTripTime,t.map&&(t.map=this.initSegment(t.map,!0)),this.processSegmentResponse_(t)}}else this.mediaRequestsAborted+=1},r.prototype.processSegmentResponse_=function(e){var t=this.pendingSegment_;t.bytes=e.bytes,e.map&&(t.segment.map.bytes=e.map.bytes),t.endOfAllRequests=e.endOfAllRequests,this.handleSegment_()},r.prototype.handleSegment_=function(){var e=this;if(this.pendingSegment_){var t=this.pendingSegment_,i=t.segment,r=this.syncController_.probeSegmentInfo(t);"undefined"==typeof this.startingMedia_&&r&&(r.containsAudio||r.containsVideo)&&(this.startingMedia_={containsAudio:r.containsAudio,containsVideo:r.containsVideo});var n,a,s,o=(n=this.loaderType_,a=this.startingMedia_,s=r,"main"===n&&a&&s?s.containsAudio||s.containsVideo?a.containsVideo&&!s.containsVideo?"Only audio found in segment when we expected video. We can't switch to audio only from a stream that had video. To get rid of this message, please add codec information to the manifest.":!a.containsVideo&&s.containsVideo?"Video found in segment when we expected only audio. We can't switch to a stream with video from an audio only stream. To get rid of this message, please add codec information to the manifest.":null:"Neither audio nor video found in segment.":null);if(o)return this.error({message:o,blacklistDuration:1/0}),void this.trigger("error");if(t.isSyncRequest)return this.trigger("syncinfoupdate"),this.pendingSegment_=null,void(this.state="READY");null!==t.timestampOffset&&t.timestampOffset!==this.sourceUpdater_.timestampOffset()&&(this.sourceUpdater_.timestampOffset(t.timestampOffset),this.trigger("timestampoffset"));var u,c,l,d,h,p,f,m,g,y,v,_=this.syncController_.mappingForTimeline(t.timeline);if(null!==_&&this.trigger({type:"segmenttimemapping",mapping:_}),this.state="APPENDING",i.map){var b=Bs(i.map);if(!this.activeInitSegmentId_||this.activeInitSegmentId_!==b){var T=this.initSegment(i.map);this.sourceUpdater_.appendBuffer(T.bytes,function(){e.activeInitSegmentId_=b})}}t.byteLength=t.bytes.byteLength,"number"==typeof i.start&&"number"==typeof i.end?this.mediaSecondsLoaded+=i.end-i.start:this.mediaSecondsLoaded+=i.duration,this.logger_((c=(u=t).segment,l=c.start,d=c.end,h=u.playlist,p=h.mediaSequence,f=h.id,m=h.segments,g=void 0===m?[]:m,y=u.mediaIndex,v=u.timeline,["appending ["+y+"] of ["+p+", "+(p+g.length)+"] from playlist ["+f+"]","["+l+" => "+d+"] in timeline ["+v+"]"].join(" "))),this.sourceUpdater_.appendBuffer(t.bytes,this.handleUpdateEnd_.bind(this))}else this.state="READY"},r.prototype.handleUpdateEnd_=function(){if(!this.pendingSegment_)return this.state="READY",void(this.paused()||this.monitorBuffer_());var e=this.pendingSegment_,t=e.segment,i=null!==this.mediaIndex;(this.pendingSegment_=null,this.recordThroughput_(e),this.addSegmentMetadataCue_(e),this.state="READY",this.mediaIndex=e.mediaIndex,this.fetchAtBuffer_=!0,this.currentTimeline_=e.timeline,this.trigger("syncinfoupdate"),t.end&&this.currentTime_()-t.end>3*e.playlist.targetDuration)?this.resetEverything():(i&&this.trigger("bandwidthupdate"),this.trigger("progress"),qo(e.playlist,this.mediaSource_,e.mediaIndex+1)&&this.endOfStream(),this.paused()||this.monitorBuffer_())},r.prototype.recordThroughput_=function(e){var t=this.throughput.rate,i=Date.now()-e.endOfAllRequests+1,r=Math.floor(e.byteLength/i*8*1e3);this.throughput.rate+=(r-t)/++this.throughput.count},r.prototype.addSegmentMetadataCue_=function(e){if(this.segmentMetadataTrack_){var t=e.segment,i=t.start,r=t.end;if(Vo(i)&&Vo(r)){Xs(i,r,this.segmentMetadataTrack_);var n=g.WebKitDataCue||g.VTTCue,a={bandwidth:e.playlist.attributes.BANDWIDTH,resolution:e.playlist.attributes.RESOLUTION,codecs:e.playlist.attributes.CODECS,byteLength:e.byteLength,uri:e.uri,timeline:e.timeline,playlist:e.playlist.uri,start:i,end:r},s=new n(i,r,JSON.stringify(a));s.value=a,this.segmentMetadataTrack_.addCue(s)}}},r}(en.EventTarget),Go=new Uint8Array("\n\n".split("").map(function(e){return e.charCodeAt(0)})),zo=function(e){return String.fromCharCode.apply(null,e)},Xo=function(r){function n(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};y(this,n);var i=b(this,r.call(this,e,t));return i.mediaSource_=null,i.subtitlesTrack_=null,i}return _(n,r),n.prototype.buffered_=function(){if(!this.subtitlesTrack_||!this.subtitlesTrack_.cues.length)return en.createTimeRanges();var e=this.subtitlesTrack_.cues,t=e[0].startTime,i=e[e.length-1].startTime;return en.createTimeRanges([[t,i]])},n.prototype.initSegment=function(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1];if(!e)return null;var i=Bs(e),r=this.initSegments_[i];if(t&&!r&&e.bytes){var n=Go.byteLength+e.bytes.byteLength,a=new Uint8Array(n);a.set(e.bytes),a.set(Go,e.bytes.byteLength),this.initSegments_[i]=r={resolvedUri:e.resolvedUri,byterange:e.byterange,bytes:a}}return r||e},n.prototype.couldBeginLoading_=function(){return this.playlist_&&this.subtitlesTrack_&&!this.paused()},n.prototype.init_=function(){return this.state="READY",this.resetEverything(),this.monitorBuffer_()},n.prototype.track=function(e){return"undefined"==typeof e||(this.subtitlesTrack_=e,"INIT"===this.state&&this.couldBeginLoading_()&&this.init_()),this.subtitlesTrack_},n.prototype.remove=function(e,t){Xs(e,t,this.subtitlesTrack_)},n.prototype.fillBuffer_=function(){var e=this;this.syncPoint_||(this.syncPoint_=this.syncController_.getSyncPoint(this.playlist_,this.duration_(),this.currentTimeline_,this.currentTime_()));var t=this.checkBuffer_(this.buffered_(),this.playlist_,this.mediaIndex,this.hasPlayed_(),this.currentTime_(),this.syncPoint_);if(t=this.skipEmptySegments_(t)){if(null===this.syncController_.timestampOffsetForTimeline(t.timeline)){return this.syncController_.one("timestampoffset",function(){e.state="READY",e.paused()||e.monitorBuffer_()}),void(this.state="WAITING_ON_TIMELINE")}this.loadSegment_(t)}},n.prototype.skipEmptySegments_=function(e){for(;e&&e.segment.empty;)e=this.generateSegmentInfo_(e.playlist,e.mediaIndex+1,e.startOfSegment+e.duration,e.isSyncRequest);return e},n.prototype.handleSegment_=function(){var t=this;if(this.pendingSegment_&&this.subtitlesTrack_){this.state="APPENDING";var e=this.pendingSegment_,i=e.segment;if("function"!=typeof g.WebVTT&&this.subtitlesTrack_&&this.subtitlesTrack_.tech_){var r=function(){t.handleSegment_()};return this.state="WAITING_ON_VTTJS",this.subtitlesTrack_.tech_.one("vttjsloaded",r),void this.subtitlesTrack_.tech_.one("vttjserror",function(){t.subtitlesTrack_.tech_.off("vttjsloaded",r),t.error({message:"Error loading vtt.js"}),t.state="READY",t.pause(),t.trigger("error")})}i.requested=!0;try{this.parseVTTCues_(e)}catch(e){return this.error({message:e.message}),this.state="READY",this.pause(),this.trigger("error")}if(this.updateTimeMapping_(e,this.syncController_.timelines[e.timeline],this.playlist_),e.isSyncRequest)return this.trigger("syncinfoupdate"),this.pendingSegment_=null,void(this.state="READY");e.byteLength=e.bytes.byteLength,this.mediaSecondsLoaded+=i.duration,e.cues.length&&this.remove(e.cues[0].endTime,e.cues[e.cues.length-1].endTime),e.cues.forEach(function(e){t.subtitlesTrack_.addCue(e)}),this.handleUpdateEnd_()}else this.state="READY"},n.prototype.parseVTTCues_=function(t){var e=void 0,i=!1;"function"==typeof g.TextDecoder?e=new g.TextDecoder("utf8"):(e=g.WebVTT.StringDecoder(),i=!0);var r=new g.WebVTT.Parser(g,g.vttjs,e);if(t.cues=[],t.timestampmap={MPEGTS:0,LOCAL:0},r.oncue=t.cues.push.bind(t.cues),r.ontimestampmap=function(e){return t.timestampmap=e},r.onparsingerror=function(e){en.log.warn("Error encountered when parsing cues: "+e.message)},t.segment.map){var n=t.segment.map.bytes;i&&(n=zo(n)),r.parse(n)}var a=t.bytes;i&&(a=zo(a)),r.parse(a),r.flush()},n.prototype.updateTimeMapping_=function(e,t,i){var r=e.segment;if(t)if(e.cues.length){var n=e.timestampmap,a=n.MPEGTS/9e4-n.LOCAL+t.mapping;if(e.cues.forEach(function(e){e.startTime+=a,e.endTime+=a}),!i.syncInfo){var s=e.cues[0].startTime,o=e.cues[e.cues.length-1].startTime;i.syncInfo={mediaSequence:i.mediaSequence+e.mediaIndex,time:Math.min(s,o-r.duration)}}}else r.empty=!0},n}(Wo),Yo=function(e,t){for(var i=e.cues,r=0;r<i.length;r++){var n=i[r];if(t>=n.adStartTime&&t<=n.adEndTime)return n}return null},$o=$a,Ko=[{name:"VOD",run:function(e,t,i,r,n){if(i!==1/0){return{time:0,segmentIndex:0}}return null}},{name:"ProgramDateTime",run:function(e,t,i,r,n){if(!e.datetimeToDisplayTime)return null;var a=t.segments||[],s=null,o=null;n=n||0;for(var u=0;u<a.length;u++){var c=a[u];if(c.dateTimeObject){var l=c.dateTimeObject.getTime()/1e3+e.datetimeToDisplayTime,d=Math.abs(n-l);if(null!==o&&o<d)break;o=d,s={time:l,segmentIndex:u}}}return s}},{name:"Segment",run:function(e,t,i,r,n){var a=t.segments||[],s=null,o=null;n=n||0;for(var u=0;u<a.length;u++){var c=a[u];if(c.timeline===r&&"undefined"!=typeof c.start){var l=Math.abs(n-c.start);if(null!==o&&o<l)break;(!s||null===o||l<=o)&&(o=l,s={time:c.start,segmentIndex:u})}}return s}},{name:"Discontinuity",run:function(e,t,i,r,n){var a=null;if(n=n||0,t.discontinuityStarts&&t.discontinuityStarts.length)for(var s=null,o=0;o<t.discontinuityStarts.length;o++){var u=t.discontinuityStarts[o],c=t.discontinuitySequence+o+1,l=e.discontinuities[c];if(l){var d=Math.abs(n-l.time);if(null!==s&&s<d)break;(!a||null===s||d<=s)&&(s=d,a={time:l.time,segmentIndex:u})}}return a}},{name:"Playlist",run:function(e,t,i,r,n){return t.syncInfo?{time:t.syncInfo.time,segmentIndex:t.syncInfo.mediaSequence-t.mediaSequence}:null}}],Qo=function(t){function i(){y(this,i);var e=b(this,t.call(this));return e.inspectCache_=void 0,e.timelines=[],e.discontinuities=[],e.datetimeToDisplayTime=null,e.logger_=So("SyncController"),e}return _(i,t),i.prototype.getSyncPoint=function(e,t,i,r){var n=this.runStrategies_(e,t,i,r);return n.length?this.selectSyncPoint_(n,{key:"time",value:r}):null},i.prototype.getExpiredTime=function(e,t){if(!e||!e.segments)return null;var i=this.runStrategies_(e,t,e.discontinuitySequence,0);if(!i.length)return null;var r=this.selectSyncPoint_(i,{key:"segmentIndex",value:0});return 0<r.segmentIndex&&(r.time*=-1),Math.abs(r.time+_s(e,r.segmentIndex,0))},i.prototype.runStrategies_=function(e,t,i,r){for(var n=[],a=0;a<Ko.length;a++){var s=Ko[a],o=s.run(this,e,t,i,r);o&&(o.strategy=s.name,n.push({strategy:s.name,syncPoint:o}))}return n},i.prototype.selectSyncPoint_=function(e,t){for(var i=e[0].syncPoint,r=Math.abs(e[0].syncPoint[t.key]-t.value),n=e[0].strategy,a=1;a<e.length;a++){var s=Math.abs(e[a].syncPoint[t.key]-t.value);s<r&&(r=s,i=e[a].syncPoint,n=e[a].strategy)}return this.logger_("syncPoint for ["+t.key+": "+t.value+"] chosen with strategy ["+n+"]: [time:"+i.time+", segmentIndex:"+i.segmentIndex+"]"),i},i.prototype.saveExpiredSegmentInfo=function(e,t){for(var i=t.mediaSequence-e.mediaSequence-1;0<=i;i--){var r=e.segments[i];if(r&&"undefined"!=typeof r.start){t.syncInfo={mediaSequence:e.mediaSequence+i,time:r.start},this.logger_("playlist refresh sync: [time:"+t.syncInfo.time+", mediaSequence: "+t.syncInfo.mediaSequence+"]"),this.trigger("syncinfoupdate");break}}},i.prototype.setDateTimeMapping=function(e){if(!this.datetimeToDisplayTime&&e.segments&&e.segments.length&&e.segments[0].dateTimeObject){var t=e.segments[0].dateTimeObject.getTime()/1e3;this.datetimeToDisplayTime=-t}},i.prototype.reset=function(){this.inspectCache_=void 0},i.prototype.probeSegmentInfo=function(e){var t=e.segment,i=e.playlist,r=void 0;return(r=t.map?this.probeMp4Segment_(e):this.probeTsSegment_(e))&&this.calculateSegmentTimeMapping_(e,r)&&(this.saveDiscontinuitySyncInfo_(e),i.syncInfo||(i.syncInfo={mediaSequence:i.mediaSequence+e.mediaIndex,time:t.start})),r},i.prototype.probeMp4Segment_=function(e){var t=e.segment,i=ia(t.map.bytes),r=ra(i,e.bytes);return null!==e.timestampOffset&&(e.timestampOffset-=r),{start:r,end:r+t.duration}},i.prototype.probeTsSegment_=function(e){var t=$o(e.bytes,this.inspectCache_),i=void 0,r=void 0;return t?(t.video&&2===t.video.length?(this.inspectCache_=t.video[1].dts,i=t.video[0].dtsTime,r=t.video[1].dtsTime):t.audio&&2===t.audio.length&&(this.inspectCache_=t.audio[1].dts,i=t.audio[0].dtsTime,r=t.audio[1].dtsTime),{start:i,end:r,containsVideo:t.video&&2===t.video.length,containsAudio:t.audio&&2===t.audio.length}):null},i.prototype.timestampOffsetForTimeline=function(e){return"undefined"==typeof this.timelines[e]?null:this.timelines[e].time},i.prototype.mappingForTimeline=function(e){return"undefined"==typeof this.timelines[e]?null:this.timelines[e].mapping},i.prototype.calculateSegmentTimeMapping_=function(e,t){var i=e.segment,r=this.timelines[e.timeline];if(null!==e.timestampOffset)r={time:e.startOfSegment,mapping:e.startOfSegment-t.start},this.timelines[e.timeline]=r,this.trigger("timestampoffset"),this.logger_("time mapping for timeline "+e.timeline+": [time: "+r.time+"] [mapping: "+r.mapping+"]"),i.start=e.startOfSegment,i.end=t.end+r.mapping;else{if(!r)return!1;i.start=t.start+r.mapping,i.end=t.end+r.mapping}return!0},i.prototype.saveDiscontinuitySyncInfo_=function(e){var t=e.playlist,i=e.segment;if(i.discontinuity)this.discontinuities[i.timeline]={time:i.start,accuracy:0};else if(t.discontinuityStarts&&t.discontinuityStarts.length)for(var r=0;r<t.discontinuityStarts.length;r++){var n=t.discontinuityStarts[r],a=t.discontinuitySequence+r+1,s=n-e.mediaIndex,o=Math.abs(s);if(!this.discontinuities[a]||this.discontinuities[a].accuracy>o){var u=void 0;u=s<0?i.start-_s(t,e.mediaIndex,n):i.end+_s(t,e.mediaIndex+1,n),this.discontinuities[a]={time:u,accuracy:o}}}},i}(en.EventTarget),Jo=new io("./decrypter-worker.worker.js",function(e,t){var i,a,d,r,n,h,p,g,s,c,y,o,u=this;i="undefined"!=typeof e?e:"undefined"!=typeof global?global:"undefined"!=typeof u?u:{},a="undefined"!=typeof e?e:"undefined"!=typeof i?i:"undefined"!=typeof u?u:{},d=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},r=function(){function r(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(e,t,i){return t&&r(e.prototype,t),i&&r(e,i),e}}(),n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==("undefined"==typeof t?"undefined":v(t))&&"function"!=typeof t?e:t},h=function(){var e=[[[],[],[],[],[]],[[],[],[],[],[]]],t=e[0],i=e[1],r=t[4],n=i[4],a=void 0,s=void 0,o=void 0,u=[],c=[],l=void 0,d=void 0,h=void 0,p=void 0,f=void 0;for(a=0;a<256;a++)c[(u[a]=a<<1^283*(a>>7))^a]=a;for(s=o=0;!r[s];s^=l||1,o=c[o]||1)for(h=(h=o^o<<1^o<<2^o<<3^o<<4)>>8^255&h^99,f=16843009*u[d=u[l=u[n[r[s]=h]=s]]]^65537*d^257*l^16843008*s,p=257*u[h]^16843008*h,a=0;a<4;a++)t[a][s]=p=p<<24^p>>>8,i[a][h]=f=f<<24^f>>>8;for(a=0;a<5;a++)t[a]=t[a].slice(0),i[a]=i[a].slice(0);return e},p=null,g=function(){function l(e){d(this,l),p||(p=h()),this._tables=[[p[0][0].slice(),p[0][1].slice(),p[0][2].slice(),p[0][3].slice(),p[0][4].slice()],[p[1][0].slice(),p[1][1].slice(),p[1][2].slice(),p[1][3].slice(),p[1][4].slice()]];var t=void 0,i=void 0,r=void 0,n=void 0,a=void 0,s=this._tables[0][4],o=this._tables[1],u=e.length,c=1;if(4!==u&&6!==u&&8!==u)throw new Error("Invalid aes key size");for(n=e.slice(0),a=[],this._key=[n,a],t=u;t<4*u+28;t++)r=n[t-1],(t%u==0||8===u&&t%u==4)&&(r=s[r>>>24]<<24^s[r>>16&255]<<16^s[r>>8&255]<<8^s[255&r],t%u==0&&(r=r<<8^r>>>24^c<<24,c=c<<1^283*(c>>7))),n[t]=n[t-u]^r;for(i=0;t;i++,t--)r=n[3&i?t:t-4],a[i]=t<=4||i<4?r:o[0][s[r>>>24]]^o[1][s[r>>16&255]]^o[2][s[r>>8&255]]^o[3][s[255&r]]}return l.prototype.decrypt=function(e,t,i,r,n,a){var s=this._key[1],o=e^s[0],u=r^s[1],c=i^s[2],l=t^s[3],d=void 0,h=void 0,p=void 0,f=s.length/4-2,m=void 0,g=4,y=this._tables[1],v=y[0],_=y[1],b=y[2],T=y[3],S=y[4];for(m=0;m<f;m++)d=v[o>>>24]^_[u>>16&255]^b[c>>8&255]^T[255&l]^s[g],h=v[u>>>24]^_[c>>16&255]^b[l>>8&255]^T[255&o]^s[g+1],p=v[c>>>24]^_[l>>16&255]^b[o>>8&255]^T[255&u]^s[g+2],l=v[l>>>24]^_[o>>16&255]^b[u>>8&255]^T[255&c]^s[g+3],g+=4,o=d,u=h,c=p;for(m=0;m<4;m++)n[(3&-m)+a]=S[o>>>24]<<24^S[u>>16&255]<<16^S[c>>8&255]<<8^S[255&l]^s[g++],d=o,o=u,u=c,c=l,l=d},l}(),s=function(){function e(){d(this,e),this.listeners={}}return e.prototype.on=function(e,t){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push(t)},e.prototype.off=function(e,t){if(!this.listeners[e])return!1;var i=this.listeners[e].indexOf(t);return this.listeners[e].splice(i,1),-1<i},e.prototype.trigger=function(e){var t=this.listeners[e];if(t)if(2===arguments.length)for(var i=t.length,r=0;r<i;++r)t[r].call(this,arguments[1]);else for(var n=Array.prototype.slice.call(arguments,1),a=t.length,s=0;s<a;++s)t[s].apply(this,n)},e.prototype.dispose=function(){this.listeners={}},e.prototype.pipe=function(t){this.on("data",function(e){t.push(e)})},e}(),c=function(t){function i(){d(this,i);var e=n(this,t.call(this,s));return e.jobs=[],e.delay=1,e.timeout_=null,e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+("undefined"==typeof t?"undefined":v(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(i,t),i.prototype.processJob_=function(){this.jobs.shift()(),this.jobs.length?this.timeout_=setTimeout(this.processJob_.bind(this),this.delay):this.timeout_=null},i.prototype.push=function(e){this.jobs.push(e),this.timeout_||(this.timeout_=setTimeout(this.processJob_.bind(this),this.delay))},i}(s),y=function(e){return e<<24|(65280&e)<<8|(16711680&e)>>8|e>>>24},o=function(){function u(e,t,i,r){d(this,u);var n=u.STEP,a=new Int32Array(e.buffer),s=new Uint8Array(e.byteLength),o=0;for(this.asyncStream_=new c,this.asyncStream_.push(this.decryptChunk_(a.subarray(o,o+n),t,i,s)),o=n;o<a.length;o+=n)i=new Uint32Array([y(a[o-4]),y(a[o-3]),y(a[o-2]),y(a[o-1])]),this.asyncStream_.push(this.decryptChunk_(a.subarray(o,o+n),t,i,s));this.asyncStream_.push(function(){var e;r(null,(e=s).subarray(0,e.byteLength-e[e.byteLength-1]))})}return u.prototype.decryptChunk_=function(t,i,r,n){return function(){var e=function(e,t,i){var r=new Int32Array(e.buffer,e.byteOffset,e.byteLength>>2),n=new g(Array.prototype.slice.call(t)),a=new Uint8Array(e.byteLength),s=new Int32Array(a.buffer),o=void 0,u=void 0,c=void 0,l=void 0,d=void 0,h=void 0,p=void 0,f=void 0,m=void 0;for(o=i[0],u=i[1],c=i[2],l=i[3],m=0;m<r.length;m+=4)d=y(r[m]),h=y(r[m+1]),p=y(r[m+2]),f=y(r[m+3]),n.decrypt(d,h,p,f,s,m),s[m]=y(s[m]^o),s[m+1]=y(s[m+1]^u),s[m+2]=y(s[m+2]^c),s[m+3]=y(s[m+3]^l),o=d,u=h,c=p,l=f;return a}(t,i,r);n.set(e,t.byteOffset)}},r(u,null,[{key:"STEP",get:function(){return 32e3}}]),u}(),new function(e){e.onmessage=function(e){var n=e.data,t=new Uint8Array(n.encrypted.bytes,n.encrypted.byteOffset,n.encrypted.byteLength),i=new Uint32Array(n.key.bytes,n.key.byteOffset,n.key.byteLength/4),r=new Uint32Array(n.iv.bytes,n.iv.byteOffset,n.iv.byteLength/4);new o(t,i,r,function(e,t){var i,r;a.postMessage((i={source:n.source,decrypted:t},r={},Object.keys(i).forEach(function(e){var t=i[e];ArrayBuffer.isView(t)?r[e]={bytes:t.buffer,byteOffset:t.byteOffset,byteLength:t.byteLength}:r[e]=t}),r),[t.buffer])})}}(u)}),Zo=function(e,t){e.abort(),e.pause(),t&&t.activePlaylistLoader&&(t.activePlaylistLoader.pause(),t.activePlaylistLoader=null)},eu=function(e,t){(t.activePlaylistLoader=e).load()},tu={AUDIO:function(u,c){return function(){var e=c.segmentLoaders[u],t=c.mediaTypes[u],i=c.blacklistCurrentPlaylist;Zo(e,t);var r=t.activeTrack(),n=t.activeGroup(),a=(n.filter(function(e){return e.default})[0]||n[0]).id,s=t.tracks[a];if(r!==s){for(var o in en.log.warn("Problem encountered loading the alternate audio track.Switching back to default."),t.tracks)t.tracks[o].enabled=t.tracks[o]===s;t.onTrackChanged()}else i({message:"Problem encountered loading the default audio track."})}},SUBTITLES:function(r,n){return function(){var e=n.segmentLoaders[r],t=n.mediaTypes[r];en.log.warn("Problem encountered loading the subtitle track.Disabling subtitle track."),Zo(e,t);var i=t.activeTrack();i&&(i.mode="disabled"),t.onTrackChanged()}}},iu={AUDIO:function(e,t,i){if(t){var r=i.tech,n=i.requestOptions,a=i.segmentLoaders[e];t.on("loadedmetadata",function(){var e=t.media();a.playlist(e,n),(!r.paused()||e.endList&&"none"!==r.preload())&&a.load()}),t.on("loadedplaylist",function(){a.playlist(t.media(),n),r.paused()||a.load()}),t.on("error",tu[e](e,i))}},SUBTITLES:function(e,t,i){var r=i.tech,n=i.requestOptions,a=i.segmentLoaders[e],s=i.mediaTypes[e];t.on("loadedmetadata",function(){var e=t.media();a.playlist(e,n),a.track(s.activeTrack()),(!r.paused()||e.endList&&"none"!==r.preload())&&a.load()}),t.on("loadedplaylist",function(){a.playlist(t.media(),n),r.paused()||a.load()}),t.on("error",tu[e](e,i))}},ru=function(t,i){return function(e){return e.attributes[t]===i}},nu=function(t){return function(e){return e.resolvedUri===t}},au={AUDIO:function(e,t){var i,r,n=t.hls,a=t.sourceType,s=t.segmentLoaders[e],o=t.requestOptions.withCredentials,u=t.master,c=u.mediaGroups,l=u.playlists,d=t.mediaTypes[e],h=d.groups,p=d.tracks,f=t.masterPlaylistLoader;for(var m in c[e]&&0!==Object.keys(c[e]).length||(c[e]={main:{default:{default:!0}}}),c[e]){h[m]||(h[m]=[]);var g=l.filter(ru(e,m));for(var y in c[e][m]){var v=c[e][m][y];g.filter(nu(v.resolvedUri)).length&&delete v.resolvedUri;var _=void 0;if(_=v.resolvedUri?new ms(v.resolvedUri,n,o):v.playlists&&"dash"===a?new To(v.playlists[0],n,o,f):null,v=en.mergeOptions({id:y,playlistLoader:_},v),iu[e](e,v.playlistLoader,t),h[m].push(v),"undefined"==typeof p[y]){var b=new en.AudioTrack({id:y,kind:(i=v,r=void 0,r=i.default?"main":"alternative",i.characteristics&&0<=i.characteristics.indexOf("public.accessibility.describes-video")&&(r="main-desc"),r),enabled:!1,language:v.language,default:v.default,label:y});p[y]=b}}}s.on("error",tu[e](e,t))},SUBTITLES:function(e,t){var i=t.tech,r=t.hls,n=t.sourceType,a=t.segmentLoaders[e],s=t.requestOptions.withCredentials,o=t.master.mediaGroups,u=t.mediaTypes[e],c=u.groups,l=u.tracks,d=t.masterPlaylistLoader;for(var h in o[e])for(var p in c[h]||(c[h]=[]),o[e][h])if(!o[e][h][p].forced){var f=o[e][h][p],m=void 0;if("hls"===n?m=new ms(f.resolvedUri,r,s):"dash"===n&&(m=new To(f.playlists[0],r,s,d)),f=en.mergeOptions({id:p,playlistLoader:m},f),iu[e](e,f.playlistLoader,t),c[h].push(f),"undefined"==typeof l[p]){var g=i.addRemoteTextTrack({id:p,kind:"subtitles",enabled:!1,language:f.language,label:p},!1).track;l[p]=g}}a.on("error",tu[e](e,t))},"CLOSED-CAPTIONS":function(e,t){var i=t.tech,r=t.master.mediaGroups,n=t.mediaTypes[e],a=n.groups,s=n.tracks;for(var o in r[e])for(var u in a[o]||(a[o]=[]),r[e][o]){var c=r[e][o][u];if(c.instreamId.match(/CC\d/)&&(a[o].push(en.mergeOptions({id:u},c)),"undefined"==typeof s[u])){var l=i.addRemoteTextTrack({id:c.instreamId,kind:"captions",enabled:!1,language:c.language,label:u},!1).track;s[u]=l}}}},su={AUDIO:function(i,r){return function(){var e=r.mediaTypes[i].tracks;for(var t in e)if(e[t].enabled)return e[t];return null}},SUBTITLES:function(i,r){return function(){var e=r.mediaTypes[i].tracks;for(var t in e)if("showing"===e[t].mode)return e[t];return null}}},ou=function(t){["AUDIO","SUBTITLES","CLOSED-CAPTIONS"].forEach(function(e){au[e](e,t)});var i=t.mediaTypes,e=t.masterPlaylistLoader,r=t.tech,n=t.hls;["AUDIO","SUBTITLES"].forEach(function(e){var a,s,o,u,c,l;i[e].activeGroup=(a=e,s=t,function(t){var e=s.masterPlaylistLoader,i=s.mediaTypes[a].groups,r=e.media();if(!r)return null;var n=null;return r.attributes[a]&&(n=i[r.attributes[a]]),n=n||i.main,"undefined"==typeof t?n:null===t?null:n.filter(function(e){return e.id===t.id})[0]||null}),i[e].activeTrack=su[e](e,t),i[e].onGroupChanged=(o=e,u=t,function(){var e=u.segmentLoaders,t=e[o],i=e.main,r=u.mediaTypes[o],n=r.activeTrack(),a=r.activeGroup(n),s=r.activePlaylistLoader;Zo(t,r),a&&(a.playlistLoader?(t.resyncLoader(),eu(a.playlistLoader,r)):s&&i.resetEverything())}),i[e].onTrackChanged=(c=e,l=t,function(){var e=l.segmentLoaders,t=e[c],i=e.main,r=l.mediaTypes[c],n=r.activeTrack(),a=r.activeGroup(n),s=r.activePlaylistLoader;Zo(t,r),a&&(a.playlistLoader?(s!==a.playlistLoader&&(t.track&&t.track(n),t.resetEverything()),eu(a.playlistLoader,r)):i.resetEverything())})});var a=i.AUDIO.activeGroup(),s=(a.filter(function(e){return e.default})[0]||a[0]).id;i.AUDIO.tracks[s].enabled=!0,i.AUDIO.onTrackChanged(),e.on("mediachange",function(){["AUDIO","SUBTITLES"].forEach(function(e){return i[e].onGroupChanged()})});var o=function(){i.AUDIO.onTrackChanged(),r.trigger({type:"usage",name:"hls-audio-change"})};for(var u in r.audioTracks().addEventListener("change",o),r.remoteTextTracks().addEventListener("change",i.SUBTITLES.onTrackChanged),n.on("dispose",function(){r.audioTracks().removeEventListener("change",o),r.remoteTextTracks().removeEventListener("change",i.SUBTITLES.onTrackChanged)}),r.clearTracks("audio"),i.AUDIO.tracks)r.audioTracks().addTrack(i.AUDIO.tracks[u])},uu=function(){var t={};return["AUDIO","SUBTITLES","CLOSED-CAPTIONS"].forEach(function(e){t[e]={groups:{},tracks:{},activePlaylistLoader:null,activeGroup:Eo,activeTrack:Eo,onGroupChanged:Eo,onTrackChanged:Eo}}),t},cu=void 0,lu=["mediaRequests","mediaRequestsAborted","mediaRequestsTimedout","mediaRequestsErrored","mediaTransferDuration","mediaBytesTransferred"],du=function(e){return this.audioSegmentLoader_[e]+this.mainSegmentLoader_[e]},hu=function(h){function p(e){y(this,p);var t=b(this,h.call(this)),i=e.url,r=e.withCredentials,n=e.tech,a=e.bandwidth,s=e.externHls,o=e.useCueTags,u=e.blacklistDuration,c=e.enableLowInitialPlaylist,l=e.sourceType;if(!i)throw new Error("A non-empty playlist URL is required");cu=s,t.withCredentials=r,t.tech_=n,t.hls_=n.hls,t.sourceType_=l,t.useCueTags_=o,t.blacklistDuration=u,t.enableLowInitialPlaylist=c,t.useCueTags_&&(t.cueTagsTrack_=t.tech_.addTextTrack("metadata","ad-cues"),t.cueTagsTrack_.inBandMetadataTrackDispatchType=""),t.requestOptions_={withCredentials:t.withCredentials,timeout:null},t.mediaTypes_=uu(),t.mediaSource=new en.MediaSource,t.mediaSource.addEventListener("sourceopen",t.handleSourceOpen_.bind(t)),t.seekable_=en.createTimeRanges(),t.hasPlayed_=function(){return!1},t.syncController_=new Qo(e),t.segmentMetadataTrack_=n.addRemoteTextTrack({kind:"metadata",label:"segment-metadata"},!1).track,t.decrypter_=new Jo;var d={hls:t.hls_,mediaSource:t.mediaSource,currentTime:t.tech_.currentTime.bind(t.tech_),seekable:function(){return t.seekable()},seeking:function(){return t.tech_.seeking()},duration:function(){return t.mediaSource.duration},hasPlayed:function(){return t.hasPlayed_()},goalBufferLength:function(){return t.goalBufferLength()},bandwidth:a,syncController:t.syncController_,decrypter:t.decrypter_,sourceType:t.sourceType_};return t.masterPlaylistLoader_="dash"===t.sourceType_?new To(i,t.hls_,t.withCredentials):new ms(i,t.hls_,t.withCredentials),t.setupMasterPlaylistLoaderListeners_(),t.mainSegmentLoader_=new Wo(en.mergeOptions(d,{segmentMetadataTrack:t.segmentMetadataTrack_,loaderType:"main"}),e),t.audioSegmentLoader_=new Wo(en.mergeOptions(d,{loaderType:"audio"}),e),t.subtitleSegmentLoader_=new Xo(en.mergeOptions(d,{loaderType:"vtt"}),e),t.setupSegmentLoaderListeners_(),lu.forEach(function(e){t[e+"_"]=du.bind(t,e)}),t.logger_=So("MPC"),t.masterPlaylistLoader_.load(),t}return _(p,h),p.prototype.setupMasterPlaylistLoaderListeners_=function(){var r=this;this.masterPlaylistLoader_.on("loadedmetadata",function(){var e=r.masterPlaylistLoader_.media(),t=1.5*r.masterPlaylistLoader_.targetDuration*1e3;Ps(r.masterPlaylistLoader_.master,r.masterPlaylistLoader_.media())?r.requestOptions_.timeout=0:r.requestOptions_.timeout=t,e.endList&&"none"!==r.tech_.preload()&&(r.mainSegmentLoader_.playlist(e,r.requestOptions_),r.mainSegmentLoader_.load()),ou({sourceType:r.sourceType_,segmentLoaders:{AUDIO:r.audioSegmentLoader_,SUBTITLES:r.subtitleSegmentLoader_,main:r.mainSegmentLoader_},tech:r.tech_,requestOptions:r.requestOptions_,masterPlaylistLoader:r.masterPlaylistLoader_,hls:r.hls_,master:r.master(),mediaTypes:r.mediaTypes_,blacklistCurrentPlaylist:r.blacklistCurrentPlaylist.bind(r)}),r.triggerPresenceUsage_(r.master(),e);try{r.setupSourceBuffers_()}catch(e){return en.log.warn("Failed to create SourceBuffers",e),r.mediaSource.endOfStream("decode")}r.setupFirstPlay(),r.trigger("selectedinitialmedia")}),this.masterPlaylistLoader_.on("loadedplaylist",function(){var e=r.masterPlaylistLoader_.media();if(!e){r.excludeUnsupportedVariants_();var t=void 0;return r.enableLowInitialPlaylist&&(t=r.selectInitialPlaylist()),t||(t=r.selectPlaylist()),r.initialMedia_=t,void r.masterPlaylistLoader_.media(r.initialMedia_)}if(r.useCueTags_&&r.updateAdCues_(e),r.mainSegmentLoader_.playlist(e,r.requestOptions_),r.updateDuration(),r.tech_.paused()||r.mainSegmentLoader_.load(),!e.endList){var i=function(){var e=r.seekable();0!==e.length&&r.mediaSource.addSeekableRange_(e.start(0),e.end(0))};if(r.duration()!==1/0){r.tech_.one("durationchange",function e(){r.duration()===1/0?i():r.tech_.one("durationchange",e)})}else i()}}),this.masterPlaylistLoader_.on("error",function(){r.blacklistCurrentPlaylist(r.masterPlaylistLoader_.error)}),this.masterPlaylistLoader_.on("mediachanging",function(){r.mainSegmentLoader_.abort(),r.mainSegmentLoader_.pause()}),this.masterPlaylistLoader_.on("mediachange",function(){var e=r.masterPlaylistLoader_.media(),t=1.5*r.masterPlaylistLoader_.targetDuration*1e3;Ps(r.masterPlaylistLoader_.master,r.masterPlaylistLoader_.media())?r.requestOptions_.timeout=0:r.requestOptions_.timeout=t,r.mainSegmentLoader_.playlist(e,r.requestOptions_),r.mainSegmentLoader_.load(),r.tech_.trigger({type:"mediachange",bubbles:!0})}),this.masterPlaylistLoader_.on("playlistunchanged",function(){var e=r.masterPlaylistLoader_.media();r.stuckAtPlaylistEnd_(e)&&(r.blacklistCurrentPlaylist({message:"Playlist no longer updating."}),r.tech_.trigger("playliststuck"))}),this.masterPlaylistLoader_.on("renditiondisabled",function(){r.tech_.trigger({type:"usage",name:"hls-rendition-disabled"})}),this.masterPlaylistLoader_.on("renditionenabled",function(){r.tech_.trigger({type:"usage",name:"hls-rendition-enabled"})})},p.prototype.triggerPresenceUsage_=function(e,t){var i=e.mediaGroups||{},r=!0,n=Object.keys(i.AUDIO);for(var a in i.AUDIO)for(var s in i.AUDIO[a]){i.AUDIO[a][s].uri||(r=!1)}r&&this.tech_.trigger({type:"usage",name:"hls-demuxed"}),Object.keys(i.SUBTITLES).length&&this.tech_.trigger({type:"usage",name:"hls-webvtt"}),cu.Playlist.isAes(t)&&this.tech_.trigger({type:"usage",name:"hls-aes"}),cu.Playlist.isFmp4(t)&&this.tech_.trigger({type:"usage",name:"hls-fmp4"}),n.length&&1<Object.keys(i.AUDIO[n[0]]).length&&this.tech_.trigger({type:"usage",name:"hls-alternate-audio"}),this.useCueTags_&&this.tech_.trigger({type:"usage",name:"hls-playlist-cue-tags"})},p.prototype.setupSegmentLoaderListeners_=function(){var a=this;this.mainSegmentLoader_.on("bandwidthupdate",function(){var e=a.selectPlaylist(),t=a.masterPlaylistLoader_.media(),i=a.tech_.buffered(),r=i.length?i.end(i.length-1)-a.tech_.currentTime():0,n=a.bufferLowWaterLine();(!t.endList||a.duration()<ko.MAX_BUFFER_LOW_WATER_LINE||e.attributes.BANDWIDTH<t.attributes.BANDWIDTH||n<=r)&&a.masterPlaylistLoader_.media(e),a.tech_.trigger("bandwidthupdate")}),this.mainSegmentLoader_.on("progress",function(){a.trigger("progress")}),this.mainSegmentLoader_.on("error",function(){a.blacklistCurrentPlaylist(a.mainSegmentLoader_.error())}),this.mainSegmentLoader_.on("syncinfoupdate",function(){a.onSyncInfoUpdate_()}),this.mainSegmentLoader_.on("timestampoffset",function(){a.tech_.trigger({type:"usage",name:"hls-timestamp-offset"})}),this.audioSegmentLoader_.on("syncinfoupdate",function(){a.onSyncInfoUpdate_()}),this.mainSegmentLoader_.on("ended",function(){a.onEndOfStream()}),this.mainSegmentLoader_.on("earlyabort",function(){a.blacklistCurrentPlaylist({message:"Aborted early because there isn't enough bandwidth to complete the request without rebuffering."},120)}),this.mainSegmentLoader_.on("reseteverything",function(){a.tech_.trigger("hls-reset")}),this.mainSegmentLoader_.on("segmenttimemapping",function(e){a.tech_.trigger({type:"hls-segment-time-mapping",mapping:e.mapping})}),this.audioSegmentLoader_.on("ended",function(){a.onEndOfStream()})},p.prototype.mediaSecondsLoaded_=function(){return Math.max(this.audioSegmentLoader_.mediaSecondsLoaded+this.mainSegmentLoader_.mediaSecondsLoaded)},p.prototype.load=function(){this.mainSegmentLoader_.load(),this.mediaTypes_.AUDIO.activePlaylistLoader&&this.audioSegmentLoader_.load(),this.mediaTypes_.SUBTITLES.activePlaylistLoader&&this.subtitleSegmentLoader_.load()},p.prototype.fastQualityChange_=function(){var e=this.selectPlaylist();e!==this.masterPlaylistLoader_.media()&&(this.masterPlaylistLoader_.media(e),this.mainSegmentLoader_.resetLoader())},p.prototype.play=function(){if(!this.setupFirstPlay()){this.tech_.ended()&&this.tech_.setCurrentTime(0),this.hasPlayed_()&&this.load();var e=this.tech_.seekable();return this.tech_.duration()===1/0&&this.tech_.currentTime()<e.start(0)?this.tech_.setCurrentTime(e.end(e.length-1)):void 0}},p.prototype.setupFirstPlay=function(){var e=this,t=this.masterPlaylistLoader_.media();if(!t||this.tech_.paused()||this.hasPlayed_())return!1;if(!t.endList){var i=this.seekable();if(!i.length)return!1;if(en.browser.IE_VERSION&&0===this.tech_.readyState())return this.tech_.one("loadedmetadata",function(){e.trigger("firstplay"),e.tech_.setCurrentTime(i.end(0)),e.hasPlayed_=function(){return!0}}),!1;this.trigger("firstplay"),this.tech_.setCurrentTime(i.end(0))}return this.hasPlayed_=function(){return!0},this.load(),!0},p.prototype.handleSourceOpen_=function(){try{this.setupSourceBuffers_()}catch(e){return en.log.warn("Failed to create Source Buffers",e),this.mediaSource.endOfStream("decode")}if(this.tech_.autoplay()){var e=this.tech_.play();"undefined"!=typeof e&&"function"==typeof e.then&&e.then(null,function(e){})}this.trigger("sourceopen")},p.prototype.onEndOfStream=function(){var e=this.mainSegmentLoader_.ended_;this.mediaTypes_.AUDIO.activePlaylistLoader&&(e=!this.mainSegmentLoader_.startingMedia_||this.mainSegmentLoader_.startingMedia_.containsVideo?e&&this.audioSegmentLoader_.ended_:this.audioSegmentLoader_.ended_),e&&this.mediaSource.endOfStream()},p.prototype.stuckAtPlaylistEnd_=function(e){if(!this.seekable().length)return!1;var t=this.syncController_.getExpiredTime(e,this.mediaSource.duration);if(null===t)return!1;var i=cu.Playlist.playlistEnd(e,t),r=this.tech_.currentTime(),n=this.tech_.buffered();if(!n.length)return i-r<=.1;var a=n.end(n.length-1);return a-r<=.1&&i-a<=.1},p.prototype.blacklistCurrentPlaylist=function(){var e,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},i=arguments[1],r=void 0;if(r=t.playlist||this.masterPlaylistLoader_.media(),i=i||t.blacklistDuration||this.blacklistDuration,!r){this.error=t;try{return this.mediaSource.endOfStream("network")}catch(e){return this.trigger("error")}}var n=1===this.masterPlaylistLoader_.master.playlists.filter(Ls).length;return n?(en.log.warn("Problem encountered with the current HLS playlist. Trying again since it is the final playlist."),this.tech_.trigger("retryplaylist"),this.masterPlaylistLoader_.load(n)):(r.excludeUntil=Date.now()+1e3*i,this.tech_.trigger("blacklistplaylist"),this.tech_.trigger({type:"usage",name:"hls-rendition-blacklisted"}),e=this.selectPlaylist(),en.log.warn("Problem encountered with the current HLS playlist."+(t.message?" "+t.message:"")+" Switching to another playlist."),this.masterPlaylistLoader_.media(e))},p.prototype.pauseLoading=function(){this.mainSegmentLoader_.pause(),this.mediaTypes_.AUDIO.activePlaylistLoader&&this.audioSegmentLoader_.pause(),this.mediaTypes_.SUBTITLES.activePlaylistLoader&&this.subtitleSegmentLoader_.pause()},p.prototype.setCurrentTime=function(e){var t=Vs(this.tech_.buffered(),e);return this.masterPlaylistLoader_&&this.masterPlaylistLoader_.media()&&this.masterPlaylistLoader_.media().segments?t&&t.length?e:(this.mainSegmentLoader_.resetEverything(),this.mainSegmentLoader_.abort(),this.mediaTypes_.AUDIO.activePlaylistLoader&&(this.audioSegmentLoader_.resetEverything(),this.audioSegmentLoader_.abort()),this.mediaTypes_.SUBTITLES.activePlaylistLoader&&(this.subtitleSegmentLoader_.resetEverything(),this.subtitleSegmentLoader_.abort()),void this.load()):0},p.prototype.duration=function(){return this.masterPlaylistLoader_?this.mediaSource?this.mediaSource.duration:cu.Playlist.duration(this.masterPlaylistLoader_.media()):0},p.prototype.seekable=function(){return this.seekable_},p.prototype.onSyncInfoUpdate_=function(){var e=void 0,t=void 0;if(this.masterPlaylistLoader_){var i=this.masterPlaylistLoader_.media();if(i){var r=this.syncController_.getExpiredTime(i,this.mediaSource.duration);if(null!==r&&0!==(e=cu.Playlist.seekable(i,r)).length){if(this.mediaTypes_.AUDIO.activePlaylistLoader){if(i=this.mediaTypes_.AUDIO.activePlaylistLoader.media(),null===(r=this.syncController_.getExpiredTime(i,this.mediaSource.duration)))return;if(0===(t=cu.Playlist.seekable(i,r)).length)return}t?t.start(0)>e.end(0)||e.start(0)>t.end(0)?this.seekable_=e:this.seekable_=en.createTimeRanges([[t.start(0)>e.start(0)?t.start(0):e.start(0),t.end(0)<e.end(0)?t.end(0):e.end(0)]]):this.seekable_=e,this.logger_("seekable updated ["+Gs(this.seekable_)+"]"),this.tech_.trigger("seekablechanged")}}}},p.prototype.updateDuration=function(){var t=this,e=this.mediaSource.duration,i=cu.Playlist.duration(this.masterPlaylistLoader_.media()),r=this.tech_.buffered(),n=function e(){t.mediaSource.duration=i,t.tech_.trigger("durationchange"),t.mediaSource.removeEventListener("sourceopen",e)};0<r.length&&(i=Math.max(i,r.end(r.length-1))),e!==i&&("open"!==this.mediaSource.readyState?this.mediaSource.addEventListener("sourceopen",n):n())},p.prototype.dispose=function(){var r=this;this.decrypter_.terminate(),this.masterPlaylistLoader_.dispose(),this.mainSegmentLoader_.dispose(),["AUDIO","SUBTITLES"].forEach(function(e){var t=r.mediaTypes_[e].groups;for(var i in t)t[i].forEach(function(e){e.playlistLoader&&e.playlistLoader.dispose()})}),this.audioSegmentLoader_.dispose(),this.subtitleSegmentLoader_.dispose()},p.prototype.master=function(){return this.masterPlaylistLoader_.master},p.prototype.media=function(){return this.masterPlaylistLoader_.media()||this.initialMedia_},p.prototype.setupSourceBuffers_=function(){var e,t=this.masterPlaylistLoader_.media();if(t&&"open"===this.mediaSource.readyState){if((e=Bo(this.masterPlaylistLoader_.master,t)).length<1)return this.error="No compatible SourceBuffer configuration for the variant stream:"+t.resolvedUri,this.mediaSource.endOfStream("decode");this.configureLoaderMimeTypes_(e),this.excludeIncompatibleVariants_(t)}},p.prototype.configureLoaderMimeTypes_=function(e){var t=1<e.length&&-1===e[0].indexOf(",")&&e[0]!==e[1]?new en.EventTarget:null;this.mainSegmentLoader_.mimeType(e[0],t),e[1]&&this.audioSegmentLoader_.mimeType(e[1],t)},p.prototype.excludeUnsupportedVariants_=function(){this.master().playlists.forEach(function(e){e.attributes.CODECS&&g.MediaSource&&g.MediaSource.isTypeSupported&&!g.MediaSource.isTypeSupported('video/mp4; codecs="'+e.attributes.CODECS.replace(/avc1\.(\d+)\.(\d+)/i,function(e){return lo([e])[0]})+'"')&&(e.excludeUntil=1/0)})},p.prototype.excludeIncompatibleVariants_=function(e){var i=2,r=null,t=void 0;e.attributes.CODECS&&(t=No(e.attributes.CODECS),r=t.videoCodec,i=t.codecCount),this.master().playlists.forEach(function(e){var t={codecCount:2,videoCodec:null};e.attributes.CODECS&&(t=No(e.attributes.CODECS)),t.codecCount!==i&&(e.excludeUntil=1/0),t.videoCodec!==r&&(e.excludeUntil=1/0)})},p.prototype.updateAdCues_=function(e){var t=0,i=this.seekable();i.length&&(t=i.start(0)),function(e,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:0;if(e.segments)for(var r=i,n=void 0,a=0;a<e.segments.length;a++){var s=e.segments[a];if(n||(n=Yo(t,r+s.duration/2)),n){if("cueIn"in s){n.endTime=r,n.adEndTime=r,r+=s.duration,n=null;continue}if(r<n.endTime){r+=s.duration;continue}n.endTime+=s.duration}else if("cueOut"in s&&((n=new g.VTTCue(r,r+s.duration,s.cueOut)).adStartTime=r,n.adEndTime=r+parseFloat(s.cueOut),t.addCue(n)),"cueOutCont"in s){var o,u,c=s.cueOutCont.split("/").map(parseFloat);o=c[0],u=c[1],(n=new g.VTTCue(r,r+s.duration,"")).adStartTime=r-o,n.adEndTime=n.adStartTime+u,t.addCue(n)}r+=s.duration}}(e,this.cueTagsTrack_,t)},p.prototype.goalBufferLength=function(){var e=this.tech_.currentTime(),t=ko.GOAL_BUFFER_LENGTH,i=ko.GOAL_BUFFER_LENGTH_RATE,r=Math.max(t,ko.MAX_GOAL_BUFFER_LENGTH);return Math.min(t+e*i,r)},p.prototype.bufferLowWaterLine=function(){var e=this.tech_.currentTime(),t=ko.BUFFER_LOW_WATER_LINE,i=ko.BUFFER_LOW_WATER_LINE_RATE,r=Math.max(t,ko.MAX_BUFFER_LOW_WATER_LINE);return Math.min(t+e*i,r)},p}(en.EventTarget),pu=function e(t,i,r){y(this,e);var n,a,s,o=t.masterPlaylistController_.fastQualityChange_.bind(t.masterPlaylistController_);if(i.attributes.RESOLUTION){var u=i.attributes.RESOLUTION;this.width=u.width,this.height=u.height}this.bandwidth=i.attributes.BANDWIDTH,this.id=r,this.enabled=(n=t.playlists,a=i.uri,s=o,function(e){var t=n.master.playlists[a],i=As(t),r=Ls(t);return"undefined"==typeof e?r:(e?delete t.disabled:t.disabled=!0,e===r||i||(s(),e?n.trigger("renditionenabled"):n.trigger("renditiondisabled")),e)})},fu=["seeking","seeked","pause","playing","error"],mu=function(){function s(e){var t=this;y(this,s),this.tech_=e.tech,this.seekable=e.seekable,this.consecutiveUpdates=0,this.lastRecordedTime=null,this.timer_=null,this.checkCurrentTimeTimeout_=null,this.logger_=So("PlaybackWatcher"),this.logger_("initialize");var i=function(){return t.monitorCurrentTime_()},r=function(){return t.techWaiting_()},n=function(){return t.cancelTimer_()},a=function(){return t.fixesBadSeeks_()};this.tech_.on("seekablechanged",a),this.tech_.on("waiting",r),this.tech_.on(fu,n),this.tech_.on("canplay",i),this.dispose=function(){t.logger_("dispose"),t.tech_.off("seekablechanged",a),t.tech_.off("waiting",r),t.tech_.off(fu,n),t.tech_.off("canplay",i),t.checkCurrentTimeTimeout_&&g.clearTimeout(t.checkCurrentTimeTimeout_),t.cancelTimer_()}}return s.prototype.monitorCurrentTime_=function(){this.checkCurrentTime_(),this.checkCurrentTimeTimeout_&&g.clearTimeout(this.checkCurrentTimeTimeout_),this.checkCurrentTimeTimeout_=g.setTimeout(this.monitorCurrentTime_.bind(this),250)},s.prototype.checkCurrentTime_=function(){if(this.tech_.seeking()&&this.fixesBadSeeks_())return this.consecutiveUpdates=0,void(this.lastRecordedTime=this.tech_.currentTime());if(!this.tech_.paused()&&!this.tech_.seeking()){var e=this.tech_.currentTime(),t=this.tech_.buffered();if(this.lastRecordedTime===e&&(!t.length||e+.1>=t.end(t.length-1)))return this.techWaiting_();5<=this.consecutiveUpdates&&e===this.lastRecordedTime?(this.consecutiveUpdates++,this.waiting_()):e===this.lastRecordedTime?this.consecutiveUpdates++:(this.consecutiveUpdates=0,this.lastRecordedTime=e)}},s.prototype.cancelTimer_=function(){this.consecutiveUpdates=0,this.timer_&&(this.logger_("cancelTimer_"),clearTimeout(this.timer_)),this.timer_=null},s.prototype.fixesBadSeeks_=function(){var e=this.tech_.seeking(),t=this.seekable(),i=this.tech_.currentTime(),r=void 0;e&&this.afterSeekableWindow_(t,i)&&(r=t.end(t.length-1));e&&this.beforeSeekableWindow_(t,i)&&(r=t.start(0)+.1);return"undefined"!=typeof r&&(this.logger_("Trying to seek outside of seekable at time "+i+" with seekable range "+Gs(t)+". Seeking to "+r+"."),this.tech_.setCurrentTime(r),!0)},s.prototype.waiting_=function(){if(!this.techWaiting_()){var e=this.tech_.currentTime(),t=this.tech_.buffered(),i=Vs(t,e);return i.length&&e+3<=i.end(0)?(this.cancelTimer_(),this.tech_.setCurrentTime(e),this.logger_("Stopped at "+e+" while inside a buffered region ["+i.start(0)+" -> "+i.end(0)+"]. Attempting to resume playback by seeking to the current time."),void this.tech_.trigger({type:"usage",name:"hls-unknown-waiting"})):void 0}},s.prototype.techWaiting_=function(){var e=this.seekable(),t=this.tech_.currentTime();if(this.tech_.seeking()&&this.fixesBadSeeks_())return!0;if(this.tech_.seeking()||null!==this.timer_)return!0;if(this.beforeSeekableWindow_(e,t)){var i=e.end(e.length-1);return this.logger_("Fell out of live window at time "+t+". Seeking to live point (seekable end) "+i),this.cancelTimer_(),this.tech_.setCurrentTime(i),this.tech_.trigger({type:"usage",name:"hls-live-resync"}),!0}var r=this.tech_.buffered(),n=Ws(r,t);if(this.videoUnderflow_(n,r,t))return this.cancelTimer_(),this.tech_.setCurrentTime(t),this.tech_.trigger({type:"usage",name:"hls-video-underflow"}),!0;if(0<n.length){var a=n.start(0)-t;return this.logger_("Stopped at "+t+", setting timer for "+a+", seeking to "+n.start(0)),this.timer_=setTimeout(this.skipTheGap_.bind(this),1e3*a,t),!0}return!1},s.prototype.afterSeekableWindow_=function(e,t){return!!e.length&&t>e.end(e.length-1)+.1},s.prototype.beforeSeekableWindow_=function(e,t){return!!(e.length&&0<e.start(0)&&t<e.start(0)-.1)},s.prototype.videoUnderflow_=function(e,t,i){if(0===e.length){var r=this.gapFromVideoUnderflow_(t,i);if(r)return this.logger_("Encountered a gap in video from "+r.start+" to "+r.end+". Seeking to current time "+i),!0}return!1},s.prototype.skipTheGap_=function(e){var t=this.tech_.buffered(),i=this.tech_.currentTime(),r=Ws(t,i);this.cancelTimer_(),0!==r.length&&i===e&&(this.logger_("skipTheGap_:","currentTime:",i,"scheduled currentTime:",e,"nextRange start:",r.start(0)),this.tech_.setCurrentTime(r.start(0)+Hs),this.tech_.trigger({type:"usage",name:"hls-gap-skip"}))},s.prototype.gapFromVideoUnderflow_=function(e,t){for(var i=function(e){if(e.length<2)return en.createTimeRanges();for(var t=[],i=1;i<e.length;i++){var r=e.end(i-1),n=e.start(i);t.push([r,n])}return en.createTimeRanges(t)}(e),r=0;r<i.length;r++){var n=i.start(r),a=i.end(r);if(t-n<4&&2<t-n)return{start:n,end:a}}return null},s}(),gu={errorInterval:30,getSource:function(e){return e(this.tech({IWillNotUseThisInPlugins:!0}).currentSource_)}},yu=function(e){!function t(i,e){var r=0,n=0,a=en.mergeOptions(gu,e);i.ready(function(){i.trigger({type:"usage",name:"hls-error-reload-initialized"})});var s=function(){n&&i.currentTime(n)},o=function(e){null!=e&&(n=i.duration()!==1/0&&i.currentTime()||0,i.one("loadedmetadata",s),i.src(e),i.trigger({type:"usage",name:"hls-error-reload"}),i.play())},u=function(){if(Date.now()-r<1e3*a.errorInterval)i.trigger({type:"usage",name:"hls-error-reload-canceled"});else{if(a.getSource&&"function"==typeof a.getSource)return r=Date.now(),a.getSource.call(i,o);en.log.error("ERROR: reloadSourceOnError - The option getSource must be a function!")}},c=function e(){i.off("loadedmetadata",s),i.off("error",u),i.off("dispose",e)};i.on("error",u),i.on("dispose",c),i.reloadSourceOnError=function(e){c(),t(i,e)}}(this,e)},vu={PlaylistLoader:ms,Playlist:Is,Decrypter:ss,AsyncStream:rs,decrypt:as,utils:Fs,STANDARD_PLAYLIST_SELECTOR:function(){return function(e,t,i,r){var n=e.playlists.map(function(e){var t,i;return t=e.attributes.RESOLUTION&&e.attributes.RESOLUTION.width,i=e.attributes.RESOLUTION&&e.attributes.RESOLUTION.height,{bandwidth:e.attributes.BANDWIDTH||g.Number.MAX_VALUE,width:t,height:i,playlist:e}});Fo(n,function(e,t){return e.bandwidth-t.bandwidth});var a=(n=n.filter(function(e){return!Is.isIncompatible(e.playlist)})).filter(function(e){return Is.isEnabled(e.playlist)});a.length||(a=n.filter(function(e){return!Is.isDisabled(e.playlist)}));var s=a.filter(function(e){return e.bandwidth*ko.BANDWIDTH_VARIANCE<t}),o=s[s.length-1],u=s.filter(function(e){return e.bandwidth===o.bandwidth})[0],c=s.filter(function(e){return e.width&&e.height});Fo(c,function(e,t){return e.width-t.width});var l=c.filter(function(e){return e.width===i&&e.height===r});o=l[l.length-1];var d=l.filter(function(e){return e.bandwidth===o.bandwidth})[0],h=void 0,p=void 0,f=void 0;d||(p=(h=c.filter(function(e){return e.width>i||e.height>r})).filter(function(e){return e.width===h[0].width&&e.height===h[0].height}),o=p[p.length-1],f=p.filter(function(e){return e.bandwidth===o.bandwidth})[0]);var m=f||d||u||a[0]||n[0];return m?m.playlist:null}(this.playlists.master,this.systemBandwidth,parseInt(jo(this.tech_.el(),"width"),10),parseInt(jo(this.tech_.el(),"height"),10))},INITIAL_PLAYLIST_SELECTOR:function(){var e=this.playlists.master.playlists.filter(Is.isEnabled);return Fo(e,function(e,t){return Ho(e,t)}),e.filter(function(e){return No(e.attributes.CODECS).videoCodec})[0]||null},comparePlaylistBandwidth:Ho,comparePlaylistResolution:function(e,t){var i=void 0,r=void 0;return e.attributes.RESOLUTION&&e.attributes.RESOLUTION.width&&(i=e.attributes.RESOLUTION.width),i=i||g.Number.MAX_VALUE,t.attributes.RESOLUTION&&t.attributes.RESOLUTION.width&&(r=t.attributes.RESOLUTION.width),i===(r=r||g.Number.MAX_VALUE)&&e.attributes.BANDWIDTH&&t.attributes.BANDWIDTH?e.attributes.BANDWIDTH-t.attributes.BANDWIDTH:i-r},xhr:Ds()};["GOAL_BUFFER_LENGTH","MAX_GOAL_BUFFER_LENGTH","GOAL_BUFFER_LENGTH_RATE","BUFFER_LOW_WATER_LINE","MAX_BUFFER_LOW_WATER_LINE","BUFFER_LOW_WATER_LINE_RATE","BANDWIDTH_VARIANCE"].forEach(function(t){Object.defineProperty(vu,t,{get:function(){return en.log.warn("using Hls."+t+" is UNSAFE be sure you know what you are doing"),ko[t]},set:function(e){en.log.warn("using Hls."+t+" is UNSAFE be sure you know what you are doing"),"number"!=typeof e||e<0?en.log.warn("value of Hls."+t+" must be greater than or equal to 0"):ko[t]=e}})});var _u=function(e){if(/^(audio|video|application)\/(x-|vnd\.apple\.)?mpegurl/i.test(e))return"hls";return/^application\/dash\+xml/i.test(e)?"dash":null},bu=function(e,t){for(var i=t.media(),r=-1,n=0;n<e.length;n++)if(e[n].id===i.uri){r=n;break}e.selectedIndex_=r,e.trigger({selectedIndex:r,type:"change"})};vu.canPlaySource=function(){return en.log.warn("HLS is no longer a tech. Please remove it from your player's techOrder.")};var Tu=function(e){if("dash"===e.options_.sourceType){var t=en.players[e.tech_.options_.playerId];if(t.eme){var i=function(e,t,i){if(!e)return e;var r={};for(var n in e)r[n]={audioContentType:'audio/mp4; codecs="'+i.attributes.CODECS+'"',videoContentType:'video/mp4; codecs="'+t.attributes.CODECS+'"'},t.contentProtection&&t.contentProtection[n]&&t.contentProtection[n].pssh&&(r[n].pssh=t.contentProtection[n].pssh),"string"==typeof e[n]&&(r[n].url=e[n]);return en.mergeOptions(e,r)}(e.source_.keySystems,e.playlists.media(),e.masterPlaylistController_.mediaTypes_.AUDIO.activePlaylistLoader.media());i&&(t.currentSource().keySystems=i)}}};vu.supportsNativeHls=function(){var t=p.createElement("video");if(!en.getTech("Html5").isSupported())return!1;return["application/vnd.apple.mpegurl","audio/mpegurl","audio/x-mpegurl","application/x-mpegurl","video/x-mpegurl","video/mpegurl","application/mpegurl"].some(function(e){return/maybe|probably/i.test(t.canPlayType(e))})}(),vu.supportsNativeDash=!!en.getTech("Html5").isSupported()&&/maybe|probably/i.test(p.createElement("video").canPlayType("application/dash+xml")),vu.supportsTypeNatively=function(e){return"hls"===e?vu.supportsNativeHls:"dash"===e&&vu.supportsNativeDash},vu.isSupported=function(){return en.log.warn("HLS is no longer a tech. Please remove it from your player's techOrder.")};var Su=function(a){function s(e,t,i){y(this,s);var r=b(this,a.call(this,t,i.hls));if(t.options_&&t.options_.playerId){var n=en(t.options_.playerId);n.hasOwnProperty("hls")||Object.defineProperty(n,"hls",{get:function(){return en.log.warn("player.hls is deprecated. Use player.tech_.hls instead."),t.trigger({type:"usage",name:"hls-player-access"}),r}}),n.vhs=r,n.dash=r}if(r.tech_=t,r.source_=e,r.stats={},r.ignoreNextSeekingEvent_=!1,r.setOptions_(),r.options_.overrideNative&&(t.featuresNativeVideoTracks||t.featuresNativeAudioTracks))throw new Error("Overriding native HLS requires emulated tracks. See https://git.io/vMpjB");return r.on(p,["fullscreenchange","webkitfullscreenchange","mozfullscreenchange","MSFullscreenChange"],function(e){var t=p.fullscreenElement||p.webkitFullscreenElement||p.mozFullScreenElement||p.msFullscreenElement;t&&t.contains(r.tech_.el())&&r.masterPlaylistController_.fastQualityChange_()}),r.on(r.tech_,"seeking",function(){this.ignoreNextSeekingEvent_?this.ignoreNextSeekingEvent_=!1:this.setCurrentTime(this.tech_.currentTime())}),r.on(r.tech_,"error",function(){this.masterPlaylistController_&&this.masterPlaylistController_.pauseLoading()}),r.on(r.tech_,"play",r.play),r}return _(s,a),s.prototype.setOptions_=function(){var t=this;this.options_.withCredentials=this.options_.withCredentials||!1,"number"!=typeof this.options_.blacklistDuration&&(this.options_.blacklistDuration=300),"number"!=typeof this.options_.bandwidth&&(this.options_.bandwidth=4194304),this.options_.enableLowInitialPlaylist=this.options_.enableLowInitialPlaylist&&4194304===this.options_.bandwidth,["withCredentials","bandwidth"].forEach(function(e){"undefined"!=typeof t.source_[e]&&(t.options_[e]=t.source_[e])}),this.bandwidth=this.options_.bandwidth},s.prototype.src=function(e,t){var r=this;e&&(this.setOptions_(),this.options_.url=this.source_.src,this.options_.tech=this.tech_,this.options_.externHls=vu,this.options_.sourceType=_u(t),this.masterPlaylistController_=new hu(this.options_),this.playbackWatcher_=new mu(en.mergeOptions(this.options_,{seekable:function(){return r.seekable()}})),this.masterPlaylistController_.on("error",function(){en.players[r.tech_.options_.playerId].error(r.masterPlaylistController_.error)}),this.masterPlaylistController_.selectPlaylist=this.selectPlaylist?this.selectPlaylist.bind(this):vu.STANDARD_PLAYLIST_SELECTOR.bind(this),this.masterPlaylistController_.selectInitialPlaylist=vu.INITIAL_PLAYLIST_SELECTOR.bind(this),this.playlists=this.masterPlaylistController_.masterPlaylistLoader_,this.mediaSource=this.masterPlaylistController_.mediaSource,Object.defineProperties(this,{selectPlaylist:{get:function(){return this.masterPlaylistController_.selectPlaylist},set:function(e){this.masterPlaylistController_.selectPlaylist=e.bind(this)}},throughput:{get:function(){return this.masterPlaylistController_.mainSegmentLoader_.throughput.rate},set:function(e){this.masterPlaylistController_.mainSegmentLoader_.throughput.rate=e,this.masterPlaylistController_.mainSegmentLoader_.throughput.count=1}},bandwidth:{get:function(){return this.masterPlaylistController_.mainSegmentLoader_.bandwidth},set:function(e){this.masterPlaylistController_.mainSegmentLoader_.bandwidth=e,this.masterPlaylistController_.mainSegmentLoader_.throughput={rate:0,count:0}}},systemBandwidth:{get:function(){var e=1/(this.bandwidth||1),t=void 0;return t=0<this.throughput?1/this.throughput:0,Math.floor(1/(e+t))},set:function(){en.log.error('The "systemBandwidth" property is read-only')}}}),Object.defineProperties(this.stats,{bandwidth:{get:function(){return r.bandwidth||0},enumerable:!0},mediaRequests:{get:function(){return r.masterPlaylistController_.mediaRequests_()||0},enumerable:!0},mediaRequestsAborted:{get:function(){return r.masterPlaylistController_.mediaRequestsAborted_()||0},enumerable:!0},mediaRequestsTimedout:{get:function(){return r.masterPlaylistController_.mediaRequestsTimedout_()||0},enumerable:!0},mediaRequestsErrored:{get:function(){return r.masterPlaylistController_.mediaRequestsErrored_()||0},enumerable:!0},mediaTransferDuration:{get:function(){return r.masterPlaylistController_.mediaTransferDuration_()||0},enumerable:!0},mediaBytesTransferred:{get:function(){return r.masterPlaylistController_.mediaBytesTransferred_()||0},enumerable:!0},mediaSecondsLoaded:{get:function(){return r.masterPlaylistController_.mediaSecondsLoaded_()||0},enumerable:!0},buffered:{get:function(){return zs(r.tech_.buffered())},enumerable:!0},currentTime:{get:function(){return r.tech_.currentTime()},enumerable:!0},currentSource:{get:function(){return r.tech_.currentSource_},enumerable:!0},currentTech:{get:function(){return r.tech_.name_},enumerable:!0},duration:{get:function(){return r.tech_.duration()},enumerable:!0},master:{get:function(){return r.playlists.master},enumerable:!0},playerDimensions:{get:function(){return r.tech_.currentDimensions()},enumerable:!0},seekable:{get:function(){return zs(r.tech_.seekable())},enumerable:!0},timestamp:{get:function(){return Date.now()},enumerable:!0},videoPlaybackQuality:{get:function(){return r.tech_.getVideoPlaybackQuality()},enumerable:!0}}),this.tech_.one("canplay",this.masterPlaylistController_.setupFirstPlay.bind(this.masterPlaylistController_)),this.masterPlaylistController_.on("selectedinitialmedia",function(){var i,e;e=(i=r).playlists,i.representations=function(){return e.master.playlists.filter(function(e){return!As(e)}).map(function(e,t){return new pu(i,e,e.uri)})},Tu(r)}),this.on(this.masterPlaylistController_,"progress",function(){this.tech_.trigger("progress")}),this.on(this.masterPlaylistController_,"firstplay",function(){this.ignoreNextSeekingEvent_=!0}),this.tech_.ready(function(){return r.setupQualityLevels_()}),this.tech_.el()&&this.tech_.src(en.URL.createObjectURL(this.masterPlaylistController_.mediaSource)))},s.prototype.setupQualityLevels_=function(){var i=this,e=en.players[this.tech_.options_.playerId];e&&e.qualityLevels&&(this.qualityLevels_=e.qualityLevels(),this.masterPlaylistController_.on("selectedinitialmedia",function(){var t,e;t=i.qualityLevels_,(e=i).representations().forEach(function(e){t.addQualityLevel(e)}),bu(t,e.playlists)}),this.playlists.on("mediachange",function(){bu(i.qualityLevels_,i.playlists)}))},s.prototype.play=function(){this.masterPlaylistController_.play()},s.prototype.setCurrentTime=function(e){this.masterPlaylistController_.setCurrentTime(e)},s.prototype.duration=function(){return this.masterPlaylistController_.duration()},s.prototype.seekable=function(){return this.masterPlaylistController_.seekable()},s.prototype.dispose=function(){this.playbackWatcher_&&this.playbackWatcher_.dispose(),this.masterPlaylistController_&&this.masterPlaylistController_.dispose(),this.qualityLevels_&&this.qualityLevels_.dispose(),a.prototype.dispose.call(this)},s}(en.getComponent("Component")),Eu={name:"videojs-http-streaming",VERSION:"1.0.0",canHandleSource:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},i=en.mergeOptions(en.options,t);return Eu.canPlayType(e.type,i)},handleSource:function(e,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},r=en.mergeOptions(en.options,i);return t.hls=new Su(e,t,r),t.hls.xhr=Ds(),t.hls.src(e.src,e.type),t.hls},canPlayType:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},i=en.mergeOptions(en.options,t).hls.overrideNative,r=_u(e);return r&&(!vu.supportsTypeNatively(r)||i)?"maybe":""}};return"undefined"!=typeof en.MediaSource&&"undefined"!=typeof en.URL||(en.MediaSource=yo,en.URL=vo),yo.supportsNativeMediaSources()&&en.getTech("Html5").registerSourceHandler(Eu,0),en.HlsHandler=Su,en.HlsSourceHandler=Eu,en.Hls=vu,en.use||en.registerComponent("Hls",vu),en.options.hls=en.options.hls||{},en.registerPlugin?en.registerPlugin("reloadSourceOnError",yu):en.plugin("reloadSourceOnError",yu),en});