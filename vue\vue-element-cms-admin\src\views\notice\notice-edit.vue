<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{ pageTitle ? pageTitle : '添加公告' }} </span>
      </div>
      <el-form ref="form" v-loading="formLoading" :model="form" :rules="formRules" label-width="100px">
        <el-form-item label="公告标题" prop="title">
          <el-input v-model="form.title" />
        </el-form-item>

        <el-form-item label="封面图" prop="imgUrl">
          <lz-upload-images
            ref="previewFile"
            :limit="1"
            :file-size="500"
            :file-type="['jpg', 'png', 'jpeg']"
            :source-list="previewFileList"
            @response-fn="handleResponse"
            @remove-upload="handleRemoveUploadFile"
          />
        </el-form-item>
        <el-form-item label="公告时间" prop="publishDate">
          <el-date-picker
            v-model="form.publishDate"
            type="datetime"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
            placeholder="选择日期时间"
          />
        </el-form-item>
        <el-form-item label="置顶" prop="order">
          <el-radio-group v-model="form.order">
            <el-radio :label="0">否</el-radio>
            <el-radio :label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="公告分类" prop="category">
          <el-radio-group v-model="form.category">
            <el-radio :label="0">标准解读</el-radio>
            <el-radio :label="1">行业咨询</el-radio>
            <el-radio :label="2">培训资讯</el-radio>
            <el-radio :label="3">课程资讯</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否PDF" prop="isPdf">
          <el-radio-group v-model="form.isPdf" @change="handleIsPdfChange">
            <el-radio :label="0">否</el-radio>
            <el-radio :label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-show="!!form.isPdf" label="上传PDF" prop="pdfUrl" style="width: 600px;">
          <lz-upload-file class="uplaod_file" :multiple="false" :file-size="500" :is-public="false" :is-show-tip="true" :file-type="uploadFileType" :file-list="uploadResourceList" :btn-title="'上传资源'" @response-fn="handleFileResponsePdf" @remove-upload="handleRemoveUploadFilePdf" />
        </el-form-item>
        <el-form-item v-show="!form.isPdf" label="公告内容" prop="content">
          <tinymce v-show="showContent" id="tinymce" v-model="form.content" :value="form.content" :height="400" :width="700" />
        </el-form-item>
        <el-form-item label="">
          <el-button type="primary" round icon="el-icon-check" @click="handleSaveNoticeEdit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>
<script>
import tinymce from '@/components/Tinymce'
import permission from '@/directive/permission'
import LzUploadImages from '@/components/LzUploadImages'
import LzUploadFile from '@/components/LzUploadFile'
import { noticeAdd, noticeDetail, noticeEdit } from '@/api/other'
import { parseTimeDate } from '@/utils'
export default {
  name: 'NoticeEdit',
  directives: {
    permission
  },
  components: {
    tinymce,
    LzUploadImages,
    LzUploadFile
  },
  data() {
    return {
      pageTitle: this.$route.query.name,
      form: {
        title: '',
        publishDate: '',
        order: 0,
        content: '',
        type: 1,
        category: 0,
        imgUrl: '',
        isPdf: 0,
        pdfUrl: '',
        pdfName: '',
        announcementUsers: null,
        announcementAttachments: null
      },
      // 上传PDF
      uploadResourceList: [],
      uploadFileType: ['pdf'],

      previewFileList: [],
      formLoading: false,
      formRules: {
        title: [{
          required: true,
          message: '请输入公告标题',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 100,
          message: '长度在 1 到 100 个字符',
          trigger: 'blur'
        }
        ],
        publishDate: [{
          required: true,
          message: '请输入公告日期',
          trigger: 'blur'
        }],
        content: [{
          validator: (rule, value, callback) => {
            if (!this.form.isPdf) {
              if (!value || !String(value).trim()) {
                callback(new Error('请输入公告内容'))
              } else if (String(value).length > 5000) {
                callback(new Error('长度在 1 到 5000 个字符'))
              } else {
                callback()
              }
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }],
        pdfUrl: [{
          validator: (rule, value, callback) => {
            if (this.form && this.form.isPdf) {
              // console.log('pdfUrl', value)
              if (!value) {
                return callback(new Error('请上传PDF'))
              }
            }
            callback()
          },
          trigger: 'change'
        }]
      },
      showContent: false

    }
  },
  mounted() {
    this.uploadResourceList = []
    if (this.$route.query.id) {
      this.getNoticeDetial()
    } else {
      this.showContent = true
    }
  },
  methods: {
    handleSaveNoticeEdit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true
          if (this.form.isPdf === 0) {
            this.announcementAttachments = null
          } else {
            this.form.content = ''
          }
          if (this.$route.query.id) {
            noticeEdit(this.$route.query.id, this.form).then(res => {
              this.$message.success('编辑成功')
              this.formLoading = false
            }).catch(() => {
              this.$message.error('编辑失败')
              this.formLoading = false
            })
          } else {
            noticeAdd(this.form).then(res => {
              this.$message.success('添加成功')
              this.formLoading = false
              this.$router.go(-1)
            }).catch(() => {
              this.$message.error('添加失败')
              this.formLoading = false
            })
          }
        } else {
          return false
        }
      })
    },
    handleIsPdfChange(val) {
      this.$nextTick(() => {
        if (val) {
          // 切换到“是”：需要上传PDF
          this.$refs.form.clearValidate(['content'])
          this.$refs.form.validateField('pdfUrl')
        } else {
          // 切换到“否”：需要填写公告内容
          this.$refs.form.clearValidate(['pdfUrl'])
          this.$refs.form.validateField('content')
        }
      })
    },
    getNoticeDetial() {
      noticeDetail(this.$route.query.id).then(res => {
        this.form.title = res.announcement.title
        this.form.content = res.announcement.content
        this.form.publishDate = parseTimeDate(res.announcement.publishDate)
        this.form.category = res.announcement.category
        this.form.order = res.announcement.order
        this.form.imgUrl = res.announcement.imgUrl
        if (this.form.imgUrl && this.form.imgUrl.length) {
          this.previewFileList = [{
            url: this.form.imgUrl
          }]
        }
        // 回显是否为 PDF 及 PDF 地址
        this.form.isPdf = res.announcement.isPdf
        if (res.announcement.pdfUrl) {
          // this.form.pdfUrl = res.announcement.pdfUrl
          // this.form.pdfName = res.announcement.pdfName
          // this.uploadResourceList = [{ url: this.form.pdfUrl, name: this.form.pdfName, fileType: '.pdf' }]
          this.uploadResourceList = res.announcement.announcementAttachments
        }
        this.showContent = true
      })
    },
    handleResponse(url, fileForm) {
      this.previewFileList.push(fileForm)
      this.form.imgUrl = url
    },
    handleRemoveUploadFile(index) {
      this.previewFileList = []
      this.form.imgUrl = ''
    },
    handleFileResponsePdf(url, fileForm) {
      this.uploadResourceList = []
      this.form.pdfUrl = url
      this.form.pdfName = fileForm.fileName
      this.uploadResourceList.push({
        url: url,
        localUrl: fileForm.localUrl,
        fileName: fileForm.fileName,
        name: fileForm.fileName,
        hash: fileForm.hash,
        extend: fileForm.fileType,
        size: fileForm.size,
        fileType: fileForm.fileType,
        resType: fileForm.resType,
        tranStatus: fileForm.tranStatus,
        documentId: fileForm.documentId,
        jobId: fileForm.jobId,
        durationInSecond: fileForm.durationInSecond
      })
      this.announcementAttachments = this.uploadResourceList
      // console.log(this.announcementAttachments)
      
    },
    handleRemoveUploadFilePdf(file) {
      this.form.pdfUrl = ''
      this.form.pdfName = ''
      this.uploadResourceList = []
      this.announcementAttachments = []
      // var index = this.uploadResourceList.indexOf(file)
      // if (index > -1) {
      //   this.uploadResourceList.splice(index, 1)
      // }
    }
  }

}

</script>

