<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{ pageTitle ? pageTitle : '添加企业岗位信息' }} </span>
      </div>
      <el-form ref="form" v-loading="formLoading" :model="form" :rules="formRules" label-width="100px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" />
        </el-form-item>
        <!-- <el-form-item label="封面图" prop="imgUrl">
          <lz-upload-images
            ref="previewFile"
            :limit="1"
            :file-size="500"
            :file-type="['jpg', 'png', 'jpeg']"
            :source-list="previewFileList"
            @response-fn="handleResponse"
            @remove-upload="handleRemoveUploadFile"
          />
        </el-form-item> -->
        <el-form-item label="薪资" prop="salary">
          <el-input v-model="form.salary" />
        </el-form-item>
        <el-form-item label="经验" prop="experience">
          <el-input v-model="form.experience" />
        </el-form-item>
        <el-form-item label="学历" prop="qualifications">
          <el-input v-model="form.qualifications" />
        </el-form-item>
        <el-form-item label="公司" prop="company">
          <el-input v-model="form.company" />
        </el-form-item>
        <el-form-item label="所在地区" prop="location">
          <el-input v-model="form.location" />
        </el-form-item>

        <el-form-item label="发布时间" prop="publishDate">
          <el-date-picker
            v-model="form.publishDate"
            type="datetime"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
            placeholder="选择日期时间"
          />
        </el-form-item>
        <!-- <el-form-item label="置顶" prop="order">
          <el-radio-group v-model="form.order">
            <el-radio :label="0">否</el-radio>
            <el-radio :label="1">是</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :step="1" :min="1" />
        </el-form-item>
        <!-- <el-form-item label="公告分类" prop="category">
          <el-radio-group v-model="form.category">
            <el-radio :label="0">标准解读</el-radio>
            <el-radio :label="1">行业咨询</el-radio>
            <el-radio :label="2">培训资讯</el-radio>
            <el-radio :label="3">课程资讯</el-radio>
          </el-radio-group>
        </el-form-item> -->

        <el-form-item label="内容" prop="content">
          <tinymce v-show="showContent" id="tinymce" v-model="form.content" :value="form.content" :height="400" :width="700" />
        </el-form-item>
        <el-form-item label="">
          <el-button type="primary" round icon="el-icon-check" @click="handleSavePositionsEdit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>
<script>
import tinymce from '@/components/Tinymce'
import permission from '@/directive/permission'
// import LzUploadImages from '@/components/LzUploadImages'
import { positionsAdd, positionsDetail, positionsEdit } from '@/api/other'
import { parseTimeDate } from '@/utils'
export default {
  name: 'PositionsEdit',
  directives: {
    permission
  },
  components: {
    tinymce
    // LzUploadImages
  },
  data() {
    return {
      pageTitle: this.$route.query.name,
      form: {
        title: '',
        publishDate: '',
        sort: 0,
        content: '',
        // imgUrl: '',
        salary: '',
        experience: '',
        qualifications: '',
        company: '',
        location: ''
      },
      previewFileList: [],
      formLoading: false,
      formRules: {
        title: [{
          required: true,
          message: '请输入标题',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 100,
          message: '长度在 1 到 100 个字符',
          trigger: 'blur'
        }
        ],
        salary: [{
          required: true,
          message: '请输入薪资',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 20,
          message: '长度在 1 到 20 个字符',
          trigger: 'blur'
        }
        ],
        experience: [{
          required: true,
          message: '请输入经验',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 20,
          message: '长度在 1 到 20 个字符',
          trigger: 'blur'
        }
        ],
        qualifications: [{
          required: true,
          message: '请输入学历',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 20,
          message: '长度在 1 到 20 个字符',
          trigger: 'blur'
        }
        ],
        company: [{
          required: true,
          message: '请输入公司',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 20,
          message: '长度在 1 到 20 个字符',
          trigger: 'blur'
        }
        ],
        location: [{
          required: true,
          message: '请输入所在地区',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 20,
          message: '长度在 1 到 20 个字符',
          trigger: 'blur'
        }
        ],

        publishDate: [{
          required: true,
          message: '请输入发布日期',
          trigger: 'blur'
        }],
        content: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        },
        {
          min: 1,
          max: 5000,
          message: '长度在 1 到 5000 个字符',
          trigger: 'blur'
        }
        ]
      },
      showContent: false

    }
  },
  mounted() {
    if (this.$route.query.id) {
      this.getPositionsDetial()
    } else {
      this.showContent = true
    }
  },
  methods: {
    handleSavePositionsEdit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true
          if (this.$route.query.id) {
            positionsEdit(this.$route.query.id, this.form).then(res => {
              this.$message.success('编辑成功')
              this.formLoading = false
            }).catch(() => {
              this.$message.error('编辑失败')
              this.formLoading = false
            })
          } else {
            positionsAdd(this.form).then(res => {
              this.$message.success('添加成功')
              this.formLoading = false
              this.$router.go(-1)
            }).catch(() => {
              this.$message.error('添加失败')
              this.formLoading = false
            })
          }
        } else {
          return false
        }
      })
    },
    getPositionsDetial() {
      positionsDetail(this.$route.query.id).then(res => {
        this.form.title = res.title
        this.form.content = res.content
        this.form.publishDate = parseTimeDate(res.publishDate)
        this.form.sort = res.sort
        this.form.salary = res.salary
        this.form.experience = res.experience
        this.form.qualifications = res.qualifications
        this.form.company = res.company
        this.form.location = res.location
        // this.form.imgUrl = res.imgUrl
        // if (this.form.imgUrl && this.form.imgUrl.length) {
        //   this.previewFileList = [{
        //     url: this.form.imgUrl
        //   }]
        // }
        this.showContent = true
        console.log(res.content)
      })
    }
    // handleResponse(url, fileForm) {
    //   this.previewFileList.push(fileForm)
    //   this.form.imgUrl = url
    // },
    // handleRemoveUploadFile(index) {
    //   this.previewFileList = []
    //   this.form.imgUrl = ''
    // }
  }

}

</script>

