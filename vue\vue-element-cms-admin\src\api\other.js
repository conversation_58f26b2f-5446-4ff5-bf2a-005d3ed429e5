/*
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-07-05 13:09:10
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-07-22 14:23:12
 * @FilePath: /vue-element-cms-admin/src/api/other.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import axios from '@/axios'

export function noticeList(data) {
  return axios.gets('/api/notice/announcements', data)
}

export function noticeDetail(id) {
  return axios.gets(`/api/notice/announcements/${id}`)
}

export function noticeAdd(data) {
  return axios.posts('/api/notice/announcements', data)
}

export function noticeEdit(id, data) {
  return axios.puts(`/api/notice/announcements/${id}`, data)
}

export function noticePublish(id) {
  return axios.puts(`/api/notice/announcements/publish/${id}`)
}

export function noticeDelete(id) {
  return axios.deletes(`/api/notice/announcements/${id}`)
}

export function technologyCentersList(data) {
  return axios.gets('/api/notice/TechnologyCenters', data)
}

export function technologyCentersDetail(id) {
  return axios.gets(`/api/notice/TechnologyCenters/${id}`)
}

export function technologyCentersAdd(data) {
  return axios.posts('/api/notice/TechnologyCenters', data)
}

export function technologyCentersEdit(id, data) {
  return axios.puts(`/api/notice/TechnologyCenters/${id}`, data)
}

export function technologyCentersPublish(id) {
  return axios.puts(`/api/notice/TechnologyCenters/publish/${id}`)
}

export function technologyCentersDelete(id) {
  return axios.deletes(`/api/notice/TechnologyCenters/${id}`)
}

export function resumesList(data) {
  return axios.gets('/api/notice/Resumes', data)
}

export function resumesDetail(id) {
  return axios.gets(`/api/notice/Resumes/${id}`)
}

export function resumesAdd(data) {
  return axios.posts('/api/notice/Resumes', data)
}

export function resumesEdit(id, data) {
  return axios.puts(`/api/notice/Resumes/${id}`, data)
}

export function resumesPublish(id) {
  return axios.puts(`/api/notice/Resumes/publish/${id}`)
}

export function resumesDelete(id) {
  return axios.deletes(`/api/notice/Resumes/${id}`)
}

export function positionsList(data) {
  return axios.gets('/api/notice/Positions', data)
}

export function positionsDetail(id) {
  return axios.gets(`/api/notice/Positions/${id}`)
}

export function positionsAdd(data) {
  return axios.posts('/api/notice/Positions', data)
}

export function positionsEdit(id, data) {
  return axios.puts(`/api/notice/Positions/${id}`, data)
}

export function positionsPublish(id) {
  return axios.puts(`/api/notice/Positions/publish/${id}`)
}

export function positionsDelete(id) {
  return axios.deletes(`/api/notice/Positions/${id}`)
}

export function linkList(data) {
  return axios.gets('/api/cms/virtualTrainings', data)
}

export function linkAdd(data) {
  return axios.posts('/api/cms/virtualTrainings', data)
}

export function linkEdit(id, data) {
  return axios.puts(`/api/cms/virtualTrainings/${id}`, data)
}

export function linkDelete(id) {
  return axios.deletes(`/api/cms/virtualTrainings/${id}`)
}

export function courseReport(data) {
  return axios.gets('/api/cms/course/center/report', data)
}

export function liveReport(data) {
  return axios.gets('/api/lms/lives/users/report', data)
}
